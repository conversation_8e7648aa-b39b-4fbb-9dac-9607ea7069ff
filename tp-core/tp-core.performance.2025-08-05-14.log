com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 140217,main
com.pay.tp.core.configuration.AliOss.ossClient,time=114,20250805 140219,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=36,20250805 140220,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=4,20250805 140220,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=7,20250805 140220,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 140220,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 140222,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 140222,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 140222,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 140222,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 140222,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 140222,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 140222,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 140222,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 140222,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=214,20250805 140225,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=217,20250805 140225,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=284,20250805 140225,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=284,20250805 140225,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=26934,20250805 140252,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=84,20250805 140252,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=84,20250805 140252,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=27604,20250805 140252,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=27605,20250805 140252,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 140401,main
com.pay.tp.core.configuration.AliOss.ossClient,time=110,20250805 140403,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=28,20250805 140404,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 140404,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 140404,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=3,20250805 140404,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 140406,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 140406,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 140406,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 140406,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 140406,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 140406,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 140406,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 140406,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=5,20250805 140406,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=223,20250805 140409,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=227,20250805 140409,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=22,20250805 140409,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=22,20250805 140409,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=12693,20250805 140421,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=17,20250805 140421,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=17,20250805 140421,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=13022,20250805 140421,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=13023,20250805 140421,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 142527,main
com.pay.tp.core.configuration.AliOss.ossClient,time=6,20250805 142529,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 142832,main
com.pay.tp.core.configuration.AliOss.ossClient,time=137,20250805 142834,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=32,20250805 142835,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 142835,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 142835,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=3,20250805 142835,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=4,20250805 142837,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 142837,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 142837,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 142837,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 142837,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 142837,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 142837,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 142837,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 142837,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=7,20250805 142837,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=88,20250805 142840,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=92,20250805 142840,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=146,20250805 142840,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=147,20250805 142840,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 142941,main
com.pay.tp.core.configuration.AliOss.ossClient,time=113,20250805 142943,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=44,20250805 142944,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 142944,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=6,20250805 142944,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=3,20250805 142944,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 142946,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 142946,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 142946,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 142946,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 142946,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 142946,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 142946,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=5,20250805 142946,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 142946,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=6,20250805 142946,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=186,20250805 142949,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=190,20250805 142949,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=22,20250805 142949,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=22,20250805 142949,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=12987,20250805 143002,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=25,20250805 143002,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=25,20250805 143002,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=13268,20250805 143002,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=13269,20250805 143002,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 143054,main
com.pay.tp.core.configuration.AliOss.ossClient,time=110,20250805 143056,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=40,20250805 143056,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 143056,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250805 143056,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 143057,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 143057,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=2,20250805 143058,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 143058,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 143058,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 143058,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 143058,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 143058,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 143058,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 143058,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=2,20250805 143058,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=209,20250805 143101,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=212,20250805 143101,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=19,20250805 143101,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=20,20250805 143101,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=189094,20250805 143411,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=41,20250805 143433,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=44,20250805 143433,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=212594,20250805 143433,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=212595,20250805 143433,main
