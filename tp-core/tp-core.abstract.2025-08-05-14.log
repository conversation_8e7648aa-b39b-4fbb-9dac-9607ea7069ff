0805 140217 995 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 140219 891 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=114
0805 140220 123 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=36
0805 140220 602 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=4
0805 140220 615 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=7
0805 140220 834 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 140222 326 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 140222 326 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 140222 326 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 140222 326 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 140222 327 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 140222 327 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 140222 327 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 140222 327 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 140222 357 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 140225 414 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=214
0805 140225 414 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=217
0805 140225 698 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=284
0805 140225 698 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=284
0805 140252 634 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=26934
0805 140252 723 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=84
0805 140252 723 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=84
0805 140252 792 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=27604
0805 140252 792 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=27605
0805 140401 452 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 140403 858 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=110
0805 140404 041 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=28
0805 140404 490 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 140404 499 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 140404 707 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=3
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 140406 175 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 140406 208 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=5
0805 140409 135 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=223
0805 140409 136 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=227
0805 140409 158 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=22
0805 140409 158 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=22
0805 140421 854 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=12693
0805 140421 874 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=17
0805 140421 874 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=17
0805 140421 922 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=13022
0805 140421 922 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=13023
0805 142527 208 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 142529 075 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=6
0805 142832 966 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 142834 921 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=137
0805 142835 101 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=32
0805 142835 607 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 142835 615 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 142835 787 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=3
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=4
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 142837 344 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 142837 375 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 142837 392 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=7
0805 142840 310 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=88
0805 142840 311 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=92
0805 142840 357 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=146
0805 142840 357 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=147
0805 142941 696 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 142943 835 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=113
0805 142944 056 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=44
0805 142944 522 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 142944 531 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=6
0805 142944 715 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=3
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 142946 229 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=5
0805 142946 256 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 142946 270 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=6
0805 142949 310 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=186
0805 142949 311 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=190
0805 142949 333 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=22
0805 142949 333 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=22
0805 143002 322 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=12987
0805 143002 349 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=25
0805 143002 349 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=25
0805 143002 380 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=13268
0805 143002 380 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=13269
0805 143054 249 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 143056 188 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=110
0805 143056 351 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=40
0805 143056 821 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 143056 828 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0805 143057 025 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 143057 242 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 143058 525 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=2
0805 143058 526 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 143058 526 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 143058 526 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 143058 526 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 143058 526 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 143058 526 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 143058 526 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 143058 554 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=2
0805 143101 625 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=209
0805 143101 625 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=212
0805 143101 645 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=19
0805 143101 645 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=20
0805 143411 001 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=189094
0805 143433 977 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=41
0805 143433 978 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=44
0805 143433 999 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=212594
0805 143433 999 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=212595
