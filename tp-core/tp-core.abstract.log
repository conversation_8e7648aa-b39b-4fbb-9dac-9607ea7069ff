0805 160325 370 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 160327 702 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=136
0805 160327 878 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=40
0805 160328 415 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 160328 424 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 160328 600 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 160328 838 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 160330 576 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 160330 576 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 160330 576 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 160330 576 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 160330 576 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 160330 576 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 160330 577 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 160330 577 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=7
0805 160330 609 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 160333 576 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=206
0805 160333 576 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=209
0805 160333 609 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=33
0805 160333 609 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=33
0805 160402 968 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=29356
0805 160512 480 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=64
0805 160512 481 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=65
0805 160512 516 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=99157
0805 160512 517 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=99159
0805 160645 738 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 160648 147 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=115
0805 160648 291 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=32
0805 160648 757 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 160648 764 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0805 160648 937 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 160649 157 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 160650 555 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 160650 588 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 160653 519 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=89
0805 160653 519 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=92
0805 160653 592 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=174
0805 160653 592 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=175
0805 160726 238 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=6
0805 160728 153 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=113
0805 160728 348 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=52
0805 160728 810 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 160728 817 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0805 160729 003 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 160729 237 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=2
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 160731 268 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=5
0805 160731 299 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=4
0805 160734 446 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=190
0805 160734 446 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=193
0805 160734 485 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=39
0805 160734 485 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=39
0805 160737 199 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=2711
0805 160749 366 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=21
0805 160749 366 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=21
0805 160749 395 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=15152
0805 160749 395 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=15153
0805 160933 940 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 160935 891 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=118
0805 160936 040 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=31
0805 160936 565 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=5
0805 160936 576 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=8
0805 160936 796 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=3
0805 160937 027 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 160938 390 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=2
0805 160938 390 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 160938 390 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 160938 391 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=1
0805 160938 391 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 160938 391 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 160938 391 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 160938 391 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 160938 425 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=5
0805 160941 436 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=228
0805 160941 438 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=233
0805 160941 471 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=32
0805 160941 471 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=33
0805 160941 610 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=136
0805 160948 627 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=21
0805 160948 627 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=21
0805 160948 650 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=7452
0805 160948 651 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=7454
0805 160956 628 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 160958 514 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=114
0805 160958 664 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=32
0805 160959 145 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 160959 153 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 160959 327 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 160959 557 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=6
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 161000 817 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=5
0805 161000 850 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=2
0805 161003 866 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=179
0805 161003 866 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=182
0805 161003 887 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=20
0805 161003 887 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=20
0805 161004 023 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=133
0805 161024 526 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=20850
0805 161024 526 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=20851
0805 161030 917 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=3
0805 161032 760 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=105
0805 161032 940 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=36
0805 161033 438 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 161033 446 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0805 161033 638 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=3
0805 161033 864 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=6
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=4
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 161035 618 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 161035 648 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 161038 607 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=85
0805 161038 607 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=88
0805 161038 694 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=182
0805 161038 694 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=183
0805 161252 887 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 161254 782 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=106
0805 161254 950 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=41
0805 161255 417 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 161255 425 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 161255 606 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 161255 831 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 161257 041 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 161257 071 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=2
0805 161259 920 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=93
0805 161259 921 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=97
0805 161259 992 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=176
0805 161259 992 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=177
0805 161435 291 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 161437 122 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=106
0805 161437 282 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=39
0805 161437 739 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 161437 749 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=7
0805 161437 913 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 161438 139 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 161439 659 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 161439 659 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 161439 659 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 161439 659 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 161439 660 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 161439 660 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 161439 660 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 161439 660 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 161439 687 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=2
0805 161442 668 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=181
0805 161442 668 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=184
0805 161442 784 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=116
0805 161442 784 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=116
0805 161442 941 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=153
0805 161655 625 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=139
0805 161655 626 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=144
0805 161655 658 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=133182
0805 161655 658 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=133183
0805 161810 171 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 161812 509 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=103
0805 161812 658 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=36
0805 161813 103 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0805 161813 112 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 161813 279 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 161813 499 - [,] com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,result=-,time=5
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 161814 839 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 161814 869 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=2
0805 161817 922 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=216
0805 161817 923 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=221
0805 161817 964 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=41
0805 161817 964 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=41
0805 161818 106 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=140
0805 161826 606 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=44
0805 161826 607 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=49
0805 161831 282 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=13587
0805 161831 282 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=13588
