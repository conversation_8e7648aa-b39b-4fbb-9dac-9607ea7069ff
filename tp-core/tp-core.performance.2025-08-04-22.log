com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250804 220112,main
com.pay.tp.core.configuration.AliOss.ossClient,time=117,20250804 220114,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=30,20250804 220114,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250804 220115,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=15,20250804 220115,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250804 220115,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250804 220116,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250804 220116,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250804 220116,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250804 220116,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250804 220116,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250804 220116,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250804 220116,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250804 220116,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250804 220116,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.insert,time=229,20250804 220119,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.insert,time=312,20250804 220119,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250804 220313,main
com.pay.tp.core.configuration.AliOss.ossClient,time=115,20250804 220315,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=32,20250804 220315,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250804 220316,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250804 220316,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250804 220316,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250804 220317,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250804 220317,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250804 220317,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250804 220317,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250804 220317,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250804 220317,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250804 220317,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250804 220317,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250804 220317,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.insert,time=109,20250804 220320,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.insert,time=192,20250804 220320,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250804 220410,main
com.pay.tp.core.configuration.AliOss.ossClient,time=111,20250804 220412,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=52,20250804 220412,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250804 220412,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=6,20250804 220412,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250804 220413,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250804 220414,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250804 220414,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250804 220414,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250804 220414,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250804 220414,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250804 220414,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250804 220414,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=5,20250804 220414,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250804 220414,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=11333,20250804 220433,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=11416,20250804 220433,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=46,20250804 220433,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=49,20250804 220433,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=11580,20250804 220433,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=37195,20250804 220454,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=9,20250804 220506,main
com.pay.tp.core.configuration.AliOss.ossClient,time=107,20250804 220508,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=50,20250804 220508,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250804 220509,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250804 220509,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250804 220509,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250804 220510,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250804 220510,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250804 220510,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250804 220510,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250804 220510,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250804 220510,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250804 220510,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=8,20250804 220510,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250804 220510,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=226,20250804 220516,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=230,20250804 220516,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=17,20250804 220545,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=11724,20250804 220545,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=29053,20250804 220545,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=32102,20250804 220545,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=6,20250804 220750,main
com.pay.tp.core.configuration.AliOss.ossClient,time=107,20250804 220752,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=39,20250804 220752,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250804 220752,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250804 220752,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250804 220753,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250804 220754,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250804 220754,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250804 220754,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250804 220754,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250804 220754,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250804 220754,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250804 220754,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250804 220754,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250804 220754,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=202,20250804 220808,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=207,20250804 220808,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=16,20250804 220813,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=1643,20250804 220813,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=5035,20250804 220813,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=15853,20250804 220813,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250804 220947,main
com.pay.tp.core.configuration.AliOss.ossClient,time=114,20250804 220950,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=33,20250804 220950,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250804 220950,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250804 220950,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=3,20250804 220950,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250804 220952,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250804 220952,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250804 220952,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250804 220952,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250804 220952,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250804 220952,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250804 220952,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=7,20250804 220952,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250804 220952,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=166,20250804 220955,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=169,20250804 220955,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=69,20250804 221000,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=2443,20250804 221000,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=166,20250804 221012,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=2,20250804 221012,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=2,20250804 221012,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=17707,20250804 221012,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=17708,20250804 221012,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250804 221511,main
com.pay.tp.core.configuration.AliOss.ossClient,time=118,20250804 221513,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=42,20250804 221513,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250804 221514,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250804 221514,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250804 221514,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250804 221516,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250804 221516,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250804 221516,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250804 221516,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250804 221516,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250804 221516,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250804 221516,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250804 221516,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250804 221516,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=167,20250804 221519,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=171,20250804 221519,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=56,20250804 221519,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=56,20250804 221519,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=30394,20250804 221552,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=4,20250804 221552,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=4,20250804 221552,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=33858,20250804 221552,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=33859,20250804 221552,main
