com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=6,20250805 095035,main
com.pay.tp.core.configuration.AliOss.ossClient,time=116,20250805 095037,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=35,20250805 095037,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 095037,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 095037,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 095038,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 095039,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 095039,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 095039,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 095039,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 095039,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 095039,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 095039,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=5,20250805 095039,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 095039,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=817,20250805 095042,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=821,20250805 095042,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=72,20250805 095043,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=73,20250805 095043,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=37579,20250805 095135,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=2,20250805 095135,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=2,20250805 095135,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=53394,20250805 095135,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=53395,20250805 095135,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 095419,main
com.pay.tp.core.configuration.AliOss.ossClient,time=108,20250805 095421,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=46,20250805 095421,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250805 095422,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250805 095422,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 095422,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=5,20250805 095424,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 095424,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 095424,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 095424,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 095424,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 095424,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 095424,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=9,20250805 095424,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 095424,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=98,20250805 095427,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=102,20250805 095427,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=173,20250805 095427,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=175,20250805 095427,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 095547,main
com.pay.tp.core.configuration.AliOss.ossClient,time=106,20250805 095549,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=30,20250805 095549,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250805 095550,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250805 095550,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 095550,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 095552,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 095552,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 095552,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 095552,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 095552,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 095552,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 095552,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 095552,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 095552,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=164,20250805 095555,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=167,20250805 095555,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=37,20250805 095555,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=39,20250805 095555,main
