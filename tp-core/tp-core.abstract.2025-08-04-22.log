0804 220112 571 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0804 220114 497 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=117
0804 220114 658 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=30
0804 220115 099 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0804 220115 118 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=15
0804 220115 282 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0804 220116 712 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0804 220116 744 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0804 220119 691 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.insert,result=-,time=229
0804 220119 766 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.insert,result=-,time=312
0804 220313 749 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0804 220315 535 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=115
0804 220315 713 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=32
0804 220316 183 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0804 220316 192 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0804 220316 352 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0804 220317 793 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0804 220317 794 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0804 220317 794 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0804 220317 794 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0804 220317 794 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0804 220317 794 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0804 220317 794 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0804 220317 794 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0804 220317 822 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0804 220320 743 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.insert,result=-,time=109
0804 220320 818 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.insert,result=-,time=192
0804 220410 404 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0804 220412 228 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=111
0804 220412 432 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=52
0804 220412 899 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0804 220412 908 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=6
0804 220413 070 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0804 220414 532 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=5
0804 220414 563 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0804 220433 081 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=11333
0804 220433 083 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=11416
0804 220433 131 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=46
0804 220433 132 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=49
0804 220433 153 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=-,time=11580
0804 220454 501 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=37195
0804 220506 493 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=9
0804 220508 363 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=107
0804 220508 553 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=50
0804 220509 019 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0804 220509 027 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0804 220509 197 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0804 220510 680 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0804 220510 680 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0804 220510 680 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0804 220510 680 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0804 220510 681 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0804 220510 681 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0804 220510 681 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0804 220510 681 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=8
0804 220510 712 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0804 220516 783 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=226
0804 220516 783 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=230
0804 220545 572 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=17
0804 220545 572 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=11724
0804 220545 597 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=-,time=29053
0804 220545 597 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=32102
0804 220750 444 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=6
0804 220752 250 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=107
0804 220752 467 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=39
0804 220752 939 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0804 220752 947 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0804 220753 102 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0804 220754 561 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0804 220754 588 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0804 220808 289 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=202
0804 220808 289 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=207
0804 220813 071 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=16
0804 220813 071 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=1643
0804 220813 107 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=-,time=5035
0804 220813 108 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=15853
0804 220947 612 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0804 220950 003 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=114
0804 220950 184 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=33
0804 220950 654 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0804 220950 661 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0804 220950 810 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=3
0804 220952 333 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0804 220952 333 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0804 220952 333 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0804 220952 333 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0804 220952 333 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0804 220952 334 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0804 220952 334 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0804 220952 334 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=7
0804 220952 362 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0804 220955 280 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=166
0804 220955 280 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=169
0804 221000 986 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=69
0804 221000 986 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=2443
0804 221012 751 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=166
0804 221012 754 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=2
0804 221012 754 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=2
0804 221012 809 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=98,time=17707
0804 221012 809 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=17708
0804 221511 757 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0804 221513 707 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=118
0804 221513 860 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=42
0804 221514 330 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0804 221514 337 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0804 221514 489 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0804 221516 257 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0804 221516 289 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0804 221519 240 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=167
0804 221519 241 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=171
0804 221519 297 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=56
0804 221519 297 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=56
0804 221552 856 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=30394
0804 221552 864 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=4
0804 221552 864 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=4
0804 221552 920 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=98,time=33858
0804 221552 920 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=33859
