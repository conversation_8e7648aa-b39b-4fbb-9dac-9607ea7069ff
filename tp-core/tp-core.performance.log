com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 160325,main
com.pay.tp.core.configuration.AliOss.ossClient,time=136,20250805 160327,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=40,20250805 160327,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 160328,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 160328,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 160328,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 160328,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 160330,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 160330,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 160330,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 160330,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 160330,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 160330,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 160330,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=7,20250805 160330,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 160330,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=206,20250805 160333,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=209,20250805 160333,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=33,20250805 160333,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=33,20250805 160333,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=29356,20250805 160402,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=64,20250805 160512,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=65,20250805 160512,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=99157,20250805 160512,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=99159,20250805 160512,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 160645,main
com.pay.tp.core.configuration.AliOss.ossClient,time=115,20250805 160648,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=32,20250805 160648,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 160648,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250805 160648,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 160648,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 160649,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 160650,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 160650,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 160650,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 160650,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 160650,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 160650,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 160650,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 160650,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 160650,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=89,20250805 160653,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=92,20250805 160653,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=174,20250805 160653,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=175,20250805 160653,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=6,20250805 160726,main
com.pay.tp.core.configuration.AliOss.ossClient,time=113,20250805 160728,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=52,20250805 160728,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 160728,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250805 160728,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 160729,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 160729,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=2,20250805 160731,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 160731,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 160731,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 160731,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 160731,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 160731,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 160731,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=5,20250805 160731,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=4,20250805 160731,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=190,20250805 160734,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=193,20250805 160734,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=39,20250805 160734,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=39,20250805 160734,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=2711,20250805 160737,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=21,20250805 160749,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=21,20250805 160749,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=15152,20250805 160749,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=15153,20250805 160749,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 160933,main
com.pay.tp.core.configuration.AliOss.ossClient,time=118,20250805 160935,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=31,20250805 160936,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=5,20250805 160936,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=8,20250805 160936,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=3,20250805 160936,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 160937,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=2,20250805 160938,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 160938,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 160938,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=1,20250805 160938,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 160938,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 160938,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 160938,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 160938,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=5,20250805 160938,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=228,20250805 160941,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=233,20250805 160941,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=32,20250805 160941,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=33,20250805 160941,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=136,20250805 160941,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=21,20250805 160948,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=21,20250805 160948,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=7452,20250805 160948,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=7454,20250805 160948,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 160956,main
com.pay.tp.core.configuration.AliOss.ossClient,time=114,20250805 160958,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=32,20250805 160958,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 160959,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 160959,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 160959,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=6,20250805 160959,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 161000,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 161000,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 161000,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 161000,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 161000,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 161000,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 161000,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=5,20250805 161000,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=2,20250805 161000,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=179,20250805 161003,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=182,20250805 161003,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=20,20250805 161003,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=20,20250805 161003,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=133,20250805 161004,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=20850,20250805 161024,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=20851,20250805 161024,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=3,20250805 161030,main
com.pay.tp.core.configuration.AliOss.ossClient,time=105,20250805 161032,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=36,20250805 161032,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 161033,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250805 161033,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=3,20250805 161033,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=6,20250805 161033,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=4,20250805 161035,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 161035,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 161035,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 161035,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 161035,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 161035,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 161035,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 161035,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 161035,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=85,20250805 161038,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=88,20250805 161038,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=182,20250805 161038,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=183,20250805 161038,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 161252,main
com.pay.tp.core.configuration.AliOss.ossClient,time=106,20250805 161254,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=41,20250805 161254,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 161255,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 161255,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 161255,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 161255,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 161257,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 161257,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 161257,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 161257,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 161257,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 161257,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 161257,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 161257,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=2,20250805 161257,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=93,20250805 161259,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=97,20250805 161259,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=176,20250805 161259,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=177,20250805 161259,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 161435,main
com.pay.tp.core.configuration.AliOss.ossClient,time=106,20250805 161437,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=39,20250805 161437,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 161437,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=7,20250805 161437,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 161437,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 161438,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 161439,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 161439,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 161439,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 161439,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 161439,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 161439,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 161439,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 161439,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=2,20250805 161439,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=181,20250805 161442,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=184,20250805 161442,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=116,20250805 161442,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=116,20250805 161442,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=153,20250805 161442,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=139,20250805 161655,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=144,20250805 161655,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=133182,20250805 161655,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=133183,20250805 161655,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 161810,main
com.pay.tp.core.configuration.AliOss.ossClient,time=103,20250805 161812,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=36,20250805 161812,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250805 161813,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 161813,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 161813,main
com.pay.tp.core.configuration.RestTemplateConfig.yyProxyRestTemplate,time=5,20250805 161813,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 161814,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 161814,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 161814,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 161814,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 161814,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 161814,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 161814,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 161814,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=2,20250805 161814,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=216,20250805 161817,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=221,20250805 161817,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=41,20250805 161817,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=41,20250805 161817,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=140,20250805 161818,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=44,20250805 161826,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=49,20250805 161826,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=13587,20250805 161831,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=13588,20250805 161831,main
