com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=4728111,20250805 111446,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=4,20250805 111446,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=5,20250805 111446,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=4730983,20250805 111446,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=4730984,20250805 111446,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 111512,main
com.pay.tp.core.configuration.AliOss.ossClient,time=125,20250805 111514,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=33,20250805 111514,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 111515,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 111515,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 111515,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 111516,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 111516,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 111516,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 111516,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 111516,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 111516,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 111516,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 111516,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 111516,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=194,20250805 111519,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=198,20250805 111519,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=27,20250805 111519,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=28,20250805 111519,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=104978,20250805 111704,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=4,20250805 111704,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=4,20250805 111704,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=105258,20250805 111705,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=105260,20250805 111705,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 114112,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=4,20250805 114706,main
com.pay.tp.core.configuration.AliOss.ossClient,time=124,20250805 114708,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=32,20250805 114708,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=3,20250805 114709,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=4,20250805 114709,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 114709,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 114710,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 114710,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 114710,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 114710,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 114710,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 114710,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 114710,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 114710,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 114710,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=221,20250805 114713,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=224,20250805 114713,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=35,20250805 114713,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=37,20250805 114713,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=110,20250805 114713,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=1,20250805 114713,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=2,20250805 114713,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=429,20250805 114714,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=430,20250805 114714,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=5,20250805 115152,main
com.pay.tp.core.configuration.AliOss.ossClient,time=105,20250805 115154,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=31,20250805 115154,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250805 115155,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 115155,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 115155,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 115156,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 115156,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 115156,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 115156,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 115156,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 115156,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 115156,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=6,20250805 115156,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=2,20250805 115156,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=82,20250805 115159,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=85,20250805 115159,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=156,20250805 115159,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=157,20250805 115159,main
com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,time=9,20250805 115230,main
com.pay.tp.core.configuration.AliOss.ossClient,time=118,20250805 115232,main
com.pay.tp.core.configuration.AliOss.defaultAcsClient,time=28,20250805 115232,main
com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,time=2,20250805 115233,main
com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,time=5,20250805 115233,main
com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,time=2,20250805 115233,main
com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,time=3,20250805 115234,main
com.pay.tp.core.configuration.JobExecutorProperties.getAppName,time=0,20250805 115234,main
com.pay.tp.core.configuration.JobExecutorProperties.getIp,time=0,20250805 115234,main
com.pay.tp.core.configuration.JobExecutorProperties.getPort,time=0,20250805 115234,main
com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,time=0,20250805 115234,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,time=0,20250805 115234,main
com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,time=0,20250805 115234,main
com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,time=7,20250805 115234,main
com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,time=3,20250805 115235,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,time=170,20250805 115238,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,time=174,20250805 115238,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,time=341,20250805 115238,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,time=342,20250805 115238,main
com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,time=157,20250805 115238,main
com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,time=2,20250805 115238,main
com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,time=2,20250805 115238,main
com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,time=718,20250805 115238,main
com.pay.tp.core.task.YongYouDataPushTask.execute,time=719,20250805 115238,main
