0805 095035 120 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=6
0805 095037 215 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=116
0805 095037 380 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=35
0805 095037 848 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 095037 856 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 095038 007 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 095039 408 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=5
0805 095039 441 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 095042 982 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=817
0805 095042 982 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=821
0805 095043 054 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=72
0805 095043 055 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=73
0805 095135 505 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=37579
0805 095135 508 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=2
0805 095135 508 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=2
0805 095135 547 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=98,time=53394
0805 095135 547 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=53395
0805 095419 459 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 095421 502 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=108
0805 095421 697 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=46
0805 095422 133 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0805 095422 142 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0805 095422 307 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 095424 244 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=5
0805 095424 244 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 095424 245 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 095424 245 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 095424 245 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 095424 245 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 095424 245 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 095424 245 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=9
0805 095424 278 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 095427 191 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=98
0805 095427 192 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=102
0805 095427 254 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=173
0805 095427 255 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=175
0805 095547 178 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 095549 715 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=106
0805 095549 863 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=30
0805 095550 327 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0805 095550 335 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0805 095550 490 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 095552 604 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 095552 605 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 095552 605 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 095552 605 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 095552 605 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 095552 605 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 095552 605 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 095552 605 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 095552 633 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 095555 644 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=164
0805 095555 644 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=167
0805 095555 682 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=37
0805 095555 683 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=39
