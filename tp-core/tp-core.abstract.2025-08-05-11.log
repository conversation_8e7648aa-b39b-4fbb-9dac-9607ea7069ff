0805 111446 133 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=4728111
0805 111446 145 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=4
0805 111446 145 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=5
0805 111446 452 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=98,time=4730983
0805 111446 452 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=4730984
0805 111512 485 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 111514 383 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=125
0805 111514 541 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=33
0805 111515 003 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 111515 011 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 111515 167 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 111516 880 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 111516 909 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 111519 962 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=194
0805 111519 962 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=198
0805 111519 989 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=27
0805 111519 990 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=28
0805 111704 970 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=104978
0805 111704 975 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=4
0805 111704 975 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=4
0805 111705 013 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=98,time=105258
0805 111705 014 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=105260
0805 114112 018 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 114706 754 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=4
0805 114708 578 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=124
0805 114708 738 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=32
0805 114709 189 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=3
0805 114709 196 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=4
0805 114709 367 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 114710 869 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 114710 899 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 114713 831 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=221
0805 114713 831 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=224
0805 114713 867 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=35
0805 114713 868 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=37
0805 114713 980 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=110
0805 114713 982 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=1
0805 114713 982 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=2
0805 114714 028 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=98,time=429
0805 114714 028 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=430
0805 115152 030 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=5
0805 115154 422 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=105
0805 115154 580 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=31
0805 115155 054 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0805 115155 062 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 115155 236 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 115156 702 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=6
0805 115156 730 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=2
0805 115159 641 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=82
0805 115159 641 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=85
0805 115159 703 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=00,time=156
0805 115159 703 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=157
0805 115230 874 - [,] com.pay.tp.core.configuration.MybatisConfiguration.configurationCustomizer,result=-,time=9
0805 115232 733 - [,] com.pay.tp.core.configuration.AliOss.ossClient,result=-,time=118
0805 115232 883 - [,] com.pay.tp.core.configuration.AliOss.defaultAcsClient,result=-,time=28
0805 115233 344 - [,] com.pay.tp.core.configuration.RestTemplateConfig.simpleClientHttpRequestFactory,result=-,time=2
0805 115233 352 - [,] com.pay.tp.core.configuration.RestTemplateConfig.restTemplate,result=-,time=5
0805 115233 530 - [,] com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig.getTemplates,result=-,time=2
0805 115234 984 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getDispatcherAddresses,result=-,time=3
0805 115234 984 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAppName,result=-,time=0
0805 115234 984 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getIp,result=-,time=0
0805 115234 984 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getPort,result=-,time=0
0805 115234 984 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getAccessToken,result=-,time=0
0805 115234 985 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogPath,result=-,time=0
0805 115234 985 - [,] com.pay.tp.core.configuration.JobExecutorProperties.getLogRetentionDays,result=-,time=0
0805 115234 985 - [,] com.pay.tp.core.configuration.JobExecutorConfig.springJobExecutor,result=-,time=7
0805 115235 017 - [,] com.pay.tp.core.configuration.MybatisConfiguration.pageHelper,result=-,time=3
0805 115238 039 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.findPendingPushRecords,result=-,time=170
0805 115238 040 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.findPendingPushRecords,result=-,time=174
0805 115238 381 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum,result=-,time=341
0805 115238 382 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.batchIncrementPushReturnNum,result=-,time=342
0805 115238 547 - [,] com.pay.tp.core.remote.yongyou.YongYouClient.addCustomer,result=-,time=157
0805 115238 550 - [,] com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper.updatePushStatusBatch,result=-,time=2
0805 115238 550 - [,] com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService.updatePushStatusBatch,result=-,time=2
0805 115238 574 - [,] com.pay.tp.core.biz.impl.YongYouDataPushBiz.batchProcessPush,result=98,time=718
0805 115238 574 - [,] com.pay.tp.core.task.YongYouDataPushTask.execute,result=-,time=719
