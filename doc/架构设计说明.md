# 用友API架构设计说明

## 设计原则

参考钉钉消息模块的实现，采用了相同的架构模式，确保代码风格和设计理念的一致性。

## 架构分层

### 1. Controller层 (YongYouWorkPhoneController)
**职责**: 
- 接收HTTP请求
- 参数验证
- 调用业务逻辑层
- 返回统一格式的响应

**特点**:
- 使用`@Valid`注解进行参数校验
- 记录请求和响应日志
- 返回`ResultsBean`统一响应格式

### 2. Business层 (YongYouBiz)
**职责**:
- 业务逻辑处理
- 数据校验和转换
- 调用Client层进行API请求
- 处理业务异常

**特点**:
- 专注于业务逻辑，不涉及具体的HTTP调用
- 包含业务级别的参数校验
- 处理API响应的业务逻辑
- 记录业务操作日志

### 3. Client层 (YongYouClient)
**职责**:
- 封装第三方API调用
- 处理HTTP请求和响应
- 签名生成和请求头构建
- 网络异常处理

**特点**:
- 专注于API调用，不涉及业务逻辑
- 负责签名算法实现
- 处理HTTP层面的异常
- 记录API调用日志

## 与钉钉模块的对比

### 钉钉模块架构
```
DingDingMsgController -> DingDingBiz -> DingDingMsgClient
```

### 用友模块架构
```
YongYouWorkPhoneController -> YongYouBiz -> YongYouClient
```

## 职责分离的优势

### 1. 单一职责原则
- **Controller**: 只负责HTTP层面的处理
- **Biz**: 只负责业务逻辑
- **Client**: 只负责第三方API调用

### 2. 可测试性
- 每一层都可以独立进行单元测试
- 可以通过Mock对象测试各层的逻辑
- 便于集成测试和端到端测试

### 3. 可维护性
- 修改API调用逻辑只需要修改Client层
- 修改业务逻辑只需要修改Biz层
- 各层职责清晰，便于代码维护

### 4. 可扩展性
- 可以轻松添加新的API接口
- 可以支持多种第三方服务
- 便于添加缓存、重试等横切关注点

## 配置管理

### 集中化配置
所有用友API相关的配置都通过`@Value`注解从配置文件中读取：

```yaml
yongyou:
  api:
    baseUrl: https://open.smarttrust.com.cn
    signKey: your-sign-key
    partnerId: your-partner-id
    company: your-company-code
```

### 配置校验
在Biz层进行配置完整性校验，确保必要的配置项都已设置。

## 异常处理策略

### 1. 分层异常处理
- **Client层**: 处理网络异常、HTTP异常
- **Biz层**: 处理业务异常、配置异常
- **Controller层**: 处理参数校验异常

### 2. 统一异常响应
所有异常最终都转换为`ResultsBean.FAIL()`格式返回给客户端。

## 日志记录策略

### 1. 分层日志
- **Controller层**: 记录请求参数和响应结果
- **Biz层**: 记录业务处理过程和结果
- **Client层**: 记录API调用的详细信息

### 2. 日志级别
- **INFO**: 正常的业务流程
- **ERROR**: 异常情况和错误信息
- **DEBUG**: 详细的调试信息（如签名生成过程）

## 扩展建议

### 1. 添加更多API接口
可以在YongYouClient中添加更多方法来支持用友的其他API接口。

### 2. 添加缓存机制
可以在Biz层添加缓存来提高性能，特别是对于查询类接口。

### 3. 添加重试机制
可以在Client层添加重试机制来提高API调用的可靠性。

### 4. 添加监控和指标
可以添加API调用的监控指标，如成功率、响应时间等。
