# 用友添加客户API实现

## 概述

基于用友开放平台API文档，实现了添加客户的功能。参考了钉钉消息发送的实现模式，采用了相似的架构设计。

## 文件结构

```
src/main/java/com/pay/tp/core/
├── beans/yongyou/
│   ├── YongYouCustomerReq.java      # 添加客户请求Bean
│   └── YongYouCustomerResp.java     # 添加客户响应Bean
├── biz/impl/
│   └── YongYouBiz.java              # 用友业务逻辑类
├── controller/yongyou/
│   ├── YongYouWorkPhoneController.java  # 用友控制器
│   ├── 请求说明.md                   # 原始API文档
│   ├── 用友API配置说明.md            # 配置说明文档
│   └── README.md                    # 本文件
├── remote/yongyou/
│   └── YongYouClient.java           # 用友API客户端（封装接口调用）
└── utils/
    └── Md5Util.java                 # MD5工具类（已存在）

src/test/java/com/pay/tp/core/controller/yongyou/
└── YongYouWorkPhoneControllerTest.java  # 测试类
```

## 主要功能

### 1. 请求Bean (YongYouCustomerReq)
- 支持批量添加客户
- 包含完整的客户信息字段
- 支持自定义字段和多联系方式
- 使用JSR-303验证注解

### 2. 响应Bean (YongYouCustomerResp)
- 映射用友API的响应结构
- 区分成功和失败的客户记录
- 提供详细的错误信息

### 3. API客户端 (YongYouClient)
- 封装用友API接口调用
- 自动生成签名和请求头
- 处理HTTP请求和响应
- 支持配置化的API参数

### 4. 业务逻辑类 (YongYouBiz)
- 处理业务逻辑和数据校验
- 调用YongYouClient进行API请求
- 完整的异常处理和日志记录
- 响应结果的业务处理

### 5. 控制器 (YongYouWorkPhoneController)
- 提供RESTful API接口
- 参数验证和日志记录
- 统一的响应格式

## API接口

### 添加客户
- **URL**: `POST /yongYou/customer/add`
- **Content-Type**: `application/json`
- **请求体**: YongYouCustomerReq对象
- **响应**: ResultsBean<YongYouCustomerResp>

## 配置要求

在application.yml中添加以下配置：

```yaml
yongyou:
  api:
    baseUrl: https://open.smarttrust.com.cn  # API基础URL
    signKey: your-sign-key                   # 签名密钥
    partnerId: your-partner-id               # 合作伙伴ID
    company: your-company-code               # 企业串码
```

## 签名机制

实现了用友API要求的MD5签名机制：
1. 生成毫秒级时间戳
2. 按照 `signKey + company + partnerId + timestamp` 拼接字符串
3. 计算MD5并转换为大写
4. 设置到请求头中

## 特性

1. **参考钉钉实现**: 采用了与DingDingBiz和DingDingMsgClient相同的架构模式
2. **职责分离**: Client负责接口调用，Biz负责业务逻辑
3. **完整的错误处理**: 包含参数校验、配置检查、异常捕获
4. **详细的日志记录**: 记录请求和响应的完整信息
5. **配置化设计**: 支持通过配置文件修改API参数
6. **类型安全**: 使用强类型Bean而非Map
7. **测试支持**: 提供了测试用例和示例数据

## 使用示例

```java
@Autowired
private YongYouBiz yongYouBiz;

// 创建客户请求
YongYouCustomerReq request = new YongYouCustomerReq();
// ... 设置客户信息

// 调用API
ResultsBean<YongYouCustomerResp> result = yongYouBiz.addCustomer(request);

if (result.isSuccess()) {
    YongYouCustomerResp response = result.getData();
    // 处理成功响应
} else {
    // 处理失败情况
}
```

## 注意事项

1. 确保配置了正确的API参数
2. 时间戳精确到毫秒，时间差不能超过1分钟
3. 客户ID和姓名为必填字段
4. 手机号根据后台设置可能为必填
5. 支持自定义字段和多联系方式配置
