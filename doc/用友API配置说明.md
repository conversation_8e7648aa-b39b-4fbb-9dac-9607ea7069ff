# 用友API配置说明

## 配置参数

在 `application.yml` 或 `application.properties` 中添加以下配置：

### application.yml 配置示例
```yaml
yongyou:
  api:
    baseUrl: https://open.smarttrust.com.cn  # 用友API基础URL（私有部署时修改为实际域名）
    signKey: sign-key-baidu                  # 签名密钥
    partnerId: p123456                       # 用户id，用于识别调用人
    company: baidu                           # 企业串码
```

### application.properties 配置示例
```properties
# 用友API配置
yongyou.api.baseUrl=https://open.smarttrust.com.cn
yongyou.api.signKey=sign-key-baidu
yongyou.api.partnerId=p123456
yongyou.api.company=baidu
```

## API接口说明

### 添加客户接口

**接口地址：** `POST /yongYou/customer/add`

**请求示例：**
```json
{
  "customers": [
    {
      "customerId": "90002",
      "name": "小潘妈妈",
      "phone": "13960190002",
      "userId": "90031",
      "collaborators": [
        "90032",
        "90033"
      ],
      "customFieldValues": [
        {
          "id": 709,
          "values": [
            "清华大学附属中学"
          ]
        },
        {
          "id": 707,
          "values": [
            "一年级"
          ]
        },
        {
          "id": 708,
          "values": [
            "英语",
            "数学"
          ]
        },
        {
          "id": 735,
          "values": [],
          "fieldType": "6",
          "multiContacts": [
            {
              "optionId": "11765",
              "optionText": "13466665544"
            },
            {
              "optionId": "11916",
              "optionText": "13466665542"
            }
          ]
        }
      ]
    },
    {
      "company": "测试3",
      "customerId": "90003",
      "phone": "13960190003",
      "userId": "90031"
    }
  ]
}
```

**响应示例：**
```json
{
  "code": "00",
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "finished",
    "data": {
      "success": {
        "90002": "C6B2A934A1DD4ED599B7D0905511D7DB"
      },
      "failed": {
        "90003": "信息不完整"
      }
    },
    "code": null
  }
}
```

## 签名算法

签名计算方式：`md5(签名key+company+partnerId+timestamp).toUpper()`

示例：
- 签名key: `sign-key-baidu`
- company: `baidu`
- partnerId: `p123456`
- timestamp: `1530081688201`
- 签名字符串: `sign-key-baidubaidup1234561530081688201`
- MD5后转大写: `A5869E8A67393190E81197CBA43A8782`

## 注意事项

1. 时间戳精确到毫秒，如果时间相差1分钟会返回错误结果
2. 请求参数与返回数据均为JSON格式，请勿使用单引号包裹
3. 默认情况下，平台系统按照手机号排重（一个手机号只能添加一条客户数据）
4. 未传userId时会优先按照客户分配规则分配客户，未开启客户分配规则设置的情况下会将客户分配给超管
5. 部分企业不需要按照手机号排重（多个销售可添加同一个手机号），后台会存多条手机号相同的客户数据
