# 用友数据推送定时任务说明

## 概述

用友数据推送定时任务用于定期将验证通过的用友工作手机业务数据推送到用友系统。任务会自动查询符合条件的记录，调用用友API进行数据推送，并更新推送状态。

## 文件结构

```
src/main/java/com/pay/tp/core/
├── entity/yongyou/
│   └── YongYouWorkPhoneBusiness.java           # 用友工作手机业务实体类
├── mapper/yongyou/
│   └── YongYouWorkPhoneBusinessMapper.java     # 数据访问接口
├── service/yongyou/
│   └── YongYouWorkPhoneBusinessService.java    # 业务服务类
├── biz/impl/
│   └── YongYouDataPushBiz.java                 # 业务逻辑处理类
└── task/
    └── YongYouDataPushTask.java                # 定时任务类

src/main/resources/mybatis/
└── YongYouWorkPhoneBusinessMapper.xml          # MyBatis映射文件

src/main/java/com/pay/tp/core/controller/yongyou/
└── yongyou_work_phone_business.sql             # 数据库表创建脚本

src/test/java/com/pay/tp/core/task/
└── YongYouDataPushTaskTest.java                # 测试类
```

## 数据库表结构

### YONGYOU_WORK_PHONE_BUSINESS 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| ID | NUMBER(12,0) | 主键ID |
| OPTIMISTIC | NUMBER(12,0) | 乐观锁版本号 |
| CUSTOMER_NO | VARCHAR2(30) | 商户编号 |
| FULL_NAME | VARCHAR2(100) | 商户名称 |
| REAL_NAME | VARCHAR2(50) | 真实姓名 |
| PHONE_NO | VARCHAR2(20) | 手机号 |
| ORGAN_CODE | VARCHAR2(20) | 所属地区 |
| VERIFY_STATUS | VARCHAR2(20) | 验证状态 |
| VERIFY_RETURN_NUM | NUMBER(5,0) | 验证次数 |
| VERIFY_RETURN_VALUE | VARCHAR2(20) | 验证返回值 |
| VERIFY_RETURN_REMARK | VARCHAR2(500) | 验证返回备注 |
| YONGYOU_PUSH_STATUS | VARCHAR2(20) | 用友推送状态 |
| PUSH_RETURN_NUM | NUMBER(5,0) | 推送次数 |
| PUSH_RETURN_VALUE | VARCHAR2(20) | 推送返回值 |
| PUSH_RETURN_MESSAGE | VARCHAR2(500) | 推送返回信息 |
| OPERATOR | VARCHAR2(100) | 操作人 |
| CREATE_TIME | DATE | 创建时间 |
| UPDATE_TIME | DATE | 更新时间 |
| BRAND | VARCHAR2(20) | 品牌 |

## 定时任务逻辑

### 查询条件
定时任务会查询符合以下条件的记录：
- `YONGYOU_PUSH_STATUS` = 'INIT' 或 'FAIL'
- `PUSH_RETURN_NUM` < 3 (推送次数小于3次)
- `VERIFY_STATUS` = 'STATUS' (验证状态为STATUS)

### 处理流程
1. 查询符合条件的记录（每次最多1000条）
2. **批量更新推送次数**：先对所有查询到的记录批量增加推送次数 (`PUSH_RETURN_NUM` + 1)
3. 对每条记录逐个处理：
   - 构建用友客户请求数据
   - 调用用友API进行推送
   - 根据推送结果更新状态：
     - 成功：`YONGYOU_PUSH_STATUS` = 'SUCCESS'
     - 失败：`YONGYOU_PUSH_STATUS` = 'FAIL'
   - 记录推送返回值和返回信息
4. **事务管理**：每个步骤都在独立的事务中执行，确保数据一致性

### 状态说明

#### 推送状态 (YONGYOU_PUSH_STATUS)
- `INIT`: 初始状态，待推送
- `SUCCESS`: 推送成功
- `FAIL`: 推送失败

#### 验证状态 (VERIFY_STATUS)
- `STATUS`: 验证通过，可以推送
- `SUCCESS`: 验证成功
- `FAIL`: 验证失败

## 配置说明

### 定时任务配置
在调度系统中配置定时任务：
- **任务名称**: `yongYouDataPush`
- **执行类**: `YongYouDataPushTask`
- **执行频率**: 建议每5-10分钟执行一次

### 用友API配置
确保在 `application.yml` 中配置了用友API相关参数：
```yaml
yongyou:
  api:
    baseUrl: https://open.smarttrust.com.cn
    signKey: your-sign-key
    partnerId: your-partner-id
    company: your-company-code
```

## 使用方法

### 1. 部署数据库表
执行 `yongyou_work_phone_business.sql` 脚本创建数据库表和序列。

### 2. 插入业务数据
向 `YONGYOU_WORK_PHONE_BUSINESS` 表插入需要推送的数据：
```sql
INSERT INTO UBADMA.YONGYOU_WORK_PHONE_BUSINESS (
    ID, OPTIMISTIC, CUSTOMER_NO, FULL_NAME, REAL_NAME, PHONE_NO, 
    ORGAN_CODE, VERIFY_STATUS, YONGYOU_PUSH_STATUS, PUSH_RETURN_NUM,
    OPERATOR, CREATE_TIME, UPDATE_TIME, BRAND
) VALUES (
    UBADMA.SEQ_YONGYOU_WORK_PHONE_ID.nextval, 0, '商户编号', '商户名称', '真实姓名', '手机号',
    '地区代码', 'STATUS', 'INIT', 0,
    'SYSTEM', SYSDATE, SYSDATE, 'PLUS'
);
```

### 3. 配置定时任务
在调度系统中添加定时任务，指向 `yongYouDataPush` 处理器。

### 4. 监控执行结果
- 查看定时任务执行日志
- 检查数据库中记录的推送状态
- 监控推送成功率和失败原因

## 测试

### 运行测试类
```bash
# 运行所有测试
mvn test -Dtest=YongYouDataPushTaskTest

# 运行特定测试方法
mvn test -Dtest=YongYouDataPushTaskTest#testExecute
```

### 测试方法说明
- `testExecute()`: 测试定时任务执行
- `testFindPendingPushRecords()`: 测试查询待推送数据
- `testInsertTestData()`: 插入测试数据
- `testUpdatePushStatus()`: 测试更新推送状态
- `testIncrementPushReturnNum()`: 测试增加推送次数

## 架构优化

### 分层架构
- **Task层**: 定时任务调度，负责任务的启动和结果返回
- **Biz层**: 业务逻辑处理，负责批量处理和事务管理
- **Service层**: 数据服务，负责数据库操作和批量更新
- **Mapper层**: 数据访问，负责SQL执行

### 事务管理
- **批量事务**: 批量更新推送次数在一个事务中完成
- **单条事务**: 每条记录的状态更新在独立事务中，避免相互影响
- **异常隔离**: 单条记录异常不影响其他记录的处理

### 性能优化
- **批量查询**: 一次查询1000条记录，减少数据库交互
- **批量更新**: 推送次数批量更新，提高处理效率
- **分批处理**: 大批量数据分批处理，避免内存溢出

## 注意事项

1. **推送次数限制**: 每条记录最多推送3次，超过3次将不再推送
2. **批量处理**: 每次最多处理1000条记录，避免长时间占用资源
3. **异常处理**: 推送异常时会记录错误信息，不会影响其他记录的处理
4. **事务管理**: 采用分层事务管理，确保数据一致性和异常隔离
5. **日志记录**: 详细记录推送过程和结果，便于问题排查
6. **验证状态**: 查询条件已更新为 `VERIFY_STATUS = 'SUCCESS'`

## 监控指标

建议监控以下指标：
- 定时任务执行频率和耗时
- 推送成功率
- 推送失败原因分布
- 待推送记录数量
- 超过最大推送次数的记录数量
