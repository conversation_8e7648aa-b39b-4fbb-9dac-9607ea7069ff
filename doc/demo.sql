
----DROP TABLE UBADMA.LPOS_STOP_PAY_ORDER ;
CREATE TABLE UBADMA.LPOS_STOP_PAY_ORDER (
    ID NUMBER(12,0) NOT NULL,
  OPTIMISTIC NUMBER(12,0) DEFAULT 0,
  STATUS VARCHAR2(20 BYTE),
  CUSTOMER_NO VARCHAR2(30 BYTE),
  POS_SN VARCHAR2(50 BYTE),
  AMOUNT NUMBER(7,2),
  STOP_PAY_TYPE VARCHAR2(50 BYTE),
  STOP_PAY_TIMES NUMBER(5,0),
  SIM_TIMES NUMBER(5,0),
  ORDER_NO VARCHAR2(50 BYTE),
  CHANNEL_ORDER_NO VARCHAR2(100 BYTE),
  FLOW_NO VARCHAR2(50 BYTE),
  FAIL_NUM NUMBER(5,0) DEFAULT 0,
  REMARK VARCHAR2(150 BYTE),
  OPERATOR VARCHAR2(100 BYTE),
  CREATE_TIME DATE DEFAULT sysdate,
  UPDATE_TIME DATE DEFAULT sysdate,
  BRAND VARCHAR2(20 BYTE) DEFAULT 'PLUS' NOT NULL

) TABLESPACE TBS_PAY_CORE_DATA;

ALTER TABLE UBADMA.LPOS_STOP_PAY_ORDER ADD CONSTRAINT PK_LPOS_STOP_PAY_ORDER PRIMARY KEY (ID) USING INDEX TABLESPACE TBS_PAY_CORE_DATA;

CREATE INDEX UBADMA.IX_LPOS_STOP_PAY_ORDER_CT  ON UBADMA.LPOS_STOP_PAY_ORDER (CREATE_TIME ASC)  TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_LPOS_STOP_PAY_ORDER_FN  ON UBADMA.LPOS_STOP_PAY_ORDER (FLOW_NO ASC)  TABLESPACE TBS_PAY_CORE_DATA;
CREATE UNIQUE INDEX UBADMA.IX_LPOS_STOP_PAY_ORDER_CSS  ON UBADMA.LPOS_STOP_PAY_ORDER (CUSTOMER_NO, STOP_PAY_TYPE,STOP_PAY_TIMES)  TABLESPACE TBS_PAY_CORE_DATA;
CREATE UNIQUE INDEX UBADMA.IX_LPOS_STOP_PAY_ORDER_CON  ON UBADMA.LPOS_STOP_PAY_ORDER (CHANNEL_ORDER_NO ASC)  TABLESPACE TBS_PAY_CORE_DATA;


COMMENT ON COLUMN UBADMA.LPOS_STOP_PAY_ORDER.STATUS IS '状态';
COMMENT ON COLUMN UBADMA.LPOS_STOP_PAY_ORDER.POS_SN IS '序列号';
COMMENT ON COLUMN UBADMA.LPOS_STOP_PAY_ORDER.CUSTOMER_NO IS '所属商户';
COMMENT ON COLUMN UBADMA.LPOS_STOP_PAY_ORDER.STOP_PAY_TYPE IS '类型';
COMMENT ON COLUMN UBADMA.LPOS_STOP_PAY_ORDER.CHANNEL_ORDER_NO IS '渠道订单号';
COMMENT ON COLUMN UBADMA.LPOS_STOP_PAY_ORDER.AMOUNT IS '金额';
COMMENT ON TABLE UBADMA.LPOS_STOP_PAY_ORDER IS '服务费预下单';
GRANT SELECT,UPDATE,INSERT,DELETE ON UBADMA.LPOS_STOP_PAY_ORDER TO UPCOREA;