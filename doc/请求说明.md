请求说明
接口请求域名：https://open.smarttrust.com.cn（私有部署以实际使用的域名为准）
接口请求地址：接口请求域名 + 接口path
接口请求方式均为POST
请求参数与返回数据均为JSON格式（请勿使用单引号包裹 如{'key': 'value'}）
请求Header中需要计算签名
Header参数说明

字段	说明
partnerId	用户id，用于识别调用人，大部分请求为平台分配的管理员id，个别接口需要传员工的用户id
company	企业串码
timestamp	unix时间戳，精确到毫秒 例如 1530081688201（如果时间相差1分钟 会返回错误结果）
sign	签名 MD5后转大写，计算方式 md5(签名key+company+partnerId+timestamp).toUpper
Header示例: 签名key为 sign-key-baidu
请求需要传入的header参数：
  timestamp: 1530081688201
  partnerId: p123456
  company: baidu
  sign = md5(sign-key-baidubaidup1234561530081688201).toUpper() = A5869E8A67393190E81197CBA43A8782



  新增客户
基本信息
Path： /open/customer/add

Method： POST

接口描述：

说明：未传userId时会优先按照客户分配规则分配客户，未开启客户分配规则设置的情况下会将客户分配给超管
           默认情况，平台系统按照手机号排重（一个手机号只能添加添加一条客户数据），后台只存一条客户数据，customerId只有一个
部分企业不需要按照手机号排重（多个销售可添加同一个手机号），后台会存多条手机号相同的客户数据

请求示例
[
  {
    "customerId": "90002",
    "name": "小潘妈妈",
    "phone": "13960190002",
    "userId": "90031",
    "collaborators": [
      "90032",
      "90033"
    ],
    "customFieldValues": [
      {
        "id": 709,
        "values": [
          "清华大学附属中学"
        ]
      },
      {
        "id": 707,
        "values": [
          "一年级"
        ]
      },
      {
        "id": 708,
        "values": [
          "英语",
          "数学"
        ]
      },
      {
        "id": 735,
        "values": [],
        "fieldType":6, // 特殊类型，创建或修改客户信息时，需要回传类型值
        "multiContacts":[
          {
            "optionId": "11765",
            "optionText":"13466665544"
          },
          {
            "optionId": "11916",
            "optionText":"13466665542"
          }
        ]
      }
    ]
  },
  {
    "company": "测试3",
    "customerId": "90003",
    "phone": "13960190003",
    "userId": "90031"
  }
]
返回结果
data中返回success和faield 两种结果：success中返回的为保存成功的数据，key：甲方客户Id value：我公司客户id
failed中返回保存失败的数据 key：甲方客户Id value：失败原因

{
    "success": true,
    "message": "finished",
    "data": {
        "success": {
            "90002": "C6B2A934A1DD4ED599B7D0905511D7DB"
        },
        "failed": {
            "90003": "信息不完整"
        }
    },
    "code": null
}
请求参数
Headers

参数名称	参数值	是否必须	示例	备注
Content-Type	application/json	是		
Body

名称	类型	是否必须	默认值	备注	其他信息
object []	非必须			
item 类型: object

├─ company	string	非必须		_公司	
├─ customerId	string	必须		客户id	
├─ name	string	必须		客户姓名	
├─ phone	string	非必须		客户手机号（已经在管理后台设置手机号不必填的可以不填写手机号）	
├─ position	string	非必须		_职位	
├─ userId	string	非必须		跟进人的用户Id	
├─ gender	string	非必须		性别 男/女	
├─ birthday	string	非必须		生日	
├─ telephone	string	非必须		座机	
├─ qq	string	非必须		qq	
├─ wechat	string	非必须		微信号	
├─ address	string	非必须		地址	
├─ email	string	非必须		邮箱	
├─ from	string	非必须		客户分配：0分配到客户列表（缺省），1分配到公海	
├─ seaId	string	非必须		公海id	
├─ remark	string	非必须		备注	
├─ dataSource	string	非必须		客户来源（可自定义）	
├─ econKind	string	非必须		企业性质	
├─ comAdress	string	非必须		公司地址	
├─ registCapi	string	非必须		注册资本	
├─ scope	string	非必须		经营范围	
├─ operName	string	非必须		企业法人	
├─ comType	string	非必须		公司类型	
├─ employeesNum	string	非必须		员工数量	
├─ industry	string	非必须		行业	
├─ mainProduce	string	非必须		主营产品	
├─ comInfo	string	非必须		公司简介	
├─ collaborators	string []	非必须		协作人的用户Id数组	
item 类型: string

├─		非必须			
├─ customFieldValues	object []	非必须		自定义字段	
item 类型: object

├─ id	number	必须		自定义字段id	
├─ values	string []	必须		自定义字段的值（数组），如果多选字段可传多个，文本类型的值长度不能超过400	
item 类型: string

├─		非必须		自定义字段的值	
├─ fieldType	string	必须		自定义字段类型 ，取自 查询自定义字段设置（/open/customer/customFields） 接口的type字段	
├─ multiContacts	object []	非必须		当fieldType=6 既其他联系方式类型时，传此参数	
item 类型: object

├─ optionId	string	必须		取自 查询自定义字段设置（/open/customer/customFields） 接口的options选项数据的id	
├─ optionText	string	必须		具体其他联系方式号码	
返回数据
名称	类型	是否必须	默认值	备注	其他信息
success	boolean	必须		成功或失败（数据为空时返回false）	
message	string	必须		成功或失败信息	
data	object	必须		新增结果	
├─ success	object	必须		新增成功的客户的map集合（字段参照接口描述）	
├─ 10001	string	非必须			
├─ failed	object	非必须		新增失败的客户的map集合（字段参照接口描述）	
├─ 10002	string	非必须			
