# 用友API 403 Forbidden 错误排查指南

## 问题现象
Java代码调用用友API返回 `403 Forbidden` 错误，但Postman调用相同接口成功。

## 可能原因分析

### 1. 签名计算错误 ⭐⭐⭐⭐⭐
**最常见原因**

#### 检查项目：
- [ ] 签名算法是否正确：`md5(signKey + company + partnerId + timestamp).toUpperCase()`
- [ ] 参数拼接顺序是否正确
- [ ] MD5结果是否转换为大写
- [ ] 时间戳是否为毫秒级

#### 验证方法：
```bash
# 访问签名验证接口
GET http://localhost:8080/yongYou/test/validateSign

# 查看签名调试信息
GET http://localhost:8080/yongYou/test/signDebugInfo
```

### 2. 请求头参数错误 ⭐⭐⭐⭐
#### 检查项目：
- [ ] 请求头参数名称是否正确：`timestamp`, `partnerId`, `company`, `sign`
- [ ] 参数值是否为空
- [ ] Content-Type是否为 `application/json`

#### 验证方法：
```bash
# 验证配置参数
GET http://localhost:8080/yongYou/test/validateConfig
```

### 3. 时间戳问题 ⭐⭐⭐
#### 检查项目：
- [ ] 时间戳是否为毫秒级（13位数字）
- [ ] 服务器时间与用友服务器时间差是否超过1分钟

#### 验证方法：
```java
long timestamp = System.currentTimeMillis();
System.out.println("当前时间戳: " + timestamp);
System.out.println("时间戳长度: " + String.valueOf(timestamp).length());
```

### 4. 请求体格式错误 ⭐⭐⭐
#### 检查项目：
- [ ] 请求体是否为JSON格式
- [ ] 是否使用了单引号（应该使用双引号）
- [ ] 请求体结构是否符合API文档要求

#### 验证方法：
```bash
# 比较两种请求格式
GET http://localhost:8080/yongYou/test/compareFormats

# 测试两种格式
GET http://localhost:8080/yongYou/test/testBothFormats
```

### 5. 配置参数错误 ⭐⭐
#### 检查项目：
- [ ] signKey是否正确
- [ ] partnerId是否正确
- [ ] company是否正确
- [ ] baseUrl是否正确

## 排查步骤

### 第一步：验证基础配置
```bash
GET http://localhost:8080/yongYou/test/validateConfig
```

### 第二步：测试签名生成
```bash
GET http://localhost:8080/yongYou/test/testSign
```

### 第三步：比较请求格式
```bash
GET http://localhost:8080/yongYou/test/compareFormats
```

### 第四步：完整调试
```bash
GET http://localhost:8080/yongYou/test/fullDebug
```

## 常见解决方案

### 1. 签名问题解决
```java
// 确保签名计算正确
String signStr = signKey + company + partnerId + timestamp;
String sign = Md5Util.encode(signStr).toUpperCase();
```

### 2. 时间戳问题解决
```java
// 使用毫秒级时间戳
long timestamp = System.currentTimeMillis();
```

### 3. 请求头问题解决
```java
headers.set("timestamp", String.valueOf(timestamp));
headers.set("partnerId", partnerId);
headers.set("company", company);
headers.set("sign", sign);
headers.setContentType(MediaType.APPLICATION_JSON);
```

### 4. 请求体格式问题解决
根据API文档，请求体应该是数组格式：
```json
[
  {
    "customerId": "806913807219281",
    "name": "王金龙",
    "phone": "13693033890",
    "company": "测试王金龙"
  }
]
```

但如果Postman使用单个对象成功，可能需要调整为：
```json
{
  "customerId": "806913807219281",
  "name": "王金龙", 
  "phone": "13693033890",
  "company": "测试王金龙"
}
```

## 调试工具

### 1. 测试接口列表
- `GET /yongYou/test/addCustomer` - 测试添加客户
- `GET /yongYou/test/validateConfig` - 验证配置
- `GET /yongYou/test/testSign` - 测试签名
- `GET /yongYou/test/compareFormats` - 比较请求格式
- `GET /yongYou/test/fullDebug` - 完整调试
- `GET /yongYou/test/signDebugInfo` - 签名调试信息

### 2. 日志查看
查看应用日志，关注以下信息：
- 签名计算过程
- 请求头详情
- 请求体内容
- HTTP响应状态码和内容

### 3. 与Postman对比
1. 记录Postman的请求头和请求体
2. 记录Java代码的请求头和请求体
3. 逐项对比差异

## 特殊情况处理

### 1. 如果所有验证都通过但仍然403
可能是用友服务器的特殊限制：
- IP白名单限制
- 用户权限问题
- API版本问题

### 2. 如果Postman成功但代码失败
重点检查：
- User-Agent差异
- 请求体编码问题
- HTTP客户端配置差异

## 最终验证

当所有问题修复后，使用以下接口进行最终验证：
```bash
GET http://localhost:8080/yongYou/test/addCustomer
```

如果返回成功，说明问题已解决。如果仍然失败，请查看详细日志进行进一步分析。

## 联系支持

如果按照本指南仍无法解决问题，请提供以下信息：
1. 完整的错误日志
2. 签名调试信息
3. 请求头和请求体详情
4. Postman的成功请求截图
