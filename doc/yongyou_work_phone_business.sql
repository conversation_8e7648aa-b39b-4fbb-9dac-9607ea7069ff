----DROP TABLE UBADMA.YONGYOU_WORK_PHONE_BUSINESS ;
CREATE TABLE UBADMA.YONGYOU_WORK_PHONE_BUSINESS (
    ID NUMBER(12,0) NOT NULL,
    OPTIMIST<PERSON> NUMBER(12,0) DEFAULT 0,
    CUSTOMER_NO VARCHAR2(30 CHAR),
    FULL_NAME VARCHAR2(100 CHAR),
    REAL_NAME VARCHAR2(100 CHAR),
    PHONE_NO VARCHAR2(100 CHAR),
    ORGAN_CODE VARCHAR2(20 CHAR),
    VERIFY_STATUS VARCHAR2(20 CHAR),
    VERIFY_RETURN_NUM NUMBER(5,0),
    VERIFY_RETURN_VALUE VARCHAR2(20 CHAR),
    VERIFY_RETURN_REMARK VARCHAR2(500 CHAR),
    YONGYOU_PUSH_STATUS VARCHAR2(20 CHAR),
    PUSH_RETURN_NUM NUMBER(5,0),
    PUSH_RETURN_VALUE VARCHAR2(20 CHAR),
    PUSH_RETURN_MESSAGE VARCHAR2(500 CHAR),
    OPERATOR VARCHAR2(100 CHAR),
    CREATE_TIME DATE DEFAULT sysdate,
    UPDATE_TIME DATE DEFAULT sysdate,
    BRAND VARCHAR2(20 CHAR) DEFAULT 'PLUS' NOT NULL

) TABLESPACE TBS_PAY_CORE_DATA;

ALTER TABLE UBADMA.YONGYOU_WORK_PHONE_BUSINESS ADD CONSTRAINT PK_YONGYOU_WORK_PHONE_BUSINESS PRIMARY KEY (ID) USING INDEX TABLESPACE TBS_PAY_CORE_DATA;

CREATE INDEX UBADMA.IX_YYWPB_CT_01 ON UBADMA.YONGYOU_WORK_PHONE_BUSINESS (CREATE_TIME ASC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_YYWPB_CN_02 ON UBADMA.YONGYOU_WORK_PHONE_BUSINESS (CUSTOMER_NO ASC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_YYWPB_PN_03 ON UBADMA.YONGYOU_WORK_PHONE_BUSINESS (PHONE_NO ASC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE UNIQUE INDEX UBADMA.IX_YYWPB_CP_04 ON UBADMA.YONGYOU_WORK_PHONE_BUSINESS (CUSTOMER_NO, PHONE_NO) TABLESPACE TBS_PAY_CORE_DATA;


COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.ID IS '主键ID';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.OPTIMISTIC IS '乐观锁版本号';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.CUSTOMER_NO IS '商户编号';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.FULL_NAME IS '商户名称';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.REAL_NAME IS '真实姓名';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.PHONE_NO IS '手机号';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.ORGAN_CODE IS '所属地区';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.VERIFY_STATUS IS '验证状态';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.VERIFY_RETURN_VALUE IS '验证返回值';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.VERIFY_RETURN_NUM IS '验证次数';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.VERIFY_RETURN_REMARK IS '验证返回备注';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.YONGYOU_PUSH_STATUS IS '用友推送状态';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.PUSH_RETURN_NUM IS '推返次数';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.PUSH_RETURN_VALUE IS '推送返回值';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.PUSH_RETURN_MESSAGE IS '推送返回信息';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.OPERATOR IS '操作人';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN UBADMA.YONGYOU_WORK_PHONE_BUSINESS.BRAND IS '品牌';
COMMENT ON TABLE UBADMA.YONGYOU_WORK_PHONE_BUSINESS IS '用友工作手机业务表';

-- 创建序列
CREATE SEQUENCE UBADMA.SEQ_YONGYOU_WORK_PHONE_ID
    START WITH 1
    INCREMENT BY 1
    NOMAXVALUE
    NOCYCLE
    CACHE 20;

GRANT SELECT,UPDATE,INSERT,DELETE ON UBADMA.YONGYOU_WORK_PHONE_BUSINESS TO UPCOREA;

CREATE SEQUENCE UBADMA.SEQ_YONGYOU_WORK_PHONE_ID MINVALUE 1 MAXVALUE 999999999999 START WITH 1 INCREMENT BY 1 CACHE 20 CYCLE;
grant select on UBADMA.SEQ_YONGYOU_WORK_PHONE_ID to upcorea;
