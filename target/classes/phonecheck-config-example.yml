# 号码检测功能配置示例
# 将此配置添加到 application.yml 中

phonecheck:
  # 第三方API配置
  api:
    # API基础地址（如果为空则使用模拟检测）
    baseUrl: ""
    # 应用Key
    appKey: ""
    # 应用密钥
    appSecret: ""
    # 请求超时时间（毫秒）
    timeout: 30000
    # 空号检测接口路径
    emptyNumberUrl: "/api/phone/empty"
    # 实名检测接口路径
    realNameUrl: "/api/phone/realname"
    # 风控检测接口路径
    riskControlUrl: "/api/phone/risk"
  
  # 缓存配置
  cache:
    # 缓存有效期（小时）
    validHours: 24
  
  # 批量处理配置
  batch:
    # 单次最大检测数量
    maxSize: 100

# 数据库连接池配置（如果需要单独配置）
spring:
  datasource:
    # 连接池配置
    hikari:
      # 最大连接数
      maximum-pool-size: 20
      # 最小空闲连接数
      minimum-idle: 5
      # 连接超时时间
      connection-timeout: 30000
      # 空闲超时时间
      idle-timeout: 600000
      # 最大生命周期
      max-lifetime: 1800000

# 日志配置
logging:
  level:
    # 号码检测相关日志级别
    com.pay.tp.core.phonecheck: INFO
    com.pay.tp.core.remote.phonecheck: DEBUG
    com.pay.tp.core.biz.impl.PhoneCheckBiz: INFO
    com.pay.tp.core.controller.phonecheck: INFO

# MyBatis配置
mybatis:
  # 映射文件位置
  mapper-locations: classpath:mybatis/*.xml
  # 类型别名包
  type-aliases-package: com.pay.tp.core.entity
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 延迟加载
    lazy-loading-enabled: true

# 分页插件配置
pagehelper:
  # 数据库类型
  helper-type: oracle
  # 合理化参数
  reasonable: true
  # 支持通过Mapper接口参数来传递分页参数
  support-methods-arguments: true
  # 分页参数合理化
  params: count=countSql

# 线程池配置（用于异步处理）
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 5
        # 最大线程数
        max-size: 20
        # 队列容量
        queue-capacity: 100
        # 线程名前缀
        thread-name-prefix: "phonecheck-"
        # 空闲时间
        keep-alive: 60s

# 监控配置（如果使用Spring Boot Actuator）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 第三方API提供商配置示例
# 示例1: 阿里云号码认证服务
aliyun:
  phonecheck:
    accessKeyId: "your-access-key-id"
    accessKeySecret: "your-access-key-secret"
    regionId: "cn-hangzhou"
    endpoint: "https://dytnsapi.aliyuncs.com"

# 示例2: 腾讯云号码认证服务
tencent:
  phonecheck:
    secretId: "your-secret-id"
    secretKey: "your-secret-key"
    region: "ap-beijing"
    endpoint: "https://sms.tencentcloudapi.com"

# 示例3: 百度云号码认证服务
baidu:
  phonecheck:
    apiKey: "your-api-key"
    secretKey: "your-secret-key"
    endpoint: "https://aip.baidubce.com"

# Redis配置（如果使用Redis作为缓存）
spring:
  redis:
    host: localhost
    port: 6379
    password: ""
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms

# 定时任务配置（用于数据清理）
phonecheck:
  cleanup:
    # 是否启用定时清理
    enabled: true
    # 清理周期（cron表达式）
    cron: "0 0 2 * * ?"
    # 数据保留天数
    retentionDays: 90

# 限流配置（防止API滥用）
phonecheck:
  ratelimit:
    # 是否启用限流
    enabled: true
    # 每分钟最大请求数
    maxRequestsPerMinute: 1000
    # 每小时最大请求数
    maxRequestsPerHour: 10000
    # 每天最大请求数
    maxRequestsPerDay: 100000

# 安全配置
phonecheck:
  security:
    # 是否启用IP白名单
    ipWhitelistEnabled: false
    # IP白名单
    ipWhitelist:
      - "127.0.0.1"
      - "***********/24"
    # 是否启用签名验证
    signatureEnabled: false
    # 签名密钥
    signatureKey: "your-signature-key"

# 告警配置
phonecheck:
  alert:
    # 是否启用告警
    enabled: true
    # API失败率阈值（百分比）
    failureRateThreshold: 10
    # 响应时间阈值（毫秒）
    responseTimeThreshold: 5000
    # 告警接收邮箱
    emailRecipients:
      - "<EMAIL>"
      - "<EMAIL>"
