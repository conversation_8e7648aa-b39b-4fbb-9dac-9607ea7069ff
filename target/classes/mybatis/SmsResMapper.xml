<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.sms.SmsResMapper">


    <select id="query" resultType="com.pay.tp.core.entity.sms.SmsRes">
        select
          ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, PHONE, STATUS, USER_RECEIVE_TIME, CHANNEL_NO
        from UBADMA.SMS_RES
        where REQUEST_NO = #{requestNo} and CHANNEL_NO = #{channelNo}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.sms.SmsRes">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_SMS_RES_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.SMS_RES (ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, PHONE, STATUS, USER_RECEIVE_TIME,
        CHANNEL_NO)
        values (#{id,jdbcType=DECIMAL},0,#{createTime}, #{sys}, #{requestNo}, #{phone}, #{status}, #{userReceiveTime},
        #{channelNo})
    </insert>

</mapper>