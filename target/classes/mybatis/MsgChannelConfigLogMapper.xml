<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.msgmanage.MsgChannelConfigLogMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.msgmanage.MsgChannelConfigLog">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CHANNEL_CONFIG_ID" jdbcType="DECIMAL" property="channelConfigId"/>
        <result column="BEFORE" jdbcType="VARCHAR" property="before"/>
        <result column="AFTER" jdbcType="VARCHAR" property="after"/>
        <result column="OPERATOR" jdbcType="VARCHAR" property="operator"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, CREATE_TIME, CHANNEL_CONFIG_ID, "BEFORE", "AFTER", "OPERATOR"
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL_CONFIG_LOG
        where ID = #{id,jdbcType=DECIMAL}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.msgmanage.MsgChannelConfigLog">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_MSG_CHANNEL_CONFIG_LOG_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.MSG_CHANNEL_CONFIG_LOG (ID, CREATE_TIME, CHANNEL_CONFIG_ID, "BEFORE",
        "AFTER", "OPERATOR")
        values (#{id,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{channelConfigId,jdbcType=DECIMAL},
        #{before,jdbcType=VARCHAR},
        #{after,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR})
    </insert>
</mapper>