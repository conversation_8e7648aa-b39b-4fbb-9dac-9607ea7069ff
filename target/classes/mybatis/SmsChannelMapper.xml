<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.sms.SmsChannelMapper">


    <select id="findActive" resultType="com.pay.tp.core.entity.sms.SmsChannel">
        select
          ID, OPTIMISTIC, CREATE_TIME, CODE, NAME, STATUS, CHANNEL_TYPE
        from UBADMA.SMS_CHANNEL
        where STATUS = 'ENABLE' and CHANNEL_TYPE = #{channelType}
        ORDER BY WEIGHT
    </select>
    
    <select id="findByStatus" resultType="com.pay.tp.core.entity.sms.SmsChannel">
        select
          ID, OPTIMISTIC, CREATE_TIME, CODE, NAME, STATUS, CHANNEL_TYPE
        from UBADMA.SMS_CHANNEL
        where STATUS = #{status}  and CHANNEL_TYPE = #{channelType}
    </select>

    <select id="findById" resultType="com.pay.tp.core.entity.sms.SmsChannel">
        select
          ID, OPTIMISTIC, CREATE_TIME, CODE, NAME, STATUS, CHANNEL_TYPE
        from UBADMA.SMS_CHANNEL
        where ID = #{id}
    </select>

    <update id="updateStatusById">
        UPDATE UBADMA.SMS_CHANNEL SET STATUS = #{status} WHERE ID = #{id}
    </update>

    <update id="updateDisableAll">
        UPDATE UBADMA.SMS_CHANNEL SET STATUS = 'DISABLE' WHERE STATUS = 'ENABLE' and CHANNEL_TYPE = #{channelType}
    </update>

    <select id="findAll" resultType="com.pay.tp.core.entity.sms.SmsChannel">
        select
          ID, OPTIMISTIC, CREATE_TIME, CODE, NAME, STATUS, CHANNEL_TYPE
        from UBADMA.SMS_CHANNEL
    </select>

</mapper>