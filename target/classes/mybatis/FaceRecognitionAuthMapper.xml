<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.auth.FaceRecognitionAuthMapper">

    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.auth.FaceRecognitionAuth">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTISMISTIC" jdbcType="DECIMAL" property="optismistic"/>
        <result column="REQUEST_NO" jdbcType="VARCHAR" property="requestNo"/>
        <result column="CUSTOMER_NO" jdbcType="VARCHAR" property="customerNo"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="CID_NO" jdbcType="VARCHAR" property="cidNo"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="TRADE_NO" jdbcType="VARCHAR" property="tradeNo"/>
        <result column="RSP_COD" jdbcType="VARCHAR" property="rspCod"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="RSP_MSG" jdbcType="VARCHAR" property="rspMsg"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTISMISTIC, REQUEST_NO, CUSTOMER_NO, "NAME", CID_NO, TRADE_NO, RSP_COD, CODE,
    RSP_MSG, REMARK, CREATE_TIME, UPDATE_TIME, "STATUS"
  </sql>


    <insert id="insert" parameterType="com.pay.tp.core.entity.auth.FaceRecognitionAuth">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_TP_FACE_RECO_AUTH_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.TP_FACE_RECOGNITION_AUTH (ID, OPTISMISTIC, REQUEST_NO,
        CUSTOMER_NO, NAME, CID_NO,
        TRADE_NO, RSP_COD, CODE,
        RSP_MSG, REMARK, CREATE_TIME,STATUS,
        UPDATE_TIME)
        values (#{id,jdbcType=DECIMAL}, 0 , #{requestNo,jdbcType=VARCHAR},
        #{customerNo,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{tradeNo,jdbcType=VARCHAR}, #{rspCod,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
        #{rspMsg,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, SYSDATE,#{status,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateById" parameterType="com.pay.tp.core.entity.auth.FaceRecognitionAuth">
			update UBADMA.TP_FACE_RECOGNITION_AUTH
			set OPTISMISTIC = OPTISMISTIC + 1,
			REQUEST_NO = #{requestNo,jdbcType=VARCHAR},
			CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
			NAME = #{name,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
			CID_NO = #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
			TRADE_NO = #{tradeNo,jdbcType=VARCHAR},
			RSP_COD = #{rspCod,jdbcType=VARCHAR},
			CODE = #{code,jdbcType=VARCHAR},
			RSP_MSG = #{rspMsg,jdbcType=VARCHAR},
			REMARK = #{remark,jdbcType=VARCHAR},
			STATUS = #{status,jdbcType=VARCHAR},
			UPDATE_TIME = SYSDATE
			where ID = #{id,jdbcType=DECIMAL}
		</update>

    <select id="queryByRequestNo" resultMap="BaseResultMap"
            parameterType="com.pay.tp.core.entity.auth.FaceRecognitionAuth">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_FACE_RECOGNITION_AUTH
        where
        REQUEST_NO = #{requestNo}
    </select>

    <select id="query" resultMap="BaseResultMap"
            parameterType="com.pay.tp.core.entity.auth.FaceRecognitionAuth">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_FACE_RECOGNITION_AUTH
        where CUSTOMER_NO = #{customerNo}
        and CID_NO = #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        and NAME = #{name,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        and RSP_COD = '0'
        and CODE = '01'
        order by CREATE_TIME desc
    </select>


</mapper>