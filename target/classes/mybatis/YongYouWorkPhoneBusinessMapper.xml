<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper">

    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.yongyou.YongYouWorkPhoneBusiness">
        <id column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="OPTIMISTIC" property="optimistic" jdbcType="DECIMAL"/>
        <result column="CUSTOMER_NO" property="customerNo" jdbcType="VARCHAR"/>
        <result column="FULL_NAME" property="fullName" jdbcType="VARCHAR"/>
        <result column="REAL_NAME" property="realName" jdbcType="VARCHAR"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="PHONE_NO" property="phoneNo" jdbcType="VARCHAR"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="ORGAN_CODE" property="organCode" jdbcType="VARCHAR"/>
        <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="VARCHAR"/>
        <result column="VERIFY_RETURN_NUM" property="verifyReturnNum" jdbcType="DECIMAL"/>
        <result column="VERIFY_RETURN_VALUE" property="verifyReturnValue" jdbcType="VARCHAR"/>
        <result column="VERIFY_RETURN_REMARK" property="verifyReturnRemark" jdbcType="VARCHAR"/>
        <result column="YONGYOU_PUSH_STATUS" property="yongyouPushStatus" jdbcType="VARCHAR"/>
        <result column="PUSH_RETURN_NUM" property="pushReturnNum" jdbcType="DECIMAL"/>
        <result column="PUSH_RETURN_VALUE" property="pushReturnValue" jdbcType="VARCHAR"/>
        <result column="PUSH_RETURN_MESSAGE" property="pushReturnMessage" jdbcType="VARCHAR"/>
        <result column="OPERATOR" property="operator" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="BRAND" property="brand" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        , OPTIMISTIC, CUSTOMER_NO, FULL_NAME, REAL_NAME, PHONE_NO, ORGAN_CODE,
        VERIFY_STATUS, VERIFY_RETURN_NUM, VERIFY_RETURN_VALUE, VERIFY_RETURN_REMARK,
        YONGYOU_PUSH_STATUS, PUSH_RETURN_NUM, PUSH_RETURN_VALUE, PUSH_RETURN_MESSAGE,
        OPERATOR, CREATE_TIME, UPDATE_TIME, BRAND
    </sql>

    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM UBADMA.YONGYOU_WORK_PHONE_BUSINESS
        WHERE ID = #{id,jdbcType=DECIMAL}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.yongyou.YongYouWorkPhoneBusiness">
        INSERT INTO UBADMA.YONGYOU_WORK_PHONE_BUSINESS (ID, OPTIMISTIC, CUSTOMER_NO, FULL_NAME, REAL_NAME, PHONE_NO,
                                                        ORGAN_CODE,
                                                        VERIFY_STATUS, VERIFY_RETURN_NUM, VERIFY_RETURN_VALUE,
                                                        VERIFY_RETURN_REMARK,
                                                        YONGYOU_PUSH_STATUS, PUSH_RETURN_NUM, PUSH_RETURN_VALUE,
                                                        PUSH_RETURN_MESSAGE,
                                                        OPERATOR, CREATE_TIME, UPDATE_TIME, BRAND)
        VALUES (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, #{customerNo,jdbcType=VARCHAR},
                #{fullName,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, #{phoneNo,jdbcType=VARCHAR},
                #{organCode,jdbcType=VARCHAR}, #{verifyStatus,jdbcType=VARCHAR}, #{verifyReturnNum,jdbcType=DECIMAL},
                #{verifyReturnValue,jdbcType=VARCHAR}, #{verifyReturnRemark,jdbcType=VARCHAR},
                #{yongyouPushStatus,jdbcType=VARCHAR}, #{pushReturnNum,jdbcType=DECIMAL},
                #{pushReturnValue,jdbcType=VARCHAR}, #{pushReturnMessage,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{brand,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.yongyou.YongYouWorkPhoneBusiness">
        UPDATE UBADMA.YONGYOU_WORK_PHONE_BUSINESS
        SET OPTIMISTIC           = OPTIMISTIC + 1,
            CUSTOMER_NO          = #{customerNo,jdbcType=VARCHAR},
            FULL_NAME            = #{fullName,jdbcType=VARCHAR},
            REAL_NAME            = #{realName,jdbcType=VARCHAR},
            PHONE_NO             = #{phoneNo,jdbcType=VARCHAR},
            ORGAN_CODE           = #{organCode,jdbcType=VARCHAR},
            VERIFY_STATUS        = #{verifyStatus,jdbcType=VARCHAR},
            VERIFY_RETURN_NUM    = #{verifyReturnNum,jdbcType=DECIMAL},
            VERIFY_RETURN_VALUE  = #{verifyReturnValue,jdbcType=VARCHAR},
            VERIFY_RETURN_REMARK = #{verifyReturnRemark,jdbcType=VARCHAR},
            YONGYOU_PUSH_STATUS  = #{yongyouPushStatus,jdbcType=VARCHAR},
            PUSH_RETURN_NUM      = #{pushReturnNum,jdbcType=DECIMAL},
            PUSH_RETURN_VALUE    = #{pushReturnValue,jdbcType=VARCHAR},
            PUSH_RETURN_MESSAGE  = #{pushReturnMessage,jdbcType=VARCHAR},
            OPERATOR             = #{operator,jdbcType=VARCHAR},
            UPDATE_TIME          = SYSDATE
        WHERE ID = #{id,jdbcType=DECIMAL}
          and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
    </update>

    <select id="findPendingPushRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM UBADMA.YONGYOU_WORK_PHONE_BUSINESS
        WHERE YONGYOU_PUSH_STATUS IN ('INIT', 'FAIL')
        AND PUSH_RETURN_NUM &lt; 3
        AND VERIFY_STATUS = 'SUCCESS'
        AND ROWNUM &lt;= #{limit}
        ORDER BY CREATE_TIME ASC
    </select>

    <update id="updatePushStatus">
        UPDATE UBADMA.YONGYOU_WORK_PHONE_BUSINESS
        SET YONGYOU_PUSH_STATUS = #{pushStatus,jdbcType=VARCHAR},
            PUSH_RETURN_VALUE   = #{pushReturnValue,jdbcType=VARCHAR},
            PUSH_RETURN_MESSAGE = #{pushReturnMessage,jdbcType=VARCHAR},
            UPDATE_TIME         = SYSDATE,
            OPTIMISTIC          = OPTIMISTIC + 1
        WHERE ID = #{id,jdbcType=DECIMAL}
    </update>

    <update id="incrementPushReturnNum">
        UPDATE UBADMA.YONGYOU_WORK_PHONE_BUSINESS
        SET PUSH_RETURN_NUM = PUSH_RETURN_NUM + 1,
            UPDATE_TIME     = SYSDATE,
            OPTIMISTIC      = OPTIMISTIC + 1
        WHERE ID = #{id,jdbcType=DECIMAL}
    </update>
    <update id="batchIncrementPushReturnNum">
        UPDATE UBADMA.YONGYOU_WORK_PHONE_BUSINESS
        SET PUSH_RETURN_NUM = PUSH_RETURN_NUM + 1,
        UPDATE_TIME = SYSDATE,
        OPTIMISTIC = OPTIMISTIC + 1
        WHERE ID IN
        <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </update>
    <update id="updatePushStatusBatch">
        UPDATE UBADMA.YONGYOU_WORK_PHONE_BUSINESS
        SET YONGYOU_PUSH_STATUS = #{pushStatus,jdbcType=VARCHAR},
        PUSH_RETURN_VALUE = #{returnValue,jdbcType=VARCHAR},
        PUSH_RETURN_MESSAGE = #{returnMessage,jdbcType=VARCHAR},
        UPDATE_TIME = SYSDATE,
        OPTIMISTIC = OPTIMISTIC + 1
        WHERE id IN
        <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </update>

</mapper>
