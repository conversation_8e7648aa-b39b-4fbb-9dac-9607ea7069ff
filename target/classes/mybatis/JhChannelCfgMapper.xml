<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pay.tp.core.mapper.jh.JhChannelCfgMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.jh.JhChannelCfg">
        <id column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="OPTIMISTIC" property="optimistic" jdbcType="DECIMAL"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="APP_ID" property="appId" jdbcType="VARCHAR"/>
        <result column="CHANNEL_NO" property="channelNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="VARCHAR"/>
        <result column="AUTH_URL" property="authUrl" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="OPERATOR" property="operator" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="ACCESS_TOKEN" property="accessToken" jdbcType="VARCHAR"/>
        <result column="APP_SECRET" property="appSecret" jdbcType="VARCHAR"/>
        <result column="BRAND" property="brand" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID,OPTIMISTIC,STATUS,APP_ID,CHANNEL_NO,CHANNEL_TYPE,AUTH_URL,CREATE_TIME,UPDATE_TIME,OPERATOR,REMARK,
    ACCESS_TOKEN,APP_SECRET,BRAND
    </sql>
    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.JH_CHANNEL_CFG
        where id= #{id,jdbcType=DECIMAL}
    </select>
    <select id="findByPageAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.JH_CHANNEL_CFG
        where 1=1
        <if test="status != null and  status !=''">
            and STATUS = #{status,jdbcType=VARCHAR}
        </if>
        <if test="appId != null and  appId !=''">
            and APP_ID = #{appId,jdbcType=VARCHAR}
        </if>
        <if test="channelNo != null and  channelNo !=''">
            and CHANNEL_NO = #{channelNo,jdbcType=VARCHAR}
        </if>
        <if test="channelType != null and  channelType !=''">
            and CHANNEL_TYPE = #{channelType,jdbcType=VARCHAR}
        </if>
        <if test="createTimeStart != null and  createTimeStart !=''">
            AND TRUNC(CREATE_TIME,'DD') &gt;= to_date(#{createTimeStart,jdbcType=VARCHAR},'yyyy-MM-dd')
        </if>
        <if test="createTimeEnd != null and  createTimeEnd !=''">
            AND TRUNC(CREATE_TIME,'DD') &lt;= to_date(#{createTimeStart,jdbcType=VARCHAR},'yyyy-MM-dd')
        </if>
        ORDER BY ID DESC
    </select>
    <insert id="insert" parameterType="com.pay.tp.core.entity.jh.JhChannelCfg">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_JH_CHANNEL_CFG_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.JH_CHANNEL_CFG (ID,OPERATOR,STATUS,APP_ID,CHANNEL_NO,CHANNEL_TYPE,
        AUTH_URL,CREATE_TIME,UPDATE_TIME,REMARK,ACCESS_TOKEN,APP_SECRET,BRAND,OPTIMISTIC)
        values (#{id,jdbcType=DECIMAL},#{operator,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR}
                ,#{appId,jdbcType=VARCHAR},#{channelNo,jdbcType=VARCHAR},#{channelType,jdbcType=VARCHAR}
                ,#{authUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{remark,jdbcType=VARCHAR},#{accessToken,jdbcType=VARCHAR}
                ,#{appSecret,jdbcType=VARCHAR},#{brand,jdbcType=VARCHAR},1)
    </insert>
    <update id="update" parameterType="com.pay.tp.core.entity.jh.JhChannelCfg">
        UPDATE UBADMA.JH_CHANNEL_CFG
        SET OPTIMISTIC = OPTIMISTIC + 1,
            STATUS = #{status,jdbcType = VARCHAR},
            APP_ID = #{appId,jdbcType = VARCHAR},
            CHANNEL_NO = #{channelNo,jdbcType = VARCHAR},
            CHANNEL_TYPE = #{channelType,jdbcType = VARCHAR},
            AUTH_URL = #{authUrl,jdbcType = VARCHAR},
            CREATE_TIME = #{createTime,jdbcType = TIMESTAMP},
            UPDATE_TIME = SYSDATE,
            OPERATOR = #{operator,jdbcType = VARCHAR},
            REMARK = #{remark,jdbcType = VARCHAR},
            ACCESS_TOKEN = #{accessToken,jdbcType = VARCHAR},
            APP_SECRET = #{appSecret,jdbcType = VARCHAR},
            BRAND = #{brand,jdbcType = VARCHAR}
    WHERE ID = #{id,jdbcType=DECIMAL}
</update>
<update id="updateAccessToken">
    update UBADMA.JH_CHANNEL_CFG
    set OPTIMISTIC  = OPTIMISTIC + 1,
        UPDATE_TIME = SYSDATE,
        ACCESS_TOKEN      = #{accessToken,jdbcType=VARCHAR}
    where APP_ID = #{appId,jdbcType=VARCHAR}
    and APP_SECRET = #{appSecret,jdbcType=VARCHAR}
</update>
</mapper>