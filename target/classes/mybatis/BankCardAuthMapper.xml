<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.auth.BankCardAuthMapper">

    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.auth.BankcardAuth">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTISMISTIC" jdbcType="DECIMAL" property="optismistic"/>
        <result column="CREATETIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="INSID" jdbcType="VARCHAR" property="insId"/>
        <result column="OPERID" jdbcType="VARCHAR" property="operId"/>
        <result column="CARDNO" jdbcType="VARCHAR" property="cardNo"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="COOPERSERIALNO" jdbcType="VARCHAR" property="cooperSerialNo"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="CIDNO" jdbcType="VARCHAR" property="cidNo"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="RSPCOD" jdbcType="VARCHAR" property="rspCod"/>
        <result column="RSPMSG" jdbcType="VARCHAR" property="rspMsg"/>
        <result column="EXECTYPE" jdbcType="VARCHAR" property="execType"/>
        <result column="VALIDATESTATUS" jdbcType="VARCHAR" property="validateStatus"/>
        <result column="BANKNAME" jdbcType="VARCHAR" property="bankName"/>
        <result column="ORITRANSDATE" jdbcType="TIMESTAMP" property="oriTransDate"/>
        <result column="PAYSERIALNO" jdbcType="VARCHAR" property="paySerialNo"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="CARDTYPE" jdbcType="VARCHAR" property="cardType"/>
        <result column="RESV" jdbcType="VARCHAR" property="resv"/>
        <result column="REQUEST_NO" jdbcType="VARCHAR" property="requestNo"/>
        <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName"/>
        <result column="CHANNEL_BRAND" jdbcType="VARCHAR" property="channelBrand"/>
        <result column="CHANNEL_RSPCOD" jdbcType="VARCHAR" property="channelRspCod"/>
 		<result column="BRAND" property="brand" jdbcType="VARCHAR" />
 		<result column="OWNER_NO" property="ownerNo" jdbcType="VARCHAR" />
 		<result column="AUTH_TYPE" property="authType" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTISMISTIC, CREATETIME, INSID, OPERID, CARDNO, COOPERSERIALNO, "NAME", CIDNO,
    MOBILE, RSPCOD, RSPMSG, EXECTYPE, VALIDATESTATUS, BANKNAME, ORITRANSDATE, PAYSERIALNO,
    REMARK, CARDTYPE, RESV, REQUEST_NO, CUSTOMER_NAME, CHANNEL_BRAND, CHANNEL_RSPCOD,BRAND,
    OWNER_NO, AUTH_TYPE
  </sql>

    <select id="queryEncrypt" resultMap="BaseResultMap"
            parameterType="com.pay.tp.core.beans.auth.BankCardAuthParam">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_BANKCARD_AUTH
        where
        CARDNO = #{cardNo,jdbcType=VARCHAR}
        and NAME = #{name,jdbcType=VARCHAR}
        and CIDNO = #{cidNo,jdbcType=VARCHAR}
        <if test="mobile == null or mobile == ''">
            and MOBILE is NULL
        </if>
        <if test="mobile != null and mobile != ''">
            and MOBILE = #{mobile}
        </if>
        and RSPCOD = '000000' and VALIDATESTATUS='01'
        order by id desc
    </select>
    <select id="query" resultMap="BaseResultMap"
            parameterType="com.pay.tp.core.beans.auth.BankCardAuthParam">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_BANKCARD_AUTH
        where
        CARDNO = #{cardNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        and NAME = #{name,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        and CIDNO = #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        <if test="mobile == null or mobile == ''">
            and MOBILE is NULL
        </if>
        <if test="mobile != null and mobile != ''">
            and MOBILE = #{mobile,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        </if>
        and RSPCOD = '000000' and VALIDATESTATUS='01'
        order by CREATETIME desc
    </select>


    <select id="findByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_BANKCARD_AUTH
        <where>
            1=1
            <if test="params.brand != null and params.brand != ''">
                and BRAND=#{params.brand}
            </if>
            <if test="params.ownerNo != null and params.ownerNo != ''">
                and OWNER_NO = #{params.ownerNo}
            </if>
            <if test="params.authType != null and params.authType != ''">
                and AUTH_TYPE = #{params.authType}
            </if>
            <if test="params.channelBrand != null and params.channelBrand != ''">
                and CHANNEL_BRAND = #{params.channelBrand}
            </if>
            <if test="params.requestNo != null and params.requestNo != ''">
                and REQUEST_NO=#{params.requestNo}
            </if>
            <if test="params.cardNo != null and params.cardNo != ''">
                and
                CARDNO=#{params.cardNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.name != null and params.name != ''">
                and
                NAME=#{params.name,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.cidNo != null and params.cidNo != ''">
                and
                CIDNO=#{params.cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.mobile != null and params.mobile != ''">
                and
                MOBILE=#{params.mobile,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.customerName != null and params.customerName != ''">
                and CUSTOMER_NAME=#{params.customerName}
            </if>
            <if test="params.date_start !=null and params.date_start !=''">
                and CREATETIME
                <![CDATA[ >= ]]>
                to_date(#{params.date_start} || '00:00:00','yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="params.date_end !=null and params.date_end !=''">
                and CREATETIME
                <![CDATA[ <= ]]>
                to_date(#{params.date_end} || '23:59:59','yyyy-mm-dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY CREATETIME DESC Nulls Last, ID DESC
    </select>


    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_BANKCARD_AUTH
        WHERE id=#{id}
    </select>


    <insert id="insert" parameterType="com.pay.tp.core.entity.auth.BankcardAuth">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_TP_BANKCARD_AUTH.nextval FROM dual
        </selectKey>
        insert into UBADMA.TP_BANKCARD_AUTH (ID,OPTISMISTIC,CREATETIME,
        CARDNO,CIDNO,MOBILE,NAME,
        REQUEST_NO,CUSTOMER_NAME, CHANNEL_BRAND,BRAND,INSID, OWNER_NO, AUTH_TYPE)
        values (#{id,jdbcType=DECIMAL},0, sysdate,
        #{cardNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{mobile,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{name,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{requestNo},
        #{customerName},
        #{channelBrand},
        #{brand},
        #{insId},
        #{ownerNo},
        #{authType}
        )
    </insert>
    <insert id="insertFromSdData" parameterType="com.pay.tp.core.entity.auth.BankcardAuth">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_TP_BANKCARD_AUTH_ID_SD.nextval FROM dual
        </selectKey>
        insert into UBADMA.TP_BANKCARD_AUTH (ID,CREATETIME,CARDNO,CIDNO,NAME,REQUEST_NO, 
        CHANNEL_BRAND,BRAND,VALIDATESTATUS,REMARK,RSPCOD, OWNER_NO, AUTH_TYPE)
        values (
        #{id,jdbcType=DECIMAL},
        #{createTime},
        #{cardNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{name,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{requestNo},
        'MOCK','PLUS','01','验证通过','000000',
        #{ownerNo}, #{authType}
        )
    </insert>

    <update id="update" parameterType="com.pay.tp.core.entity.auth.BankcardAuth">
		    update UBADMA.TP_BANKCARD_AUTH set
		    RSPCOD = #{rspCod,jdbcType=INTEGER},
		    RSPMSG=#{rspMsg,jdbcType=INTEGER},
		    EXECTYPE=#{execType,jdbcType=INTEGER},
		    BANKNAME =#{bankName},
		    VALIDATESTATUS=#{validateStatus,jdbcType=INTEGER},
		    ORITRANSDATE=to_date(#{oriTransDate}, 'yyyy/mm/dd'),
		    PAYSERIALNO=#{paySerialNo,jdbcType=INTEGER},
		    CHANNEL_RSPCOD=#{channelRspCod},
		    REMARK = #{remark}
		    where ID = #{id,jdbcType=INTEGER}
  		</update>


</mapper>