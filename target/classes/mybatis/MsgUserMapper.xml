<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.msgmanage.MsgUserMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.msgmanage.MsgUser">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="PHONE_NO" jdbcType="VARCHAR" property="phoneNo"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode"/>
        <result column="CHANNEL_USER_NO" jdbcType="VARCHAR" property="channelUserNo"/>
        <result column="USER_NO" jdbcType="VARCHAR" property="userNo"/>
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="GROUP_NO" jdbcType="VARCHAR" property="groupNo"/>
        <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName"/>
        <result column="DESC" jdbcType="VARCHAR" property="desc"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, PHONE_NO, CHANNEL_CODE, CHANNEL_USER_NO,
    USER_NO, USER_NAME, "STATUS", GROUP_NO, GROUP_NAME, "DESC"
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_USER
        where ID = #{id,jdbcType=DECIMAL}
    </select>

    <select id="findByChannelCodeAndGroupNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_USER
        where "STATUS" = 'ENABLE'
        and CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
        and GROUP_NO = #{groupNo,jdbcType=VARCHAR}
    </select>

    <select id="findByUserNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_USER
        where USER_NO = #{userNo,jdbcType=VARCHAR}
        and CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
    </select>
    <select id="findByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_USER
        <where>
            1=1
            <if test="params.channelCode != null and params.channelCode != ''">
                and CHANNEL_CODE=#{params.channelCode}
            </if>
            <if test="params.userNo != null and params.userNo != ''">
                and USER_NO=#{params.userNo}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and
                USER_NAME=#{params.userName,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.channelUserNo != null and params.channelUserNo != ''">
                and CHANNEL_USER_NO=#{params.channelUserNo}
            </if>
            <if test="params.status != null and params.status != ''">
                and "STATUS"=#{params.status}
            </if>
            <if test="params.phoneNo != null and params.phoneNo != ''">
                and
                PHONE_NO=#{params.phoneNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.groupNo != null and params.groupNo != ''">
                and GROUP_NO=#{params.groupNo}
            </if>
            <if test="params.groupName != null and params.groupName != ''">
                and GROUP_NAME=#{params.groupName}
            </if>
            <if test="params.dateStart !=null and params.dateStart !=''">
                and CREATE_TIME &gt;= to_date(#{params.dateStart},'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="params.dateEnd !=null and params.dateEnd !=''">
                and CREATE_TIME &lt;= to_date(#{params.dateEnd},'yyyy-MM-dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.msgmanage.MsgUser">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_MSG_USER_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.MSG_USER (ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME,
        PHONE_NO, CHANNEL_CODE, CHANNEL_USER_NO,
        USER_NO, USER_NAME, "STATUS",
        GROUP_NO, GROUP_NAME, "DESC"
        )
        values (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{phoneNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{channelCode,jdbcType=VARCHAR}, #{channelUserNo,jdbcType=VARCHAR},
        #{userNo,jdbcType=VARCHAR},
        #{userName,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{status,jdbcType=VARCHAR},
        #{groupNo,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.msgmanage.MsgUser">
    update UBADMA.MSG_USER
    set OPTIMISTIC = OPTIMISTIC + 1,
      UPDATE_TIME = SYSDATE,
      PHONE_NO = #{phoneNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
      CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      CHANNEL_USER_NO = #{channelUserNo,jdbcType=VARCHAR},
      USER_NO = #{userNo,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
      "STATUS" = #{status,jdbcType=VARCHAR},
      GROUP_NO = #{groupNo,jdbcType=VARCHAR},
      GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      "DESC" = #{desc,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
    and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
  </update>
</mapper>