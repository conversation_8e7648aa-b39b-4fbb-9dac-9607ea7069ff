{"groups": [{"name": "sms.aliyun", "type": "com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig", "sourceType": "com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig"}, {"name": "spring.job.executor", "type": "com.pay.tp.core.configuration.JobExecutorProperties", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties"}], "properties": [{"name": "sms.aliyun.templates", "type": "java.util.List<com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplate>", "sourceType": "com.pay.tp.core.remote.sms.aliyun.AliyunSmsTemplateConfig"}, {"name": "spring.job.executor.access-token", "type": "java.lang.String", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties"}, {"name": "spring.job.executor.app-name", "type": "java.lang.String", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties"}, {"name": "spring.job.executor.dispatcher-addresses", "type": "java.lang.String", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties"}, {"name": "spring.job.executor.ip", "type": "java.lang.String", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties"}, {"name": "spring.job.executor.log-path", "type": "java.lang.String", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties"}, {"name": "spring.job.executor.log-retention-days", "type": "java.lang.Integer", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties", "defaultValue": 0}, {"name": "spring.job.executor.port", "type": "java.lang.Integer", "sourceType": "com.pay.tp.core.configuration.JobExecutorProperties", "defaultValue": 0}], "hints": []}