<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.WxMsgTempCfgMapper">
  <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.WxMsgTempCfg">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="OPERATOR" jdbcType="VARCHAR" property="operator" />
    <result column="TEMP_ID" jdbcType="VARCHAR" property="tempId" />
    <result column="KEY" jdbcType="VARCHAR" property="key" />
    <result column="TEMP_KEY" jdbcType="VARCHAR" property="tempKey" />
    <result column="TEMP_COLOR" jdbcType="VARCHAR" property="tempColor" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, "OPERATOR", TEMP_ID, "KEY", TEMP_KEY, TEMP_COLOR, 
    "STATUS", BRAND
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UBADMA.WX_MSG_TEMP_CFG
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="findByTempId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WX_MSG_TEMP_CFG
    where TEMP_ID = #{tempId} and STATUS = 'TRUE'
  </select>

  <select id="findByPageAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WX_MSG_TEMP_CFG
    <where>
      <if test="brand != null and brand != '' ">
        and BRAND = #{brand}
      </if>
      <if test="operator != null and operator != '' ">
        and OPERATOR = #{operator}
      </if>
      <if test="status != null and status != '' ">
        and STATUS = #{status}
      </if>
      <if test="tempId != null and tempId != '' ">
        and TEMP_ID = #{tempId}
      </if>
      <if test="param.createTimeStart != null and param.createTimeStart != '' " >
        <![CDATA[
			  and CREATE_TIME >= to_date(#{param.createTimeStart,jdbcType=TIMESTAMP} || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
			]]>
      </if>
      <if test="param.createTimeEnd != null and param.createTimeEnd != '' " >
        <![CDATA[
			  and CREATE_TIME <= to_date(#{param.createTimeEnd,jdbcType=TIMESTAMP} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
			]]>
      </if>
    </where>
    order by id desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from UBADMA.WX_MSG_TEMP_CFG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>

  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.pay.tp.core.entity.WxMsgTempCfg" useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
      SELECT UBADMA.SEQ_WX_MSG_TEMP_CFG_ID.nextval FROM dual
    </selectKey>
    insert into UBADMA.WX_MSG_TEMP_CFG (OPTIMISTIC, CREATE_TIME, UPDATE_TIME,
      "OPERATOR", TEMP_ID, "KEY", 
      TEMP_KEY, TEMP_COLOR, "STATUS", 
      BRAND)
    values (#{id,jdbcType=DECIMAL}, 0, SYSDATE, #{updateTime,jdbcType=TIMESTAMP},
      #{operator,jdbcType=VARCHAR}, #{tempId,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, 
      #{tempKey,jdbcType=VARCHAR}, #{tempColor,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.WxMsgTempCfg">
    update UBADMA.WX_MSG_TEMP_CFG
    set OPTIMISTIC = OPTIMISTIC + 1,
      UPDATE_TIME = SYSDATE,
      "OPERATOR" = #{operator,jdbcType=VARCHAR},
      TEMP_ID = #{tempId,jdbcType=VARCHAR},
      "KEY" = #{key,jdbcType=VARCHAR},
      TEMP_KEY = #{tempKey,jdbcType=VARCHAR},
      TEMP_COLOR = #{tempColor,jdbcType=VARCHAR},
      "STATUS" = #{status,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>