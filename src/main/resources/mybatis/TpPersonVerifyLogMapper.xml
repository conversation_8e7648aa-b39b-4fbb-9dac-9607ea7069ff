<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pay.tp.core.mapper.TpPersonVerifyLogMapper" >
  <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.TpPersonVerifyLog" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="OPTIMISTIC" property="optimistic" jdbcType="DECIMAL" />
    <result column="USER_NO" property="userNo" jdbcType="VARCHAR" />
    <result column="LEGAL_PERSON" property="legalPerson" jdbcType="VARCHAR"  typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
    <result column="IDENTITY_NO" property="identityNo" jdbcType="VARCHAR"  typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
    <result column="VERIFY_SOURCE" property="verifySource" jdbcType="VARCHAR" />
    <result column="SOURCE_LOG_ID" property="sourceLogId" jdbcType="VARCHAR" />
    <result column="SCORE" property="score" jdbcType="VARCHAR" />
    <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR" />
    <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="BRAND" property="brand" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, OPTIMISTIC, USER_NO, LEGAL_PERSON, IDENTITY_NO, VERIFY_SOURCE, SOURCE_LOG_ID, 
    SCORE, ERROR_CODE, ERROR_MSG, CREATE_TIME, BRAND
  </sql>
  <insert id="insert" parameterType="com.pay.tp.core.entity.TpPersonVerifyLog" >
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
  		 SELECT UBADMA.SEQ_TP_PERSON_VERIFY_LOG_ID.nextval FROM dual
	</selectKey>
    insert into UBADMA.TP_PERSON_VERIFY_LOG (ID, OPTIMISTIC, USER_NO, 
      LEGAL_PERSON, IDENTITY_NO, VERIFY_SOURCE, 
      SOURCE_LOG_ID, SCORE, ERROR_CODE, 
      ERROR_MSG, CREATE_TIME, BRAND
      )
    values (#{id,jdbcType=DECIMAL}, 0, #{userNo,jdbcType=VARCHAR}, 
      #{legalPerson,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
      #{identityNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
      #{verifySource,jdbcType=VARCHAR}, 
      #{sourceLogId,jdbcType=VARCHAR}, #{score,jdbcType=VARCHAR}, #{errorCode,jdbcType=VARCHAR}, 
      #{errorMsg,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{brand,jdbcType=VARCHAR}
      )
  </insert>
</mapper>