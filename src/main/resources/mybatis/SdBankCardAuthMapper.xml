<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.auth.SdBankCardAuthMapper">

    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.auth.BankcardAuth">
        <result column="CARDNO" jdbcType="VARCHAR" property="cardNo"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CIDNO" jdbcType="VARCHAR" property="cidNo"/>
    </resultMap>
    <sql id="Base_Column_List">
    CARDNO,NAME,CIDNO
  </sql>

    <select id="query" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_BANKCARD_AUTH_COPY1
    </select>

	<delete id="del">
	 delete 
        from UBADMA.TP_BANKCARD_AUTH_COPY1 where CARDNO=#{cardNo} and NAME=#{name} and CIDNO=#{cidNo}
	</delete>
</mapper>