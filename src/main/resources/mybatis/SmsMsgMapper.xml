<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.sms.SmsMsgMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.sms.SmsMsg">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optismistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="SYS" jdbcType="VARCHAR" property="sys"/>
        <result column="REQUEST_NO" jdbcType="VARCHAR" property="requestNo"/>
        <result column="CONTENT" jdbcType="VARCHAR" property="content"/>
        <result column="PHONE" jdbcType="VARCHAR" property="phone"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="RESULT" jdbcType="VARCHAR" property="result"/>
        <result column="MESSAGE" jdbcType="VARCHAR" property="message"/>
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode"/>
 		<result column="BRAND" property="brand" jdbcType="VARCHAR" />
 		<result column="HIDDEN_PHONE" property="hiddenPhone" jdbcType="VARCHAR" />
 		<result column="MSG_ID" property="msgId" jdbcType="VARCHAR" />
 		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
 		<result column="RES_RESULT" property="resResult" jdbcType="VARCHAR" />
 		<result column="RES_MESSAGE" property="resMessage" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, CONTENT, PHONE, "RESULT", MESSAGE,
    CHANNEL_CODE, BRAND, HIDDEN_PHONE, MSG_ID, UPDATE_TIME, RES_RESULT, RES_MESSAGE
  </sql>

    <select id="query" resultMap="BaseResultMap"
            parameterType="com.pay.tp.core.beans.sms.SmsParam">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.SMS_MSG
        where REQUEST_NO = #{requestNo}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.sms.SmsMsg">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_SMS_MSG_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.SMS_MSG (ID,OPTIMISTIC, CREATE_TIME,
        PHONE, CONTENT, REQUEST_NO,
        BRAND, HIDDEN_PHONE, MSG_ID,
        UPDATE_TIME)
        values (#{id,jdbcType=DECIMAL},
        #{optismistic},
        #{createTime},
        #{phone,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{content},
        #{requestNo},
        #{brand}, #{hiddenPhone}, #{msgId},
        #{updateTime})
    </insert>

    <update id="update">
        update UBADMA.SMS_MSG set
        result = #{result},
        message = #{message},
        channel_code = #{channelCode},
        MSG_ID = #{msgId},
        UPDATE_TIME = #{updateTime}
        where ID = #{id}
  	</update>

    <update id="updateResMsg">
        update UBADMA.SMS_MSG set
        RES_RESULT = #{resResult},
        RES_MESSAGE = #{resMessage},
        UPDATE_TIME = #{updateTime}
        where ID = #{id}
  	</update>

    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.SMS_MSG
        where ID = #{id,jdbcType=DECIMAL}
    </select>

    <select id="findByMsgIdPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.SMS_MSG
        where PHONE = #{phone,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        and MSG_ID = #{msgId}
    </select>

    <select id="findByPageAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM UBADMA.SMS_MSG
        <where>
            <if test="queryParams.brand != null and queryParams.brand != ''">
                and BRAND = #{queryParams.brand}
            </if>
            <if test="queryParams.requestNo != null and queryParams.requestNo != ''">
                and REQUEST_NO = #{queryParams.requestNo}
            </if>
            <if test="queryParams.hiddenPhone != null and queryParams.hiddenPhone != ''">
                and HIDDEN_PHONE like CONCAT('%', #{queryParams.hiddenPhone})
            </if>
            <if test="queryParams.phone != null and queryParams.phone != ''">
                and PHONE =
                #{queryParams.phone,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="queryParams.channelCode != null and queryParams.channelCode != ''">
                and CHANNEL_CODE = #{queryParams.channelCode}
            </if>
            <if test="queryParams.result != null and queryParams.result != ''">
                and RESULT = #{queryParams.result}
            </if>
            <if test="queryParams.resResult != null and queryParams.resResult != ''">
                and RES_RESULT = #{queryParams.resResult}
            </if>
            <if test="queryParams.msgId != null and queryParams.msgId != ''">
                and MSG_ID = #{queryParams.msgId}
            </if>
            <if test="queryParams.startTime != null and queryParams.startTime != ''">
                <![CDATA[
	       	and CREATE_TIME >= to_date(#{queryParams.startTime},'yyyy-mm-dd')
	        ]]>
            </if>
            <if test="queryParams.endTime != null and queryParams.endTime != ''">
                <![CDATA[
	    	and CREATE_TIME < to_date(#{queryParams.endTime},'yyyy-mm-dd')+1
	     ]]>
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>