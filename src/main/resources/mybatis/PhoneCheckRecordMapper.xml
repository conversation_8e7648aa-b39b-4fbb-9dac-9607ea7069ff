<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.phonecheck.PhoneCheckRecordMapper">

    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.phonecheck.PhoneCheckRecord">
        <id column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="REQUEST_NO" property="requestNo" jdbcType="VARCHAR"/>
        <result column="PHONE_NUMBER" property="phoneNumber" jdbcType="VARCHAR"/>
        <result column="CHECK_TYPE" property="checkType" jdbcType="VARCHAR"/>
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="BRAND" property="brand" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="DECIMAL"/>
        <result column="STATUS_DESC" property="statusDesc" jdbcType="VARCHAR"/>
        <result column="CARRIER" property="carrier" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="NUMBER_TYPE" property="numberType" jdbcType="DECIMAL"/>
        <result column="RISK_LEVEL" property="riskLevel" jdbcType="VARCHAR"/>
        <result column="REAL_NAME_STATUS" property="realNameStatus" jdbcType="DECIMAL"/>
        <result column="CHECK_CHANNEL" property="checkChannel" jdbcType="VARCHAR"/>
        <result column="COST_TIME" property="costTime" jdbcType="DECIMAL"/>
        <result column="RAW_RESPONSE" property="rawResponse" jdbcType="CLOB"/>
        <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"/>
        <result column="EXT_PARAMS" property="extParams" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        , REQUEST_NO, PHONE_NUMBER, CHECK_TYPE, MERCHANT_NO, BRAND, STATUS, STATUS_DESC,
        CARRIER, PROVINCE, CITY, NUMBER_TYPE, RISK_LEVEL, REAL_NAME_STATUS, CHECK_CHANNEL,
        COST_TIME, RAW_RESPONSE, ERROR_MSG, EXT_PARAMS, CREATE_TIME, UPDATE_TIME
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE ID = #{id,jdbcType=DECIMAL}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.phonecheck.PhoneCheckRecord">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_PHONE_CHECK_RECORD_ID.nextval FROM dual
        </selectKey>
        INSERT INTO UBADMA.PHONE_CHECK_RECORD (
        ID, REQUEST_NO, PHONE_NUMBER, CHECK_TYPE, MERCHANT_NO, BRAND, STATUS, STATUS_DESC,
        CARRIER, PROVINCE, CITY, NUMBER_TYPE, RISK_LEVEL, REAL_NAME_STATUS, CHECK_CHANNEL,
        COST_TIME, RAW_RESPONSE, ERROR_MSG, EXT_PARAMS, CREATE_TIME, UPDATE_TIME
        ) VALUES (
        #{id,jdbcType=DECIMAL}, #{requestNo,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR},
        #{checkType,jdbcType=VARCHAR}, #{merchantNo,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR},
        #{status,jdbcType=DECIMAL}, #{statusDesc,jdbcType=VARCHAR}, #{carrier,jdbcType=VARCHAR},
        #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{numberType,jdbcType=DECIMAL},
        #{riskLevel,jdbcType=VARCHAR}, #{realNameStatus,jdbcType=DECIMAL}, #{checkChannel,jdbcType=VARCHAR},
        #{costTime,jdbcType=DECIMAL}, #{rawResponse,jdbcType=CLOB}, #{errorMsg,jdbcType=VARCHAR},
        #{extParams,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.phonecheck.PhoneCheckRecord">
        UPDATE UBADMA.PHONE_CHECK_RECORD
        SET REQUEST_NO       = #{requestNo,jdbcType=VARCHAR},
            PHONE_NUMBER     = #{phoneNumber,jdbcType=VARCHAR},
            CHECK_TYPE       = #{checkType,jdbcType=VARCHAR},
            MERCHANT_NO      = #{merchantNo,jdbcType=VARCHAR},
            BRAND            = #{brand,jdbcType=VARCHAR},
            STATUS           = #{status,jdbcType=DECIMAL},
            STATUS_DESC      = #{statusDesc,jdbcType=VARCHAR},
            CARRIER          = #{carrier,jdbcType=VARCHAR},
            PROVINCE         = #{province,jdbcType=VARCHAR},
            CITY             = #{city,jdbcType=VARCHAR},
            NUMBER_TYPE      = #{numberType,jdbcType=DECIMAL},
            RISK_LEVEL       = #{riskLevel,jdbcType=VARCHAR},
            REAL_NAME_STATUS = #{realNameStatus,jdbcType=DECIMAL},
            CHECK_CHANNEL    = #{checkChannel,jdbcType=VARCHAR},
            COST_TIME        = #{costTime,jdbcType=DECIMAL},
            RAW_RESPONSE     = #{rawResponse,jdbcType=CLOB},
            ERROR_MSG        = #{errorMsg,jdbcType=VARCHAR},
            EXT_PARAMS       = #{extParams,jdbcType=VARCHAR},
            UPDATE_TIME      = SYSDATE
        WHERE ID = #{id,jdbcType=DECIMAL}
    </update>

    <select id="findByRequestNoAndPhone" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE REQUEST_NO = #{requestNo,jdbcType=VARCHAR}
        AND PHONE_NUMBER = #{phoneNumber,jdbcType=VARCHAR}
        ORDER BY CREATE_TIME DESC
        FETCH FIRST 1 ROWS ONLY
    </select>

    <select id="findByRequestNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE REQUEST_NO = #{requestNo,jdbcType=VARCHAR}
        ORDER BY CREATE_TIME ASC
    </select>

    <select id="findRecentByPhone" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE PHONE_NUMBER = #{phoneNumber,jdbcType=VARCHAR}
        <if test="checkType != null and checkType != ''">
            AND CHECK_TYPE = #{checkType,jdbcType=VARCHAR}
        </if>
        ORDER BY CREATE_TIME DESC
        <if test="limit > 0">
            FETCH FIRST #{limit} ROWS ONLY
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO UBADMA.PHONE_CHECK_RECORD (
        ID, REQUEST_NO, PHONE_NUMBER, CHECK_TYPE, MERCHANT_NO, BRAND, STATUS, STATUS_DESC,
        CARRIER, PROVINCE, CITY, NUMBER_TYPE, RISK_LEVEL, REAL_NAME_STATUS, CHECK_CHANNEL,
        COST_TIME, RAW_RESPONSE, ERROR_MSG, EXT_PARAMS, CREATE_TIME, UPDATE_TIME
        )
        <foreach collection="records" item="record" separator="UNION ALL">
            SELECT
            UBADMA.SEQ_PHONE_CHECK_RECORD_ID.nextval,
            #{record.requestNo,jdbcType=VARCHAR},
            #{record.phoneNumber,jdbcType=VARCHAR},
            #{record.checkType,jdbcType=VARCHAR},
            #{record.merchantNo,jdbcType=VARCHAR},
            #{record.brand,jdbcType=VARCHAR},
            #{record.status,jdbcType=DECIMAL},
            #{record.statusDesc,jdbcType=VARCHAR},
            #{record.carrier,jdbcType=VARCHAR},
            #{record.province,jdbcType=VARCHAR},
            #{record.city,jdbcType=VARCHAR},
            #{record.numberType,jdbcType=DECIMAL},
            #{record.riskLevel,jdbcType=VARCHAR},
            #{record.realNameStatus,jdbcType=DECIMAL},
            #{record.checkChannel,jdbcType=VARCHAR},
            #{record.costTime,jdbcType=DECIMAL},
            #{record.rawResponse,jdbcType=CLOB},
            #{record.errorMsg,jdbcType=VARCHAR},
            #{record.extParams,jdbcType=VARCHAR},
            #{record.createTime,jdbcType=TIMESTAMP},
            #{record.updateTime,jdbcType=TIMESTAMP}
            FROM dual
        </foreach>
    </insert>

    <select id="findByPageAll" resultType="java.util.Map">
        SELECT
        ID, REQUEST_NO, PHONE_NUMBER, CHECK_TYPE, MERCHANT_NO, BRAND, STATUS, STATUS_DESC,
        CARRIER, PROVINCE, CITY, NUMBER_TYPE, RISK_LEVEL, REAL_NAME_STATUS, CHECK_CHANNEL,
        COST_TIME, ERROR_MSG, EXT_PARAMS, CREATE_TIME, UPDATE_TIME
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE 1=1
        <if test="requestNo != null and requestNo != ''">
            AND REQUEST_NO = #{requestNo}
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            AND PHONE_NUMBER LIKE '%' || #{phoneNumber} || '%'
        </if>
        <if test="checkType != null and checkType != ''">
            AND CHECK_TYPE = #{checkType}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            AND MERCHANT_NO = #{merchantNo}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        <if test="startTime != null and startTime != ''">
            <![CDATA[
            AND CREATE_TIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
             ]]>
        </if>
        <if test="endTime != null and endTime != ''">
            <![CDATA[
            AND CREATE_TIME<= TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
              ]]>
        </if>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="countByParams" resultType="int">
        SELECT COUNT(1)
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE 1=1
        <if test="requestNo != null and requestNo != ''">
            AND REQUEST_NO = #{requestNo}
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            AND PHONE_NUMBER LIKE '%' || #{phoneNumber} || '%'
        </if>
        <if test="checkType != null and checkType != ''">
            AND CHECK_TYPE = #{checkType}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            AND MERCHANT_NO = #{merchantNo}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        <if test="startTime != null and startTime != ''">
            <![CDATA[
            AND CREATE_TIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
              ]]>
        </if>
        <if test="endTime != null and endTime != ''">
            <![CDATA[
            AND CREATE_TIME<= TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
              ]]>
        </if>
    </select>

    <select id="countByMerchant" resultType="java.util.Map">
        SELECT
        COUNT(1) as TOTAL_COUNT,
        SUM(CASE WHEN STATUS = 1 THEN 1 ELSE 0 END) as NORMAL_COUNT,
        SUM(CASE WHEN STATUS = 0 THEN 1 ELSE 0 END) as EMPTY_COUNT,
        SUM(CASE WHEN STATUS = -1 THEN 1 ELSE 0 END) as FAIL_COUNT
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
        <if test="checkType != null and checkType != ''">
            AND CHECK_TYPE = #{checkType,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null and startTime != ''">
            <![CDATA[
            AND CREATE_TIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
              ]]>
        </if>
        <if test="endTime != null and endTime != ''">
            <![CDATA[
            AND CREATE_TIME <= TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
             ]]>
        </if>
    </select>

    <delete id="deleteExpiredRecords">
        DELETE
        FROM UBADMA.PHONE_CHECK_RECORD
        WHERE
             <![CDATA[
            CREATE_TIME <= TO_DATE(#{expireTime}, 'YYYY-MM-DD HH24:MI:SS')
        ]]>
    </delete>

</mapper>
