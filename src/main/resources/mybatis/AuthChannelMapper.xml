<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.auth.AuthChannelMapper">
  <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.auth.TpAuthChannel">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="LST_MOD_TIME" jdbcType="TIMESTAMP" property="lstModTime" />
    <result column="CHANNEL_NO" jdbcType="VARCHAR" property="channelNo" />
    <result column="CHANNEL_NAME" jdbcType="VARCHAR" property="channelName" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="PRIORITY" jdbcType="DECIMAL" property="priority" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, LST_MOD_TIME, CHANNEL_NO, CHANNEL_NAME, BUSINESS_CODE, 
    PRIORITY, STATUS, REMARK
  </sql>
  <select id="findByBusicodeAndChannelNo" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UBADMA.TP_AUTH_CHANNEL
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
    and CHANNEL_NO = #{channelNo,jdbcType=VARCHAR}
  </select>
  <select id="findByBusiCodeAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.TP_AUTH_CHANNEL
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
    <if test="status != null and status != ''">
    and STATUS = #{status,jdbcType=VARCHAR}
    </if>
    order by PRIORITY asc
  </select>
  <select id="findPageChannelList" resultType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from UBADMA.TP_AUTH_CHANNEL
    <where>
      <if test="queryParams.status != null and queryParams.status != '' " >
        and STATUS = #{queryParams.status,jdbcType=VARCHAR}
      </if>
      <if test="queryParams.businessCode != null and queryParams.businessCode != '' " >
        and BUSINESS_CODE = #{queryParams.businessCode,jdbcType=VARCHAR}
      </if>
      <if test="queryParams.channelNo != null and queryParams.channelNo != '' " >
        and CHANNEL_NO = #{queryParams.channelNo,jdbcType=VARCHAR}
      </if>
      <if test="queryParams.channelName != null and queryParams.channelName != '' " >
        and CHANNEL_NAME = #{queryParams.channelName,jdbcType=VARCHAR}
      </if>
    </where>
    order by BUSINESS_CODE,PRIORITY asc 
  </select>
  <insert id="insert" parameterType="com.pay.tp.core.entity.auth.TpAuthChannel">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
      SELECT UBADMA.SEQ_TP_AUTH_CHANNEL_ID.nextval FROM dual
    </selectKey>
    insert into UBADMA.TP_AUTH_CHANNEL (ID, OPTIMISTIC, CREATE_TIME, 
      LST_MOD_TIME, CHANNEL_NO, CHANNEL_NAME, 
      BUSINESS_CODE, PRIORITY, STATUS, 
      REMARK)
    values (#{id,jdbcType=DECIMAL}, 0, SYSDATE,
      SYSDATE, #{channelNo,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR},
      #{businessCode,jdbcType=VARCHAR}, #{priority,jdbcType=DECIMAL}, #{status,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <update id="updateById" parameterType="com.pay.tp.core.entity.auth.TpAuthChannel">
    update UBADMA.TP_AUTH_CHANNEL
    set OPTIMISTIC = OPTIMISTIC + 1,
      LST_MOD_TIME = SYSDATE,
      CHANNEL_NO = #{channelNo,jdbcType=VARCHAR},
      CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      PRIORITY = #{priority,jdbcType=DECIMAL},
      STATUS = #{status,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>