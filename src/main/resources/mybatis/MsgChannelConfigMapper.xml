<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.msgmanage.MsgChannelConfigMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.msgmanage.MsgChannelConfig">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode"/>
        <result column="CFG_PARAM" jdbcType="VARCHAR" property="cfgParam"/>
        <result column="CFG_VALUE" jdbcType="VARCHAR" property="cfgValue"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, CHANNEL_CODE, CFG_PARAM, CFG_VALUE, STATUS
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL_CONFIG
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <select id="findByChannelCodeAndCfgParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL_CONFIG
        where STATUS = 'ENABLE'
        and CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
        and CFG_PARAM = #{cfgParam,jdbcType=VARCHAR}
    </select>
    <select id="findByChannelCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL_CONFIG
        where STATUS = 'ENABLE'
        and CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
    </select>

    <select id="findByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL_CONFIG
        <where>
            1=1
            <if test="params.channelCode != null and params.channelCode != ''">
                and CHANNEL_CODE=#{params.channelCode}
            </if>
            <if test="params.cfgParam != null and params.cfgParam != ''">
                and CFG_PARAM=#{params.cfgParam}
            </if>
            <if test="params.cfgValue != null and params.cfgValue != ''">
                and CFG_VALUE=#{params.cfgValue}
            </if>
            <if test="params.status != null and params.status != ''">
                and STATUS=#{params.status}
            </if>
            <if test="params.dateStart !=null and params.dateStart !=''">
                and CREATE_TIME &gt;= to_date(#{params.dateStart},'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="params.dateEnd !=null and params.dateEnd !=''">
                and CREATE_TIME &lt;= to_date(#{params.dateEnd},'yyyy-MM-dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.msgmanage.MsgChannelConfig">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_MSG_CHANNEL_CONFIG_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.MSG_CHANNEL_CONFIG (ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME,
        CHANNEL_CODE, CFG_PARAM, CFG_VALUE, STATUS
        )
        values (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{channelCode,jdbcType=VARCHAR}, #{cfgParam,jdbcType=VARCHAR}, #{cfgValue,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.msgmanage.MsgChannelConfig">
    update UBADMA.MSG_CHANNEL_CONFIG
    set OPTIMISTIC = OPTIMISTIC + 1,
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      CFG_PARAM = #{cfgParam,jdbcType=VARCHAR},
      CFG_VALUE = #{cfgValue,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
    and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
  </update>

    <select id="findUnique" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL_CONFIG
        where ID != #{id,jdbcType=DECIMAL}
        AND STATUS = 'ENABLE'
        and CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
        and CFG_PARAM = #{cfgParam,jdbcType=VARCHAR}
    </select>
</mapper>