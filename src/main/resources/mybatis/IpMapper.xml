<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.position.IpMapper">


    <select id="query" resultType="com.pay.tp.core.entity.position.Ip" parameterType="com.pay.tp.core.beans.position.IpParam">
        select 
        	ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, IP, LAND, COUNTRY, CITY, DIST, 
        	PROV, ISP, ZIP_CODE, CC, LONGITUDE, LATITUDE, BEGIN_IP, END_IP, AREA 
        from UBADMA.POSI_IP where REQUEST_NO = #{requestNo}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.position.Ip">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_POSI_IP_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.POSI_IP (ID,OPTIMISTIC,CREATE_TIME,IP,REQUEST_NO)
        values (#{id,jdbcType=DECIMAL},0,sysdate, #{ip},#{requestNo})
    </insert>

    <update id="update" parameterType="java.util.Map">
		    update UBADMA.POSI_IP
            set
              LAND = #{land},
              COUNTRY = #{country},
              CITY = #{city},
              DIST = #{dist},
              PROV = #{prov},
              ISP = #{isp},
              ZIP_CODE = #{zipCode},
              CC = #{cc},
              LONGITUDE = #{longitude},
              LATITUDE = #{latitude},
              "BEGIN_IP" = #{beginIp},
              END_IP = #{endIp},
              AREA = #{area}
		    where ID = #{id}
  		</update>

</mapper>