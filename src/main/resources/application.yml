server:
  port: 8002
  context-path: /tp-core
  tomcat:
    basedir: '@tomcat.base@/@project.artifactId@'
    accesslog:
      directory: null
      enabled: true
      prefix: '@project.artifactId@-access-log'
      pattern: '%h %l %u [%{yyyy-MM-dd HH:mm:ss}t] %{X-Real_IP}i "%r" %s %b %D %F'
spring:
  profiles:
    active: '@env@,auth,addr,common,template'
  application:
    name: tp-core
  datasource:
    driverClassName: oracle.jdbc.OracleDriver
    driver-class-name: oracle.jdbc.OracleDriver
    platform: oracle
    url: ${common.oracle.url}
    username: ${auth.oracle.user}
    password: ${auth.oracle.password}
    druid:
      initial-size: 1
      min-idle: 1
      max-active: 10
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      filters: stat,wall,log4j
      filter:
        stat:
          log-slow-sql: true
      keep-alive: true
      stat-view-servlet:
        login-username: ${own.druid.user}
        login-password: ${own.druid.password}
        allow: ${common.stat-view-servlet.allow}
        deny: ${common.stat-view-servlet.deny}
        reset-enable: false
  redis:
    sentinel:
      master: ${common.redis.master-name}
      # redis
      nodes: ${common.redis.nodes}
    database: 5
    password: ${auth.redis.password}
    pool:
      max-active: 8
      max-wait: -1
      max-idle: 8
      min-idle: 1
    timeout: 1000
  job:
    executor:
      dispatcherAddresses: ${common.job.host}
      appName: '@project.artifactId@'
      ip:
      port: 9014
      accessToken:
      logPath: '@tomcat.base@/@project.artifactId@'
      logRetention-days:


mybatis:
  mapper-locations: classpath*:mybatis/*Mapper.xml
  type-aliases-package: com.pay.tp.core.entity
  configuration:
    map-underscore-to-camel-case: true
    jdbc-type-for-null: 'null'
eureka:
  instance:
    prefer-ip-address: true
  client:
    serviceUrl:
      defaultZone: ${common.eureka.zone}


# 负载均衡
ribbon:
  eureka:
    enabled: true
  ConnectTimeout: 60000
  ReadTimeout: 60000

# 文件ftp
file:
  ftp:
    ip: ${common.ftp.host}
    port: ${common.ftp.port}
    user: ${auth.ftp.user}
    password: ${auth.ftp.password}


custom:
  xuanwu:
    cgi: http://**************:9050/cgi-bin
    username: ldys@ldys
    password: ${own.xuanwu.password}
  xuanwuUkAgent:
    cgi: http://**************:9050/cgi-bin
    username: ldyk@ldyk
    password: q42g4V18
  xiaoma:
    send:
      url: https://Request.ucpaas.com/sms-partner/access/b005b0/sendsms
    password: ${own.xiaoma.password}
    user: b005b0
    sign: 【支付】
  jiguang:
    agent:
      app: 3a5a8c405a64463a049eaac3
      secret: 9f72bf4e30d5146ba826e64e
    agentuk:
      app: 192cb86b9ac8fc44d44b9ca7  #生产
      #e54a95f78afa7f381f914b80 #测试
      secret: ea4197bb412f595208729446 #生产
      #e29a53bab2b1b281eadd9db5 #测试
    newagentuk:
      app: ece4f41821c124ef54b843f5  #生产
      secret: ed491fda70bae0e096720fef #生产

  aozhong:
    send:
      url: http://www.aozoneyun.com/message/message/send
    uid: 276
    password: ldys@123456
  aozhongUkAgent:
    send:
      url: http://www.aozoneyun.com/message/message/send
    uid: 430
    password: Rw3ghQl5
  aliyun:
    sms:
      url: dysmsapi.aliyuncs.com
      accessKeyId: LTAI5tC3YXbxJBJknF9nojZa
      accessKeySecret: ******************************
      sign: 北京联动优客科技
      templateCode: SMS_485070256
#      sign: 联动优客
#      templateCode: SMS_485165216

wx:
  push:
    plusAppid: wx1e51696501e5ed09
    plusSecret: a2e80d3077fb3e3abc3a0a29d27b86af
    ukAppid: wx66d951fa88c6a44b
    ukSecret: a523e2eab9e5153096a8068a5e53893c
    openIdUrl: https://api.weixin.qq.com/sns/oauth2/access_token
    accessTokenUrl: https://api.weixin.qq.com/cgi-bin/token
    templateUrl: https://api.weixin.qq.com/cgi-bin/message/template/send
    #xcx
    xcxCustAppid: wx8c30f097ced989a5
    xcxCustSecret: d31aa456248fb3b3b7ddc2b62fedba68
    ukXcxCustAppid: wx7878b470d17923e6
    ukXcxCustSecret: 80c1d200eb9411bb40f38052f43bcbb8
    jscode2sessionUrl: https://api.weixin.qq.com/sns/jscode2session
    getuserphonenumberUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber
tencent:
    sl:
      appidQa: TIDA2hsN  #测试
      secretQa: ******************************oPBjQM8hCc9eOSc5VDklXHqXgASM37nM68  #测试
      appid: IDA9AkQO #生产
      secret: iMHtJNUiOqPebNvdO7vpURErPnVTLPfOdtKn3prgA5cvbpmBRcsXb6QsiNwFinDk #生产
      appidUkQa: TIDADq26  #测试
      secretUkQa: OZTlF1IMOeCGHQXNY7s9nVsow7MhD6JzXgZ0hU9iJWzaSqu9iz4JcjyAKeLnhsdW  #测试
      appidUk: IDAUOEtK #生产
      secretUk: 07IK1RWWPfbLPETYMESqaowyyxxmUDffLdInULqLSMVOj2CEH0E2BBiJm4AvDTxx #生产
      accessTokenUrl: https://miniprogram-kyc.tencentcloudapi.com/api/oauth2/access_token
      apiTicketUrl: https://miniprogram-kyc.tencentcloudapi.com/api/oauth2/api_ticket
      apiAppWillFaceResultUrl: https://miniprogram-kyc.tencentcloudapi.com/api/server/getWillFaceResult

      # eid 生产
      eidAppid: AKIDAE7nFTWNzNWUHqDPOegT3PXXMyTr8Ibo
      eidSecret: 72mjVBg071gBUaUE3jGdrunY71mMm16z
      eidUrl: faceid.tencentcloudapi.com
      eidMerchantId: 0NSJ2212211127388903
      eidMerchantIdUk: 0NSJ2306071658517831

baidu:
  acessTokenUrl: https://aip.baidubce.com/oauth/2.0/token
  text:
    appid: 24770841
    appkey: LUTNjEHDfGS1Bxg1QuB3yZ1N
    secretkey: rFRj4I0mUIsFOaVUzc1gVsURW6j3UimD
#  face:
#    appid: 26560644
#    appkey: xSjUpmGXVTtyhGytTZe4dGnh
#    secretkey: TCGyEDuQVIM4zREVRMVcLWemoeqzrgF1
#    planId: 14564
#    verifyTokenUrl: https://aip.baidubce.com/rpc/2.0/brain/solution/faceprint/verifyToken/generate

#ali:
#  yun:
#    region: cn-beijing
#    accessKeyId: LTAI5tAWz23p5bQ32Yi26eHN
#    accessKeySecret: ******************************
#    roleArn: acs:ram::1298636091447750:role/ldysramoss
#    bucketName: r3py9jtp87caldys
#    roleSessionName: sl
#    endpoint: https://oss-cn-beijing.aliyuncs.com
#    userName: <EMAIL>

# 测试
#ali:
#  yun:
#    region: cn-beijing
#    accessKeyId: LTAI5tJ7bW1HEu22NVRp9bAq
#    accessKeySecret: ******************************
#    roleArn: acs:ram::1298636091447750:role/ldysramoss
#    bucketName: 8dr43f53testoss
#    roleSessionName: sl
#    endpoint: https://oss-cn-beijing.aliyuncs.com
#    userName: <EMAIL>



ali:
  yun :
    region: cn-beijing
    accessKeyId: LTAI5tHodiCxhoczWQhY1mPD
    accessKeySecret: ******************************
    roleArn: acs:ram::1584395190084385:role/ldysramoss
    bucketName: d2df7jcl45mdldys
    roleSessionName: sl
    endpoint: https://oss-cn-beijing.aliyuncs.com
    userName: <EMAIL>
    accessUrl: https://d2df7jcl45mdldys.oss-cn-beijing.aliyuncs.com/

dingding:
  api:
    token: https://oapi.dingtalk.com/gettoken
    userId: https://oapi.dingtalk.com/user/get_by_mobile
    asyncSendV2: https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2
    robotSend: https://oapi.dingtalk.com/robot/send
workWechat:
  api:
    robotSend: https://qyapi.weixin.qq.com/cgi-bin/webhook/send
security:
  user:
    name: ${own.endpoint.user}
    password: ${own.endpoint.password}
  basic:
    path: /auditevents,/autoconfig,/beans,/configprops,/dump,/loggers,/metrics,/mappings,/trace
endpoints:
  shutdown:
    enabled: false
  pause:
    enabled: false
  restart:
    enabled: false
  refresh:
    enabled: false
  env:
    enabled: true
  loggers:
    enabled: true
  heapdump:
    enabled: true

com:
  pay:
    log:
      sleuth:
        trace:
          point: 'enable'
          expression: 'execution(* com.pay.tp.core.job.*.*(..))'
      standard:
        point: 'enable'
        expressionA: 'execution(* com.pay.tp.core..*(..))'
        expressionC: 'execution(* com.pay.tp.core.controller..*.*(..))'

kuaidi100:
  host: poll.kuaidi100.com
  path: /poll/query.do
  key: XjaKxzBr2945
  customer: 27C9F83966ABEBBA03F81F4F808C929A
  secret: 65ffbd534def492a8f7f79f9bb1fd878
  userid: 130d6c7d89bf4736aabe95412bdfe115
#fyt:
#  sha1key: '080982479E4D1F18B5C16888AE55675C'
#  aeskey: 'OZ2W491BN42WW051'
#  register:
#    url: 'https://www.lawtec.cn/open/users/register'
#    urlQa: 'http://test.lawtec.cn/open/users/register'
#  findUser:
#    url: 'https://www.lawtec.cn/open/users/find'
#    urlQa: 'http://test.lawtec.cn/open/users/find'
httpClient:
  net:
    maxPoolNum: 100
    socketTimeout: 300000
    connectTimeout: 600000
    connectionRequestTimeout: 1000

yongyou:
  api:
    baseUrl: http://*************:5580
    signKey: '64B0AD5FD36745FB94FAF6'
    partnerId: 'pCDE746E7C9664ED991E22F802B6FD866'
    company: '6ogfq2'
    addCustomer: '/open/customer/add'

phonecheck:
  api:
    baseUrl: https://dmpreapi.zjdex.com
    merchantId:


