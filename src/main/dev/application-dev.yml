# 公共变量
common:
  register:
    qa: true
  eureka:
    zone: ********************************/eureka/,********************************/eureka/
  redis:
    master-name: ldysmaster
    nodes: **********:26379,**********:26379,**********:26379
  rocketmq:
    nameserver: **********:9876;**********:9876
  oracle:
    url: ****************************************
  ftp:
    host: **********
    port: 13579
  job:
    host:
# 数据源变量
auth:
  oracle:
    user: upcorea
    password: 0iYLtvKystEtzk7G
  ftp:
    user: sftpuser
    password: 'gKc5[ZA]LhPcGM2vgD/z'
  redis:
    password: 'abe3cC7@6aM38oa44Y'

crypto:
  sensitive:
    sm4: 'onQO4J7B8+KSZ0wm7fIOsw=='

custom:
  jixin:
    bankcard:
      auth:
        #测试
        url: http://************:9003/jxdata/api/auth/jm/execute.do
        merchno: '****************'
        #生产
        #url: http://api.jixintech.net/jxdata/api/auth/jm/execute.do
        #merchno: '****************'

  xinlian:
    bankcard:
      auth:
        url: https://op.xgdfin.com/xlzxop/certificate/auth/relCard.do
    company:
      check:
        url: https://op.credlink.com/xlzxop/creditreport/companycheck.do
  zhangxun:
    bankcard:
      auth:
        url: http://test.amicloud.cn:8891/v1.0/realname/
        merId: '***********'
        #生产
        #url: http://api3.amicloud.cn:8892/v1.0/realname/
        #merId:

# position
  juhe:
    url: http://v.juhe.cn/cell/query
    #生产
    #key: 788d14efc34cf466ab432406b0e42607
    key: 889dae3e363575b65e648f70e1d35ccf
  aliyun:
    ip:
      url: ''
      app:
        code: ''
  gaode:
    key: e0286bf6adc07c65f3a1c33b33c0e4cb
    geocode:
      regeo:
        url: https://restapi.amap.com/v3/geocode/regeo?parameters



own:
  jixin:
    #测试
    merchkey: 'LaYeIemv2WEY8Z7h'
    #生产
    #merchkey: '2GEr6vfAaumcI2eS'

  zhangxun:
    #测试
    zxprivkey: ''
    zxpubkey: ''
    zxdeskey: ''
    #生产
    #zxprivkey: ''
    #zxpubkey: ''
    #zxdeskey: ''

  xinlian:
    # 测试
    insIdJilong: 'INS6220329000001'
    operIdJilong: 'JILONG_test'
    insIdBjld: 'INS6220329000002'
    operIdBjld: 'BJLD_test'
    #
    #
    # 生产
    #insId: ''
    #operId: ''

#sms
  xuanwu:
    password: 'Mir8OAs8'
  xiaoma:
    password: ''

# position
  druid:
    user: admin
    password: admin
  endpoint:
    user: admin
    password: admin


---
proxy:
  squid:
    flag: N
    host: **********
    port: 3128

spring:
  kafka:
    bootstrap-servers:
      - **********:9092


