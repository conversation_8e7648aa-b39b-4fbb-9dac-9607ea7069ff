吉信鉴权文档：
银行卡风险识别（二要素简版） https://apidoc.jixintech.net/docs/jgh?token=L6Cn4wB5ZiPV 密码：jx_104
银行卡风险识别（二要素简版） https://apidoc.jixintech.net/docs/fgds?token=9H8WzV37Tl3v 密码：jx_105
银行卡风险识别（三要素简版） https://apidoc.jixintech.net/docs/dfgd?token=66gzyd8w0Bm3 密码：jx_106
银行卡风险识别（四要素简版） https://apidoc.jixintech.net/docs/erfvv?token=DFl2DLZVi1wJ 密码：jx_107
银行卡风险识别（三要素详版） https://apidoc.jixintech.net/docs/dgnj?token=94tq1IPqlnch 密码：jx_116
银行卡风险识别（四要素详版） https://apidoc.jixintech.net/docs/sdfg?token=1Z169t8X3JtZ 密码：jx_117
银行卡风险识别（五要素）https://apidoc.jixintech.net/docs/wuyaosu/wuyaosu-1c7egpq0vee4i  密码：jx_405

三、 报文要素定义
3.1 报文组成
每笔交易的报文组成由 公共参数 + 具体业务参数 组成，当业务应答参数为空是表示该业务的所有应答参数包含在公共应答参数内。

3.2 公共请求参数
公共请求报文是每一个交易固定要上送的要素信息（有特别说明的交易以特别说明为准）。

发送请求
参数名称	参数含义	是否必填	数据类型	说明
transcode	交易码	是	String(4)	如106参见附录1)交易代码
version	版本号	是	String(4)	0100
ordersn	流水号	是	String(32)	唯一值，每次请求都唯一
merchno	商户号	是	String(15)	数据平台系统分配
dsorderid	商户订单号	是	String(32)	每笔交易唯一，若为查询接口填原交易的商户订单号
sceneCode	交易业务场景	是	String(64)	发起交易商户业务场景编码，枚举见最后交易场景编码
sCustomerName	二级商户名称	是	String(64)	xxx商户名-yyy产品名称-zzz使用方法
scUsePurpose	使用目的	是	String(2-64)	最终使用数据的终端业务机构的使用目的
protocolVerNm	授权协议版本号	是	String(2-40)	终端商户与 C 端客户签订的电子 协议版本号(要能够查询定位到 签署授权协议的具体内容)(授权协议模版应事前备案)
serialNm	授权流水号	是	String(8-64)	C 端客户向终端商户进行信息核验(或查询）的个人信息使用授权协议签署的流水号(通过该流水号要能够定位到签署协议人员 以及日期)
accttype	账户类别	否	String(4)	账户类别：0 一类 1 二类 2 三类
sign	加密校验值	是	String(300)	data 字段的加密校验值
reqIp	请求IP	否	String(32)	
案例：
{
“scUsePurpose”:”XXX”,
“protocolVerNm”:”XXXXXX”,
“serialNm”:”XXXXXXXX”,
“transcode”:”106”,
“merchno”:”000000000000XXXX”,
“dsorderid”:”17419XXXXXXXX9498”,
“sign”:”adb664c0cefa8767d744b55ea706f20c”,
“version”:”0100”,
“ordersn”:”17419XXXXXXXXX9498”,
“idcard”:”XXXXXXXXXXXXXXXXXX”,
“idtype”:”01”,
“bankcard”:”XXXXXXXXXXXXXXXX”,
“username”:”XX”,
“sceneCode”:”99”,
“sCustomerName”:”XX支付-XX账户实名认证-XX实名认证”
}

接口规则
1、 数据据均采用 JSON 格式。
2、 非必填数据可以不上送，但在满足条件时候必须上送。

3.3 公共应答参数
应答参数
参数名称	参数含义	是否必填	数据类型	说明
transcode	交易码	是	String(4)	001
ordersn	商户请求流水号	是	String(32)	唯一值，每次请求都唯一
merchno	商户号	是	String(15)	认证系统分配
dsorderid	商户订单号	是	String(32)	每笔交易唯一
orderid	平台交易流水号	否	String(32)	平台唯一流水号
returncode	返回码	是	String(4)	旧错误码系统，不再维护，以platformCode、platformDesc为准
errtext	返回信息	否	String(200)	旧错误码系统，不再维护，以platformCode、platformDesc为准
riskaccttype	账户类别是否正确	否	String(12)	账户类别正确：1 、不正确：0 、空
riskinfo	风险信息	否	JSON	riskdish是否存在失信风险
riskcash是否存在套现风险
risklam 是否存在在逃风险
riskfinacrime是否存在金融犯罪风险
说明：1:存在 、0：不存在、空
platformCode	平台返回码	是	String(16)	001000000，其他见附录二（新响应码系统）
platformDesc	平台返回信息	是	String(128)	描述交易具体情况
sign	加密校验值	否	String(300)	data 字段的加密校验值0098、0004、 等无sign字段
案例：
{
“dsorderid”:”17419XXXXXXXX9498”,
“merchno”:”000000000000XXXX”,
“returncode”:”0000”,
“errtext”:”验证通过”,
“transcode”:”106”,
“ordersn”:”17419XXXXXXXX9498”,
“orderid”:”201912XXXXXXXX0881”,
“sign”:”a375f3de613324b9d9e0bfc01cb8e0ac”,
“platformCode”:”001000000”,
“platformDesc”:”验证通过”
}

接口规则
1、 数据据均采用 JSON 格式。
2、非必填数据可以不上送，但在满足条件时候必须上送。

吉信对外响应码	吉信对外响应码描述	是否收费
001000000	验证通过	是
001010034	验证信息不一致	是
001010035	卡号无效	是
001010036	卡状态不正确或卡号错误	是
001012002	证件号码或类型有误	是
001030050	网络超时，请稍后重试	否
001050003	银行卡未开通银联无卡支付功能	否
001050011	该银行暂不支持，请联系客服	否
001050012	暂不支持该银行卡种	否
001050013	该卡片暂不支持或权限受限，请联系客服!	否
001050014	银行卡暂不支持该业务，请咨询您的发卡行	否
001050037	账号信息重复，请确认	否
001050040	交易失败，请稍后重试	否
001050041	交易失败，详情请咨询您的发卡行	否
001050043	账户类型不支持	否
001050060	请求参数格式错误	否
001050061	请求信息不全，请补齐	否
001050062	交易码或请求地址错误	否
001052001	卡号错误	否
001052003	订单重复，请更换商户订单号	否
001052011	参数解密失败	否
001052012	参数校验异常	否
001052013	验签失败	否
001052031	商户未注册	否
001052032	商户被禁用	否
001052033	通道权限受限,请联系客服	否
001052034	设置了黑名单，禁止访问	否
001052999	业务路由没有配置	否
001058000	验证次数超限，请次日重试	否
001058001	该交易存在风险，拒绝验证	否
001058002	请求过于频繁，请稍后重试	否
001059000	渠道错误，请联系客服	否
001059001	渠道请求流水号重复，请联系客服	否
001063000	系统问题导致失败，请联系客服	否
001050050	账户余额不足，请及时充值	否
001009999	新增平台响应码	否