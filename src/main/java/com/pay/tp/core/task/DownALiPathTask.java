package com.pay.tp.core.task;

import com.pay.frame.common.base.util.DateUtil;
import com.pay.frame.common.base.util.FmtDate;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.beans.sms.SmsParam;
import com.pay.tp.core.biz.SmsChannelBiz;
import com.pay.tp.core.biz.impl.TencentBiz;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.enums.WillType;
import com.pay.tp.core.service.WillBodyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@JobHandler(value = "downALiPath")
public class DownALiPathTask extends IJobHandler {

    @Autowired
    private WillBodyRecordService willBodyRecordService;

    @Autowired
    private TencentBiz tencentBiz;

    @Autowired
    private SmsChannelBiz smsChannelBiz;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("downALiPath start..." + params);

            Date appointDate = FmtDate.getAppointHourMin(new Date(), 3, 50);
            String content = "{0} 阿里云下载已经快4点了，停止下载";

            String startTime = DateUtil.addDay(-3);
            List<WillBodyRecord> list = willBodyRecordService.findAliFlag(startTime);
            log.info("下载阿里云意愿视频：{}", list.size());
            for (WillBodyRecord record : list) {

                if(new Date().after(appointDate)){
                    SmsParam param = new SmsParam();
                    param.setRequestNo(record.getOrderNo());
                    param.setPhone("13681563801");
                    param.setContent(MessageFormat.format(content, record.getOrderNo()));
                    param.setBrand(record.getBrand());
                    smsChannelBiz.send(param);
                    log.info("阿里云下载已经快4点了，停止下载：{}", record);

                    break;
                }

                try {
                    log.info("下载阿里云地址：{}", record);
                    tencentBiz.downALiPath(record);
                } catch (Exception e) {
                    log.error("tencent downALiPath error ", record, e);
                }
            }

            log.info("downALiPath end..." + params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("downALiPath error", e);
            return ReturnT.FAIL;
        }
    }
}
