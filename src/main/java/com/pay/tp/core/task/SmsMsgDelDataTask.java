package com.pay.tp.core.task;

import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.service.msgmanage.MsgRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "smsMsgDelData")
public class SmsMsgDelDataTask extends IJobHandler {

    @Autowired
    private MsgRecordService msgRecordService;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("smsMsgDelData start..." + params);

            msgRecordService.deleteDingMsg();
            log.info("smsMsgDelData end..." + params);

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("smsMsgDelData error", e);
            return ReturnT.FAIL;
        }
    }
}
