package com.pay.tp.core.task;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.biz.impl.YongYouDataPushBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用友数据推送定时任务
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Component
@JobHandler(value = "yongYouDataPush")
public class YongYouDataPushTask extends IJobHandler {

    @Autowired
    private YongYouDataPushBiz yongYouDataPushBiz;

    /**
     * 每次处理的记录数量
     */
    private static final int BATCH_SIZE = 100;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
            log.info("用友数据推送定时任务开始执行，参数: {}", params);

            // 调用biz层进行批量处理
            ResultsBean<String> result = yongYouDataPushBiz.batchProcessPush(BATCH_SIZE);

            if (result.success()) {
                log.info("用友数据推送定时任务执行成功: {}", result.getObject());
                return ReturnT.SUCCESS;
            } else {
                log.error("用友数据推送定时任务执行失败: {}", result.getMessage());
                return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
            }

        } catch (Exception e) {
            log.error("用友数据推送定时任务执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "定时任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 获取待推送记录统计信息
     * @param params 参数
     * @return 执行结果
     */
    public ReturnT<String> getStatistics(String params) throws Exception {
        try {
            log.info("获取用友数据推送统计信息，参数: {}", params);

            ResultsBean<String> result = yongYouDataPushBiz.getPendingPushStatistics();

            if (result.success()) {
                log.info("获取统计信息成功: {}", result.getObject());
                return ReturnT.SUCCESS;
            } else {
                log.error("获取统计信息失败: {}", result.getMessage());
                return new ReturnT<>(ReturnT.FAIL_CODE, result.getMessage());
            }

        } catch (Exception e) {
            log.error("获取统计信息异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取统计信息异常: " + e.getMessage());
        }
    }
}
