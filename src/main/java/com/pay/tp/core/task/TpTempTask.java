package com.pay.tp.core.task;

import java.io.File;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.GetObjectRequest;
import com.pay.frame.common.base.constants.FilePathConstants;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@JobHandler(value = "tpTempTask")
public class TpTempTask extends IJobHandler {

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;
    @Value("${ali.yun.endpoint}")
    private String endpoint;
    @Value("${ali.yun.region}")
    private String region;
    @Value("${ali.yun.accessKeyId}")
    private String accessKeyId;
    @Value("${ali.yun.accessKeySecret}")
    private String accessKeySecret;
    @Value("${ali.yun.roleArn}")
    private String roleArn;
    @Value("${ali.yun.bucketName}")
    private String bucketName;
    @Value("${ali.yun.roleSessionName}")
    private String roleSessionName;

    @Value("${spring.application.name}")
    private String appName;
    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("tpTempTask start..." + params);
        	downFile(params);
        	log.info("tpTempTask end..." + params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("appWillBody error", e);
            return ReturnT.FAIL;
        }
    }
    
    
    public void downFile(String fileName) {
        // 创建OSSClient实例。
    	log.info("tpTempTask OOS start ..." + fileName);
        ClientBuilderConfiguration cfg = new ClientBuilderConfiguration();
        cfg.setProxyHost(proxyHost);
        cfg.setProxyPort(proxyPort);
        log.info("tpTempTask OOS cfg..." + fileName);
        DefaultCredentialProvider provider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);
        log.info("tpTempTask OOS provider..." + fileName);
        OSS ossClient = new OSSClient(endpoint, provider, cfg);
//        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, cfg);
        log.info("tpTempTask OOS end ..." + fileName);
        if(StringUtils.isNotBlank(fileName)) {
        	String pathName = FilePathConstants.getLoaclTempPath(appName)+ fileName;
        	try {
        		File file = new File(pathName);
        		// 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
        		// 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
        		ossClient.getObject(new GetObjectRequest(bucketName, fileName), file);
        	} catch (OSSException oe) {
        		log.error(oe.getMessage(), oe);
        		log.info("Error Message:{},{},{},{},{},{}", oe.getErrorMessage(), oe.getErrorCode(), oe.getRequestId(), oe.getHostId(), fileName, pathName);
        		throw new ServerException(oe.getMessage());
        		
        	} catch (Exception e) {
        		log.error("阿里云下载错误：{},{},{},{}", e.getMessage(), fileName, pathName, e);
        		throw new ServerException(e.getMessage());
        		
        	}
        }
    }
}
