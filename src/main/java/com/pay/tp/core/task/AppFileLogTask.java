package com.pay.tp.core.task;

import com.pay.frame.common.base.util.DateUtil;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.biz.impl.AppFileLogBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "appFileLog")
public class AppFileLogTask extends IJobHandler {
	@Autowired
	private AppFileLogBiz appFileLogBiz;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("appFileLog start..." + params);
			String startTime = DateUtil.addDay(-7);
			appFileLogBiz.delFile(startTime);
            log.info("appFileLog end..." + params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("appFileLog error", e);
            return ReturnT.FAIL;
        }
    }
}
