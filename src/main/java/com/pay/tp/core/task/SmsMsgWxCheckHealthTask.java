package com.pay.tp.core.task;

import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.util.DateUtil;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.biz.impl.AppFileLogBiz;
import com.pay.tp.core.biz.impl.WeChatBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Slf4j
@Component
@JobHandler(value = "smsMsgWxCheckHealth")
public class SmsMsgWxCheckHealthTask extends IJobHandler {

    /**
     * 钉钉通道业务处理
     */
    @Autowired
    private WeChatBiz weChatBiz;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("smsMsgWxCheckHealth start..." + params);
            Arrays.asList(Brand.values()).forEach(brand ->{
                weChatBiz.loadAccessToken(brand.name());
            });
            log.info("smsMsgWxCheckHealth end..." + params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("smsMsgWxCheckHealth error", e);
            return ReturnT.FAIL;
        }
    }
}
