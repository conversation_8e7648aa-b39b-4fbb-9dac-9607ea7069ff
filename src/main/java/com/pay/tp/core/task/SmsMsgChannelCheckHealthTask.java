package com.pay.tp.core.task;

import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.biz.impl.DingDingBiz;
import com.pay.tp.core.entity.msgmanage.MsgChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@JobHandler(value = "smsMsgChannelCheckHealth")
public class SmsMsgChannelCheckHealthTask extends IJobHandler {

    /**
     * 钉钉通道业务处理
     */
    @Autowired
    private DingDingBiz dingDingBiz;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("smsMsgChannelCheckHealth start..." + params);

            List<MsgChannel> channels = dingDingBiz.findAllChannel();
            channels.forEach(channel -> {
                boolean isHealth = dingDingBiz.loadAccessToken(channel.getCode());
                if (isHealth) {
                    log.info("ding ding channel is healthy: {}", channel.getCode());
                } else {
                    log.error("ding ding channel is dead: {}", channel.getCode());
                }
            });
            log.info("smsMsgChannelCheckHealth end..." + params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("smsMsgChannelCheckHealth error", e);
            return ReturnT.FAIL;
        }
    }
}
