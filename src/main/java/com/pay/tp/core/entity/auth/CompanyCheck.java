package com.pay.tp.core.entity.auth;

import org.hibernate.validator.constraints.NotBlank;

import com.pay.tp.core.entity.BaseEntity;

import java.util.Date;

/**
* <AUTHOR>
* @version 创建时间：2018年12月19日 下午11:22:21
* @ClassName 
* @Description 
*/
public class CompanyCheck extends BaseEntity{
	
	private String entName;
	private String regNo;
	private String frName;
	private String cidNo;
	private String transDate;
	private String transTime;
	private String paySerialNo;
	private String validateStatus;
	private String rspMsg;
	private String validateDescribe;
	private String resultEntName;
	private String resultRegNo;
	private String data;
	private String rspCod;


	public String getEntName() {
		return entName;
	}

	public void setEntName(String entName) {
		this.entName = entName;
	}

	public String getRegNo() {
		return regNo;
	}

	public void setRegNo(String regNo) {
		this.regNo = regNo;
	}

	public String getFrName() {
		return frName;
	}

	public void setFrName(String frName) {
		this.frName = frName;
	}

	public String getCidNo() {
		return cidNo;
	}

	public void setCidNo(String cidNo) {
		this.cidNo = cidNo;
	}

	public String getTransDate() {
		return transDate;
	}

	public void setTransDate(String transDate) {
		this.transDate = transDate;
	}

	public String getTransTime() {
		return transTime;
	}

	public void setTransTime(String transTime) {
		this.transTime = transTime;
	}

	public String getPaySerialNo() {
		return paySerialNo;
	}

	public void setPaySerialNo(String paySerialNo) {
		this.paySerialNo = paySerialNo;
	}

	public String getValidateStatus() {
		return validateStatus;
	}

	public void setValidateStatus(String validateStatus) {
		this.validateStatus = validateStatus;
	}

	public String getRspMsg() {
		return rspMsg;
	}

	public void setRspMsg(String rspMsg) {
		this.rspMsg = rspMsg;
	}

	public String getValidateDescribe() {
		return validateDescribe;
	}

	public void setValidateDescribe(String validateDescribe) {
		this.validateDescribe = validateDescribe;
	}


	public String getResultEntName() {
		return resultEntName;
	}

	public void setResultEntName(String resultEntName) {
		this.resultEntName = resultEntName;
	}

	public String getResultRegNo() {
		return resultRegNo;
	}

	public void setResultRegNo(String resultRegNo) {
		this.resultRegNo = resultRegNo;
	}

	public String getRspCod() {
		return rspCod;
	}

	public void setRspCod(String rspCod) {
		this.rspCod = rspCod;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}


	public CompanyCheck() {
		super();
	}

	public CompanyCheck(String requestNo, String sys, String entName, String regNo, String frName, String cidNo) {
		super(requestNo, sys);
		this.entName = entName;
		this.regNo = regNo;
		this.frName = frName;
		this.cidNo = cidNo;
	}

	public CompanyCheck(String entName) {
		this.entName = entName;
	}
}
