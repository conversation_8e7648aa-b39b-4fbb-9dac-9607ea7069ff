package com.pay.tp.core.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * WX_MSG_TEMP
 * <AUTHOR>
@Data
public class WxMsgTemp implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 乐观锁
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 模板类型
     */
    private String tempType;

    /**
     * 模板id
     */
    private String tempId;

    /**
     * 模板名称
     */
    private String tempName;
    /**
     * 标题
     */
    private String tempTitle;
    /**
     * url类型
     */
    private String urlType;

    /**
     * url
     */
    private String tempUrl;

    /**
     * appid
     */
    private String appid;

    /**
     * 备注
     */
    private String tempRemark;

    /**
     * 状态
     */
    private String status;

    /**
     * 品牌
     */
    private String brand;

    private static final long serialVersionUID = 1L;
}