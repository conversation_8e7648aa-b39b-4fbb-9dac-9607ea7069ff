package com.pay.tp.core.entity.sms;

import java.util.Date;

import com.pay.tp.core.entity.BaseEntity;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.entity
 * @Description: TODO
 * @date Date : 2018年12月26日 21:36
 */
public class SmsRes extends BaseEntity {

    private String phone;

    private String status;

    private String channelNo;

    private Date userReceiveTime;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getUserReceiveTime() {
        return userReceiveTime;
    }

    public void setUserReceiveTime(Date userReceiveTime) {
        this.userReceiveTime = userReceiveTime;
    }

    public SmsRes() {
    }

    public SmsRes(String requestNo, String sys, String phone, String status, Date userReceiveTime, String channelNo) {
        super(requestNo, sys);
        this.phone = phone;
        this.status = status;
        this.userReceiveTime = userReceiveTime;
        this.channelNo = channelNo;
    }


}
