package com.pay.tp.core.entity.msgmanage;

import com.pay.tp.core.enums.Status;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * MSG_TEMPLATE
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MsgTemplate implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 消息模板
     */
    private String msgTemplate;

    /**
     * 状态
     */
    private Status status;

    /**
     * 消息模板名称
     */
    private String templateName;

    /**
     * 消息模板编号
     */
    private String templateCode;

    /**
     * 消息通道编号
     */
    private String channelCode;

    /**
     * 用户组号
     */
    private String userGroupNo;

    /**
     * 模板描述
     */
    private String desc;

    /**
     * 扩展功能
     */
    private String extInfo;

    @Data
    public static class ExtInfo {
        private Filter filter;
    }

    @Data
    public static class Filter {

        private List<String> pass;

        private List<String> reject;
    }

}