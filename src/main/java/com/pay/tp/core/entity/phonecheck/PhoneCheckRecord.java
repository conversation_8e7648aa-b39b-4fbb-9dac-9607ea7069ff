package com.pay.tp.core.entity.phonecheck;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 号码检测记录实体类
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@Accessors(chain = true)
public class PhoneCheckRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 请求号
     */
    private String requestNo;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 检测类型
     */
    private String checkType;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 品牌标识
     */
    private String brand;

    /**
     * 检测状态
     * 1: 正常
     * 0: 空号/异常
     * -1: 检测失败
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 运营商
     */
    private String carrier;

    /**
     * 归属地省份
     */
    private String province;

    /**
     * 归属地城市
     */
    private String city;

    /**
     * 号码类型
     */
    private Integer numberType;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 实名状态
     */
    private Integer realNameStatus;

    /**
     * 检测渠道
     */
    private String checkChannel;

    /**
     * 检测耗时（毫秒）
     */
    private Long costTime;

    /**
     * 原始响应
     */
    private String rawResponse;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 扩展参数
     */
    private String extParams;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 检测类型枚举
     */
    public static class CheckType {
        /** 空号检测 */
        public static final String EMPTY_NUMBER = "EMPTY_NUMBER";
        /** 实名检测 */
        public static final String REAL_NAME = "REAL_NAME";
        /** 风控检测 */
        public static final String RISK_CONTROL = "RISK_CONTROL";
    }

    /**
     * 号码状态枚举
     */
    public static class PhoneStatus {
        /** 正常 */
        public static final Integer NORMAL = 1;
        /** 空号/异常 */
        public static final Integer EMPTY = 0;
        /** 检测失败 */
        public static final Integer FAIL = -1;
    }

    /**
     * 运营商枚举
     */
    public static class Carrier {
        /** 中国移动 */
        public static final String CMCC = "CMCC";
        /** 中国联通 */
        public static final String CUCC = "CUCC";
        /** 中国电信 */
        public static final String CTCC = "CTCC";
    }

    /**
     * 风险等级枚举
     */
    public static class RiskLevel {
        /** 低风险 */
        public static final String LOW = "LOW";
        /** 中风险 */
        public static final String MEDIUM = "MEDIUM";
        /** 高风险 */
        public static final String HIGH = "HIGH";
    }
}
