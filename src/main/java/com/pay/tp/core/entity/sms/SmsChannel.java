package com.pay.tp.core.entity.sms;

import com.pay.tp.core.entity.BaseEntity;
import com.pay.tp.core.enums.Status;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.entity
 * @Description: TODO
 * @date Date : 2018年12月25日 14:14
 */

public class SmsChannel extends BaseEntity{

    private String code;

    private String name;

    private String channelType;

    private Status status;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }
}
