package com.pay.tp.core.entity.position;

import com.pay.tp.core.beans.position.CellBean;
import com.pay.tp.core.entity.BaseEntity;
import org.springframework.beans.BeanUtils;

import java.beans.Transient;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.entity
 * @Description: TODO
 * @date Date : 2018年12月24日 11:57
 */
public class Cell extends BaseEntity {
    /**
     * 服务商标识,移动或联通
     */
    private String mnc;

    /**
     * 区域号
     */
    private String lac;

    /**
     * 基站号
     */
    private String cell;

    /**
     * 响应码
     */
    private String resultCode;

    /**
     * 响应描述
     */
    private String reason;

    /**
     * 返回Mnc
     */
    private String dMnc;

    /**
     * 返回mcc
     */
    private String dMcc;

    /**
     * 返回区域号
     */
    private String dLac;

    /**
     * 返回基站号
     */
    private String dCell;

    /**
     * 经度
     */
    private String dLng;

    /**
     * 维度
     */
    private String dLat;

    /**
     * 纠偏后的经度
     */
    private String oLng;

    /**
     * 纠偏后的维度
     */
    private String oLat;

    /**
     * 基站范围
     */
    private String precision;

    /**
     * 街道地址
     */
    private String address;

    /**
     * 地区编号
     */
    private String adCode;

    public String getMnc() {
        return mnc;
    }

    public void setMnc(String mnc) {
        this.mnc = mnc;
    }

    public String getLac() {
        return lac;
    }

    public void setLac(String lac) {
        this.lac = lac;
    }

    public String getCell() {
        return cell;
    }

    public void setCell(String cell) {
        this.cell = cell;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getdLac() {
        return dLac;
    }

    public void setdLac(String dLac) {
        this.dLac = dLac;
    }

    public String getdCell() {
        return dCell;
    }

    public void setdCell(String dCell) {
        this.dCell = dCell;
    }

    public String getdLng() {
        return dLng;
    }

    public void setdLng(String dLng) {
        this.dLng = dLng;
    }

    public String getdLat() {
        return dLat;
    }

    public void setdLat(String dLat) {
        this.dLat = dLat;
    }

    public String getoLng() {
        return oLng;
    }

    public void setoLng(String oLng) {
        this.oLng = oLng;
    }

    public String getoLat() {
        return oLat;
    }

    public void setoLat(String oLat) {
        this.oLat = oLat;
    }

    public String getPrecision() {
        return precision;
    }

    public void setPrecision(String precision) {
        this.precision = precision;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getdMnc() {
        return dMnc;
    }

    public void setdMnc(String dMnc) {
        this.dMnc = dMnc;
    }

    public String getdMcc() {
        return dMcc;
    }

    public void setdMcc(String dMcc) {
        this.dMcc = dMcc;
    }

    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }

    @Override
    public String toString() {
        return "Cell{" +
                "mnc=" + mnc +
                ", lac=" + lac +
                ", cell=" + cell +
                ", resultCode='" + resultCode + '\'' +
                ", reason='" + reason + '\'' +
                ", dMnc='" + dMnc + '\'' +
                ", dMcc='" + dMcc + '\'' +
                ", dLac='" + dLac + '\'' +
                ", dCell='" + dCell + '\'' +
                ", dLng='" + dLng + '\'' +
                ", dLat='" + dLat + '\'' +
                ", oLng='" + oLng + '\'' +
                ", oLat='" + oLat + '\'' +
                ", precision='" + precision + '\'' +
                ", address='" + address + '\'' +
                ", adCode='" + adCode + '\'' +
                "} " + super.toString();
    }

    @Transient
    public CellBean getResult() {
        CellBean cellBean = new CellBean();
        BeanUtils.copyProperties(this, cellBean);
        return cellBean;
    }

    public Cell() {
        super();
    }

    public Cell(String requestNo, String sys, String mnc, String lac, String cell) {
        super(requestNo, sys);
        this.mnc = mnc;
        this.lac = lac;
        this.cell = cell;
    }
}
