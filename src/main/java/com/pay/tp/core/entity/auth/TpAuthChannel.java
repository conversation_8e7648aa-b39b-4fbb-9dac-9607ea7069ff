package com.pay.tp.core.entity.auth;

import java.util.Date;

import com.pay.tp.core.enums.ChannelStatus;

public class TpAuthChannel implements java.io.Serializable {

    private Long id;

    private Long optimistic;

    private Date createTime;

    private Date lstModTime;

    private String channelNo;

    private String channelName;

    private String businessCode;

    private Integer priority;

    private ChannelStatus status;

    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOptimistic() {
        return optimistic;
    }

    public void setOptimistic(Long optimistic) {
        this.optimistic = optimistic;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLstModTime() {
        return lstModTime;
    }

    public void setLstModTime(Date lstModTime) {
        this.lstModTime = lstModTime;
    }

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public ChannelStatus getStatus() {
        return status;
    }

    public void setStatus(ChannelStatus status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TpAuthChannel{" +
                "id=" + id +
                ", optimistic=" + optimistic +
                ", createTime=" + createTime +
                ", lstModTime=" + lstModTime +
                ", channelNo='" + channelNo + '\'' +
                ", channelName='" + channelName + '\'' +
                ", businessCode='" + businessCode + '\'' +
                ", priority=" + priority +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}