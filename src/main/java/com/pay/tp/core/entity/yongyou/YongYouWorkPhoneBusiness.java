package com.pay.tp.core.entity.yongyou;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 用友工作手机业务表实体类
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@Accessors(chain = true)
public class YongYouWorkPhoneBusiness implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 乐观锁版本号
     */
    private Long optimistic;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 商户名称
     */
    private String fullName;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 所属地区
     */
    private String organCode;

    /**
     * 验证状态
     */
    private String verifyStatus;

    /**
     * 验证次数
     */
    private Integer verifyReturnNum;

    /**
     * 验证返回值
     */
    private String verifyReturnValue;

    /**
     * 验证返回备注
     */
    private String verifyReturnRemark;

    /**
     * 用友推送状态
     */
    private String yongyouPushStatus;

    /**
     * 推送次数
     */
    private Integer pushReturnNum;

    /**
     * 推送返回值
     */
    private String pushReturnValue;

    /**
     * 推送返回信息
     */
    private String pushReturnMessage;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 推送状态枚举
     */
    public static class PushStatus {
        public static final String INIT = "INIT";
        public static final String SUCCESS = "SUCCESS";
        public static final String FAIL = "FAIL";
    }

    /**
     * 验证状态枚举
     */
    public static class VerifyStatus {
        public static final String INIT = "INIT";
        public static final String SUCCESS = "SUCCESS";
        public static final String FAIL = "FAIL";
    }
}
