package com.pay.tp.core.entity.msgmanage;

import com.pay.tp.core.enums.Status;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MSG_USER
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MsgUser implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 通道编号
     */
    private String channelCode;

    /**
     * 通道用户编号
     */
    private String channelUserNo;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户状态
     */
    private Status status;

    /**
     * 所属用户组编号
     */
    private String groupNo;

    /**
     * 用户组名
     */
    private String groupName;

    /**
     * 说明描述
     */
    private String desc;
}