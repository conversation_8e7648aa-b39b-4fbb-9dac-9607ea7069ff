package com.pay.tp.core.entity;

import java.util.Date;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.entity
 * @Description: TODO
 * @date Date : 2018年12月18日 14:35
 */
public class BaseEntity {

    private Long id;

    private Long optismistic = 0L;

    private Date createTime = new Date();
    private Date updateTime;

    private String requestNo;

    private String sys;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOptismistic() {
        return optismistic;
    }

    public void setOptismistic(Long optismistic) {
        this.optismistic = optismistic;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return createTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getSys() {
        return sys;
    }

    public void setSys(String sys) {
        this.sys = sys;
    }

    public BaseEntity() {}

    @Override
    public String toString() {
        return "BaseEntity{" +
                "id=" + id +
                ", optismistic=" + optismistic +
                ", createTime=" + createTime +
                ", requestNo='" + requestNo + '\'' +
                ", sys='" + sys + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }

    public BaseEntity(String requestNo, String sys) {
        this.requestNo = requestNo;
        this.sys = sys;
    }
}
