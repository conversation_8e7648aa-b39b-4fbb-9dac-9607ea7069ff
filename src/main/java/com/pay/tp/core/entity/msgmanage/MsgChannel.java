package com.pay.tp.core.entity.msgmanage;

import com.pay.tp.core.enums.Status;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MSG_CHANNEL
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MsgChannel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 通道编号
     */
    private String code;

    /**
     * 通道名称
     */
    private String name;

    /**
     * 通道类型
     */
    private String type;

    /**
     * 状态
     */
    private Status status;

    /**
     * 权重
     */
    private Long weight;

    /**
     * 通道描述
     */
    private String desc;
}