package com.pay.tp.core.controller.position;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.pay.tp.core.exception.NoValidDataException;

/**
 * <AUTHOR> z<PERSON><PERSON>an
 * @Package com.pay.boss.core.exception
 * @date Date : 2018年12月17日 20:05
 */
@RestControllerAdvice
public class JsonExceptionHandler {

	
    @ExceptionHandler(value = NoValidDataException.class)
    public ResponseEntity<String> resolveBossBadRequestException(HttpServletRequest request, HttpServletResponse response, Exception e) {
        return ResponseEntity.status(HttpStatus.CONFLICT).body(e.getMessage());
    }
    
    
}
