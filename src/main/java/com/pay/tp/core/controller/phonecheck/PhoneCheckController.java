package com.pay.tp.core.controller.phonecheck;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.phonecheck.PhoneCheckReq;
import com.pay.tp.core.beans.phonecheck.PhoneCheckResp;
import com.pay.tp.core.biz.impl.PhoneCheckBiz;
import com.pay.tp.core.entity.phonecheck.PhoneCheckRecord;
import com.pay.tp.core.service.phonecheck.PhoneCheckRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 号码检测控制器
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@RestController
@RequestMapping("/api/phonecheck")
public class PhoneCheckController {

    @Autowired
    private PhoneCheckBiz phoneCheckBiz;

    @Autowired
    private PhoneCheckRecordService phoneCheckRecordService;

    /**
     * 空号检测
     * @param request 检测请求
     * @return 检测结果
     */
    @PostMapping("/empty")
    public ResultsBean<PhoneCheckResp> checkEmptyNumber(@Valid @RequestBody PhoneCheckReq request) {
        try {
            log.info("收到空号检测请求，请求号: {}, 手机号数量: {}", 
                    request.getRequestNo(), request.getPhoneNumbers().size());
            
            // 设置检测类型
            request.setCheckType(PhoneCheckReq.CheckType.EMPTY_NUMBER);
            
            // 如果没有请求号，自动生成
            if (StringUtils.isBlank(request.getRequestNo())) {
                request.setRequestNo(phoneCheckBiz.generateRequestNo());
            }
            
            return phoneCheckBiz.checkPhoneNumbers(request);
            
        } catch (Exception e) {
            log.error("空号检测异常", e);
            return ResultsBean.FAIL("空号检测异常: " + e.getMessage());
        }
    }

    /**
     * 实名检测
     * @param request 检测请求
     * @return 检测结果
     */
    @PostMapping("/realname")
    public ResultsBean<PhoneCheckResp> checkRealName(@Valid @RequestBody PhoneCheckReq request) {
        try {
            log.info("收到实名检测请求，请求号: {}, 手机号数量: {}", 
                    request.getRequestNo(), request.getPhoneNumbers().size());
            
            // 设置检测类型
            request.setCheckType(PhoneCheckReq.CheckType.REAL_NAME);
            
            // 如果没有请求号，自动生成
            if (StringUtils.isBlank(request.getRequestNo())) {
                request.setRequestNo(phoneCheckBiz.generateRequestNo());
            }
            
            return phoneCheckBiz.checkPhoneNumbers(request);
            
        } catch (Exception e) {
            log.error("实名检测异常", e);
            return ResultsBean.FAIL("实名检测异常: " + e.getMessage());
        }
    }

    /**
     * 风控检测
     * @param request 检测请求
     * @return 检测结果
     */
    @PostMapping("/risk")
    public ResultsBean<PhoneCheckResp> checkRiskControl(@Valid @RequestBody PhoneCheckReq request) {
        try {
            log.info("收到风控检测请求，请求号: {}, 手机号数量: {}", 
                    request.getRequestNo(), request.getPhoneNumbers().size());
            
            // 设置检测类型
            request.setCheckType(PhoneCheckReq.CheckType.RISK_CONTROL);
            
            // 如果没有请求号，自动生成
            if (StringUtils.isBlank(request.getRequestNo())) {
                request.setRequestNo(phoneCheckBiz.generateRequestNo());
            }
            
            return phoneCheckBiz.checkPhoneNumbers(request);
            
        } catch (Exception e) {
            log.error("风控检测异常", e);
            return ResultsBean.FAIL("风控检测异常: " + e.getMessage());
        }
    }

    /**
     * 批量号码检测（支持大批量）
     * @param request 检测请求
     * @return 检测结果
     */
    @PostMapping("/batch")
    public ResultsBean<PhoneCheckResp> batchCheck(@Valid @RequestBody PhoneCheckReq request) {
        try {
            log.info("收到批量检测请求，请求号: {}, 检测类型: {}, 手机号数量: {}", 
                    request.getRequestNo(), request.getCheckType(), request.getPhoneNumbers().size());
            
            // 如果没有请求号，自动生成
            if (StringUtils.isBlank(request.getRequestNo())) {
                request.setRequestNo(phoneCheckBiz.generateRequestNo());
            }
            
            return phoneCheckBiz.batchCheckPhoneNumbers(request);
            
        } catch (Exception e) {
            log.error("批量检测异常", e);
            return ResultsBean.FAIL("批量检测异常: " + e.getMessage());
        }
    }

    /**
     * 根据请求号查询检测结果
     * @param requestNo 请求号
     * @return 检测结果
     */
    @GetMapping("/result/{requestNo}")
    public ResultsBean<List<PhoneCheckRecord>> getResultByRequestNo(@PathVariable String requestNo) {
        try {
            log.info("查询检测结果，请求号: {}", requestNo);
            
            List<PhoneCheckRecord> records = phoneCheckRecordService.findByRequestNo(requestNo);
            
            return ResultsBean.SUCCESS(records);
            
        } catch (Exception e) {
            log.error("查询检测结果异常，请求号: {}", requestNo, e);
            return ResultsBean.FAIL("查询检测结果异常: " + e.getMessage());
        }
    }

    /**
     * 根据手机号查询最近的检测记录
     * @param phoneNumber 手机号
     * @param checkType 检测类型（可选）
     * @param limit 查询数量限制（默认10）
     * @return 检测记录
     */
    @GetMapping("/history/{phoneNumber}")
    public ResultsBean<List<PhoneCheckRecord>> getHistoryByPhone(
            @PathVariable String phoneNumber,
            @RequestParam(required = false) String checkType,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            log.info("查询手机号检测历史，手机号: {}, 检测类型: {}, 限制数量: {}", 
                    phoneNumber, checkType, limit);
            
            List<PhoneCheckRecord> records = phoneCheckRecordService.findRecentByPhone(phoneNumber, checkType, limit);
            
            return ResultsBean.SUCCESS(records);
            
        } catch (Exception e) {
            log.error("查询手机号检测历史异常，手机号: {}", phoneNumber, e);
            return ResultsBean.FAIL("查询检测历史异常: " + e.getMessage());
        }
    }

    /**
     * 分页查询检测记录
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param requestNo 请求号（可选）
     * @param phoneNumber 手机号（可选）
     * @param checkType 检测类型（可选）
     * @param merchantNo 商户编号（可选）
     * @param status 状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    @GetMapping("/records")
    public ResultsBean<PageInfo<Map<String, Object>>> getRecords(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String requestNo,
            @RequestParam(required = false) String phoneNumber,
            @RequestParam(required = false) String checkType,
            @RequestParam(required = false) String merchantNo,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            log.info("分页查询检测记录，页码: {}, 页大小: {}", pageNum, pageSize);
            
            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(requestNo)) {
                params.put("requestNo", requestNo);
            }
            if (StringUtils.isNotBlank(phoneNumber)) {
                params.put("phoneNumber", phoneNumber);
            }
            if (StringUtils.isNotBlank(checkType)) {
                params.put("checkType", checkType);
            }
            if (StringUtils.isNotBlank(merchantNo)) {
                params.put("merchantNo", merchantNo);
            }
            if (status != null) {
                params.put("status", status);
            }
            if (StringUtils.isNotBlank(startTime)) {
                params.put("startTime", startTime);
            }
            if (StringUtils.isNotBlank(endTime)) {
                params.put("endTime", endTime);
            }
            
            PageInfo<Map<String, Object>> pageInfo = phoneCheckRecordService.findByPageAll(pageNum, pageSize, params);
            
            return ResultsBean.SUCCESS(pageInfo);
            
        } catch (Exception e) {
            log.error("分页查询检测记录异常", e);
            return ResultsBean.FAIL("查询记录异常: " + e.getMessage());
        }
    }

    /**
     * 统计商户检测数量
     * @param merchantNo 商户编号
     * @param checkType 检测类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计结果
     */
    @GetMapping("/statistics/{merchantNo}")
    public ResultsBean<Map<String, Object>> getStatistics(
            @PathVariable String merchantNo,
            @RequestParam(required = false) String checkType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            log.info("统计商户检测数量，商户编号: {}, 检测类型: {}", merchantNo, checkType);
            
            Map<String, Object> statistics = phoneCheckRecordService.countByMerchant(merchantNo, checkType, startTime, endTime);
            
            return ResultsBean.SUCCESS(statistics);
            
        } catch (Exception e) {
            log.error("统计商户检测数量异常，商户编号: {}", merchantNo, e);
            return ResultsBean.FAIL("统计异常: " + e.getMessage());
        }
    }

    /**
     * 生成请求号
     * @return 请求号
     */
    @GetMapping("/generateRequestNo")
    public ResultsBean<String> generateRequestNo() {
        try {
            String requestNo = phoneCheckBiz.generateRequestNo();
            return ResultsBean.SUCCESS(requestNo);
        } catch (Exception e) {
            log.error("生成请求号异常", e);
            return ResultsBean.FAIL("生成请求号异常: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResultsBean<String> health() {
        return ResultsBean.SUCCESS("号码检测服务运行正常");
    }
}
