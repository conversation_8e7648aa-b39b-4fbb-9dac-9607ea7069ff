package com.pay.tp.core.controller.wechat;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.sms.SmsMsgReq;
import com.pay.tp.core.biz.impl.WorkWechatBiz;
import com.pay.tp.core.controller.dingding.DingDingMsgController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @package com.pay.tp.core.controller.wechat
 * @dscription
 * @date 2024-03-20 17:30
 */
@RestController
@RequestMapping("/workWechat")
public class WorkWechatMsgController {
	private final Logger logger = LoggerFactory.getLogger(WorkWechatMsgController.class);

	@Autowired
	private WorkWechatBiz workWechatBiz;

	/**
	 *
	 * 新建通知 接入此接口
	 * 发送钉钉机器人通知消息
	 *
	 * @param msgReq
	 * @return code = 00，发送成功; code = 99, 发送失败; msg在失败时候为原因说明
	 */
	@PostMapping("/msg/send")
	public ResultsBean<String> msgSend(@RequestBody SmsMsgReq msgReq) {
		logger.info("WorkWechatMsgController sendRobotMsg {}。", msgReq);
		boolean b = workWechatBiz.sendRobotMsgTemplateCode(msgReq.getRequestNo(),
				msgReq.getMsgType(),
				msgReq.getTemplateCode(),
				msgReq.getTemplateParam());

		if (b) {
			return ResultsBean.SUCCESS("发送成功");
		} else {
			return ResultsBean.FAIL("发送失败");
		}
	}
}
