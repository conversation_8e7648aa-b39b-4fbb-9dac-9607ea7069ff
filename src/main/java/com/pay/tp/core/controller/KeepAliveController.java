package com.pay.tp.core.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目名称：trade
 * 类 名 称：KeepAliveController
 * 类 描 述：心跳检测
 * 创建时间：2021/7/30 19:57
 * 创 建 人：wangxingwei
 */
@RestController
@RequestMapping("/KeepAlive")
public class KeepAliveController {
    private static final String SUCCESS = "0000";

    private static Logger log = LoggerFactory.getLogger("abstractLogger");

    @RequestMapping("")
    public String keepAlive() {
        log.info("[KeepAlive][0000]Application is running stably.");
        return SUCCESS;
    }

}
