package com.pay.tp.core.controller.sms;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.pay.tp.core.entity.sms.SmsMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pay.tp.core.entity.sms.SmsRes;
import com.pay.tp.core.remote.sms.XiaoMaClient;
import com.pay.tp.core.service.sms.SmsMsgService;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.controller
 * @Description: TODO
 * @date Date : 2018年12月26日 21:30
 */
//@RestController
//@RequestMapping("/xiaoma")
public class XiaomaController {

    private final Logger logger = LoggerFactory.getLogger(XiaomaController.class);


}
