package com.pay.tp.core.controller.baidu;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.enums.FileBussinessType;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.tp.core.beans.BaiDuOcrBean;
import com.pay.tp.core.beans.BaiDuPersonVerifyBean;
import com.pay.tp.core.biz.impl.BaiDuBiz;

import lombok.extern.slf4j.Slf4j;

/**
 *
 */
@Slf4j
@RestController
@RequestMapping("/baiDu")
public class BaiDuController {

    @Autowired
    private BaiDuBiz baiDuBiz;
    @RequestMapping(value = "/ocrYs", method = RequestMethod.POST)
    public ResultsBean ocrYs(@RequestBody @Validated BaiDuOcrBean baiDuOcrBean) {
        try{
        	Map<String, Object> object = null;
            String imageType = baiDuOcrBean.getImageType();

            if (FileBussinessType.BANK_CARD.getCode().equals(imageType)) {
                object = baiDuBiz.ocrbankcardYs(baiDuOcrBean);

            }else if(FileBussinessType.ID_CAED_RS.getCode().equals(imageType)
                    || FileBussinessType.ID_CAED_WS.getCode().equals(imageType)){
                object = baiDuBiz.ocrIdCardYs(baiDuOcrBean);

            }else if(FileBussinessType.FACE_DETECT.getCode().equals(imageType)) {
                object = baiDuBiz.ocrFaceDetectYs(baiDuOcrBean);

            }else {
                return ResultsBean.FAIL("不支持的ocr识别类型");
            }

            return ResultsBean.SUCCESS(object);

        } catch (ServerException ser){
            return ResultsBean.FAIL(ser.getMessage());
        } catch (Exception e){
            return ResultsBean.FAIL("识别异常");
        }
    
    }

    /**
     *  百度ocr识别
     * @return
     */
    @RequestMapping(value = "/ocr", method = RequestMethod.POST)
    public ResultsBean ocr(@RequestBody @Validated BaiDuOcrBean baiDuOcrBean) {
        try{
        	Map<String, Object> object = null;
            String imageType = baiDuOcrBean.getImageType();

            if (FileBussinessType.BANK_CARD.getCode().equals(imageType)) {
                object = baiDuBiz.ocrbankcard(baiDuOcrBean);

            }else if(FileBussinessType.ID_CAED_RS.getCode().equals(imageType)
                    || FileBussinessType.ID_CAED_WS.getCode().equals(imageType)){
                object = baiDuBiz.ocrIdCard(baiDuOcrBean);

            }else if(FileBussinessType.FACE_DETECT.getCode().equals(imageType)) {
                object = baiDuBiz.ocrFaceDetect(baiDuOcrBean);

            }else {
                return ResultsBean.FAIL("不支持的ocr识别类型");
            }

            return ResultsBean.SUCCESS(object);

        } catch (ServerException ser){
            return ResultsBean.FAIL(ser.getMessage());
        } catch (Exception e){
            return ResultsBean.FAIL("识别异常");
        }
    }
    @RequestMapping(value = "/personVerify", method = RequestMethod.POST)
    public ResultsBean personVerify(@RequestBody @Validated BaiDuPersonVerifyBean bean) {
    	try{
    		Map<String, Object> object = baiDuBiz.personVerify(bean);
    		return ResultsBean.SUCCESS(object);
    	} catch (ServerException ser){
    		return ResultsBean.FAIL(ser.getMessage());
    	} catch (Exception e){
    		return ResultsBean.FAIL("识别异常");
    	}
    }


//    /**
//     *  百度获取H5的 verify_token
//     * @return
//     */
//    @RequestMapping(value = "/verifyToken", method = RequestMethod.POST)
//    public ResultsBean<String> verifyToken(){
//        String verifyToken = baiDuBiz.verifyToken();
//        return ResultsBean.SUCCESS(verifyToken);
//    }


}
