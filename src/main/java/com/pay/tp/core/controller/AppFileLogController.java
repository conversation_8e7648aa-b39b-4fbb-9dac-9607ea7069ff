package com.pay.tp.core.controller;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.biz.impl.AppFileLogBiz;
import com.pay.tp.core.entity.AppFileLog;
import com.pay.tp.core.service.AppFileLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> @date 2024/3/20
 * @apiNote
 */
@Slf4j
@RestController
@RequestMapping("/appFileLog")
public class AppFileLogController {

    @Autowired
    private AppFileLogService appFileLogService;

    @Autowired
    private AppFileLogBiz appFileLogBiz;



    /**
     * 分页查询
     * @param param
     */
    @RequestMapping("/findPage")
    public ResultsBean<PageInfo<AppFileLog>> findPage(@RequestBody Map<String, String> param) {
        PageInfo<AppFileLog> page = appFileLogService.findPage(param);
        return ResultsBean.SUCCESS(page);
    }


    @RequestMapping("/downAppFileLog")
    public String downAppFileLog(@RequestParam("id") Long id) {
        String filePath = appFileLogBiz.downAppFileLog(id);
        return filePath;
    }


}
