package com.pay.tp.core.controller.logistics;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.utils.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名称：trade-transfer
 * 类 名 称：LogisticsController
 * 类 描 述：物流
 * 创建时间：2021/11/11 17:24
 * 创 建 人：wangxingwei
 */
@Slf4j
@RestController
@RequestMapping("/logistics")
public class LogisticsController {

    @Value("${kuaidi100.host:}")
    private String host;

    @Value("${kuaidi100.path:}")
    private String path;

    @Value("${kuaidi100.customer:}")
    private String customer;
    @Value("${kuaidi100.key:}")
    private String key;

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 物流查询
     */
    @PostMapping("/query")
    public ResultsBean<String> query(@RequestBody Map<String, String> reqParams) {
        log.info("logistics query {}", reqParams);
        Map<String, String> params = new HashMap<>();

        try {
//        	https://api.kuaidi100.com/document/5f0ffb5ebc8da837cbd8aefc.html
            Map<String, String> map = new HashMap<>();
            map.put("com", reqParams.get("com"));
            map.put("num", reqParams.get("num"));
            if (!StringUtils.isEmpty(reqParams.get("phone"))) {
                map.put("phone", reqParams.get("phone"));
            }
            map.put("resultv2", "1");
            map.put("show", "0");
            map.put("order", "desc");
            String paramStr = objectMapper.writeValueAsString(map);

            params.put("customer", this.customer);
            params.put("sign", Md5Util.encodeV2(paramStr + this.key + this.customer));
            params.put("param", paramStr);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultsBean.FAIL(e.getMessage());
        }

        try (CloseableHttpClient httpclient = HttpClientBuilder.create().build()) {
            List<NameValuePair> nameValuePair = new ArrayList<NameValuePair>();
            params.forEach((k, v) -> {
                if (v != null) {
                    nameValuePair.add(new BasicNameValuePair(k, v));
                }
            });

            URI uri = new URIBuilder().addParameters(nameValuePair).setHost(host).setPath(path).setScheme("https").build();

            HttpPost httpPost = new HttpPost(uri);
            if (!StringUtils.isEmpty(proxyHost)) {
                RequestConfig config =
                        RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
                httpPost.setConfig(config);
            }
            try (CloseableHttpResponse response = httpclient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                if (null != entity) {
                    String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    log.info("express request result = {}", result);
                    EntityUtils.consume(entity);
                    return ResultsBean.SUCCESS(result);
                }
            }
            return ResultsBean.FAIL("无物流信息");
        } catch (IOException | URISyntaxException e) {
            log.error(e.getMessage(), e);
            return ResultsBean.FAIL(e.getMessage());
        }
    }

}
