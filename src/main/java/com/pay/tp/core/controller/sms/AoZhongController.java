package com.pay.tp.core.controller.sms;

import com.alibaba.fastjson.JSONArray;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.remote.sms.AoZhongClient;
import com.pay.tp.core.service.sms.SmsMsgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @date 2022年08月02日 10:53
 */
@RestController
@RequestMapping("/aozhong")
public class AoZhongController {
    private final Logger logger = LoggerFactory.getLogger(AoZhongController.class);

    @Autowired
    private SmsMsgService smsMsgService;

    @Autowired
    private AoZhongClient aoZhongClient;

    @PostMapping
    public ResultsBean<String> callback(@RequestBody String json) {
        logger.info("method = callback, map = {}", json);
        List<Map<String, String>> list = JSONArray.parseObject(json, List.class);
        List<SmsMsg> msgList = aoZhongClient.parse(list);
        for (SmsMsg smsMsg : msgList) {
            try {
                SmsMsg msg = smsMsgService.findByMsgIdPhone(smsMsg.getPhone(), smsMsg.getMsgId());
                logger.info("aozhong callback:手机号：{}, 回执id:{}, {} {}, 回执:{}{}", smsMsg.getPhone(), smsMsg.getMsgId(), msg.getResResult(), msg.getResMessage(),
                        smsMsg.getResResult(), smsMsg.getResMessage());
                if(StringUtils.isEmpty(msg.getResResult()) && StringUtils.isEmpty(msg.getResMessage())){
                    msg.setResResult(smsMsg.getResResult());
                    msg.setResMessage(smsMsg.getResMessage());
                    smsMsgService.updateResMsg(msg);
                }
            } catch (Exception e) {
                logger.error("aozhong 短信回执更新失败：{},{}", smsMsg, e);
            }
        }
        return ResultsBean.SUCCESS();
    }

}
