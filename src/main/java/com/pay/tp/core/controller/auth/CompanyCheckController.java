package com.pay.tp.core.controller.auth;


import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.pay.tp.core.beans.auth.CompanyCheckParam;
import com.pay.tp.core.entity.auth.CompanyCheck;
import com.pay.tp.core.remote.auth.xinlian.XlCompanyCheckClient;
import com.pay.tp.core.service.auth.CompanyCheckService;



/**企业四要素核验Api
* <AUTHOR>
* @version 创建时间：2018年12月19日 下午2:08:09
* @ClassName 
* @Description 
*/
@RestController
@RequestMapping("company")
public class CompanyCheckController {

	private final Logger logger = LoggerFactory.getLogger(CompanyCheckController.class);
	
	@Autowired
	private CompanyCheckService companyCheckService;

	@Autowired
	private XlCompanyCheckClient xlCompanyCheckClient;

	/**企业四要素核验API
	 * code = 200, message = "result = \"01\", msg = \"验证通过\",result = \"02\", msg = \"验证不通过（具体说明）\""
	 * <AUTHOR>
	 * @date 2018年12月19日 下午11:15:20
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public Map<String,Object> auth(@Validated @RequestBody CompanyCheckParam param) throws Exception {

//		logger.info("method = auth, param = {}", param);
		Map<String,Object> map = new HashMap<>();
		//要求幂等
		CompanyCheck companyCheck = companyCheckService.query(param);
		if (companyCheck.getRspCod() != null) {
			return xlCompanyCheckClient.parse(companyCheck);
		} else {
			Map<String,Object> res = xlCompanyCheckClient.request(companyCheck.getRequestNo(),companyCheck.getEntName(), companyCheck.getRegNo(), companyCheck.getFrName(), companyCheck.getCidNo());
			companyCheck.setTransDate(res.get("transDate")+"");
			companyCheck.setPaySerialNo(res.get("paySerialNo") + "");
			companyCheck.setRspMsg(res.get("rspMsg") + "");
			companyCheck.setTransTime(res.get("transTime") + "");
			companyCheck.setRspCod(res.get("rspCod") + "");
			companyCheck.setValidateDescribe(res.get("validateDescribe") + "");
			companyCheck.setValidateStatus(res.get("validateStatus") + "");
			String data = res.get("data")+"";
			if(data.length() > 500) {
				data = data.substring(0, 500);
			}
			companyCheck.setData(data);
			companyCheckService.update(companyCheck);
			return xlCompanyCheckClient.parse(companyCheck);
		}
	}
}
