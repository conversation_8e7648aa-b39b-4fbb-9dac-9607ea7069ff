package com.pay.tp.core.controller.ali;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.ali.UploudBase64Req;
import com.pay.tp.core.beans.ali.UploudBase64Res;
import com.pay.tp.core.biz.impl.ALiYunBiz;
import com.pay.tp.core.utils.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @date 2022年08月18日 15:24
 */
@Slf4j
@RestController
@RequestMapping("/aliyun")
public class ALiYunController {

    @Autowired
    private ALiYunBiz aLiYunBiz;


    /**
     * 获取token
     * @return
     * @throws Exception 
     */
    @RequestMapping(value = "/findToken")
    public ResultsBean<Map<String, String>> findToken(@RequestBody(required = false) Map<String, String> params) throws Exception{
        Map<String, String> map = aLiYunBiz.findToken(params);
        log.info("findToken map:{}", map);
        return ResultsBean.SUCCESS(map);
    }

    @PostMapping(value = "uploadBase64File")
    public ResultsBean<UploudBase64Res> uploadBase64File(@RequestBody UploudBase64Req uploudReq) {
        CheckUtil.validator(uploudReq);
        String ossPath = aLiYunBiz.uploadBase64File(uploudReq.getFileName(), uploudReq.getBase64());
        return ResultsBean.SUCCESS(new UploudBase64Res(ossPath));
    }

//    @GetMapping(value = "/testOssGenUrl")
//    public ResultsBean<String> testOssGenUrl(@RequestParam("fileName") String fileName) throws Exception{
//        String signedUrl = aLiYunBiz.getSignedUrl(fileName, false);
//        log.info("testOssGenUrl url:{}", signedUrl);
//        return ResultsBean.SUCCESS(signedUrl);
//    }
}
