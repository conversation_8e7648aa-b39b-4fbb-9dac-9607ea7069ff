-- 号码检测记录表
CREATE TABLE UBADMA.PHONE_CHECK_RECORD (
    ID NUMBER(12,0) NOT NULL,
    REQUEST_NO VARCHAR2(50 BYTE) NOT NULL,
    PHONE_NUMBER VARCHAR2(20 BYTE) NOT NULL,
    CHECK_TYPE VARCHAR2(20 BYTE) NOT NULL,
    MERCHANT_NO VARCHAR2(30 BYTE),
    BRAND VARCHAR2(20 BYTE),
    STATUS NUMBER(2,0),
    STATUS_DESC VARCHAR2(100 BYTE),
    CARRIER VARCHAR2(20 BYTE),
    PROVINCE VARCHAR2(50 BYTE),
    CITY VARCHAR2(50 BYTE),
    NUMBER_TYPE NUMBER(2,0),
    RISK_LEVEL VARCHAR2(20 BYTE),
    REAL_NAME_STATUS NUMBER(2,0),
    CHECK_CHANNEL VARCHAR2(50 BYTE),
    COST_TIME NUMBER(10,0),
    RAW_RESPONSE CLOB,
    ERROR_MSG VARCHAR2(500 BYTE),
    EXT_PARAMS VARCHAR2(1000 BYTE),
    CREATE_TIME DATE DEFAULT sysdate,
    UPDATE_TIME DATE DEFAULT sysdate
) TABLESPACE TBS_PAY_CORE_DATA;

-- 主键约束
ALTER TABLE UBADMA.PHONE_CHECK_RECORD ADD CONSTRAINT PK_PHONE_CHECK_RECORD PRIMARY KEY (ID) USING INDEX TABLESPACE TBS_PAY_CORE_DATA;

-- 索引
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_RN ON UBADMA.PHONE_CHECK_RECORD (REQUEST_NO ASC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_PN ON UBADMA.PHONE_CHECK_RECORD (PHONE_NUMBER ASC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_CT ON UBADMA.PHONE_CHECK_RECORD (CHECK_TYPE ASC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_MN ON UBADMA.PHONE_CHECK_RECORD (MERCHANT_NO ASC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_TIME ON UBADMA.PHONE_CHECK_RECORD (CREATE_TIME DESC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_STATUS ON UBADMA.PHONE_CHECK_RECORD (STATUS ASC) TABLESPACE TBS_PAY_CORE_DATA;

-- 复合索引
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_PN_CT ON UBADMA.PHONE_CHECK_RECORD (PHONE_NUMBER, CHECK_TYPE, CREATE_TIME DESC) TABLESPACE TBS_PAY_CORE_DATA;
CREATE INDEX UBADMA.IX_PHONE_CHECK_RECORD_RN_PN ON UBADMA.PHONE_CHECK_RECORD (REQUEST_NO, PHONE_NUMBER) TABLESPACE TBS_PAY_CORE_DATA;

-- 创建序列
CREATE SEQUENCE UBADMA.SEQ_PHONE_CHECK_RECORD_ID
    START WITH 1
    INCREMENT BY 1
    NOMAXVALUE
    NOCYCLE
    CACHE 20;

-- 字段注释
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.ID IS '主键ID';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.REQUEST_NO IS '请求号';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.PHONE_NUMBER IS '手机号';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.CHECK_TYPE IS '检测类型(EMPTY_NUMBER:空号检测,REAL_NAME:实名检测,RISK_CONTROL:风控检测)';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.MERCHANT_NO IS '商户编号';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.BRAND IS '品牌标识';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.STATUS IS '检测状态(1:正常,0:空号/异常,-1:检测失败)';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.STATUS_DESC IS '状态描述';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.CARRIER IS '运营商(CMCC:移动,CUCC:联通,CTCC:电信)';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.PROVINCE IS '归属地省份';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.CITY IS '归属地城市';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.NUMBER_TYPE IS '号码类型(1:手机号,2:固话,3:虚拟号)';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.RISK_LEVEL IS '风险等级(LOW:低风险,MEDIUM:中风险,HIGH:高风险)';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.REAL_NAME_STATUS IS '实名状态(1:已实名,0:未实名,-1:无法确定)';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.CHECK_CHANNEL IS '检测渠道';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.COST_TIME IS '检测耗时(毫秒)';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.RAW_RESPONSE IS '原始响应';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.ERROR_MSG IS '错误信息';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.EXT_PARAMS IS '扩展参数';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN UBADMA.PHONE_CHECK_RECORD.UPDATE_TIME IS '更新时间';
COMMENT ON TABLE UBADMA.PHONE_CHECK_RECORD IS '号码检测记录表';

-- 授权
GRANT SELECT,UPDATE,INSERT,DELETE ON UBADMA.PHONE_CHECK_RECORD TO UPCOREA;

