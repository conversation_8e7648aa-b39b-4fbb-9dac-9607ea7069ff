package com.pay.tp.core.controller.position;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.util.JsonUtils;
import com.pay.tp.core.beans.position.CellBean;
import com.pay.tp.core.beans.position.CellParam;
import com.pay.tp.core.entity.position.Cell;
import com.pay.tp.core.remote.position.LDYSCellClient;
import com.pay.tp.core.service.position.LDYSCellServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 联动优势-基站定位Api
 */
@RestController
@RequestMapping("cell")
public class LDYSCellController {
    private final Logger logger = LoggerFactory.getLogger(LDYSCellController.class);

    @Autowired
    private LDYSCellServiceImpl lDYSCellServiceImpl;

    @Autowired
    private LDYSCellClient ldysCellClient;


    @RequestMapping(value = "getGps", method = RequestMethod.POST)
    public ResultsBean<CellBean> cell(@Validated @RequestBody CellParam param) throws Exception {
//        logger.info("method = cell, {} param = {}", param.getRequestNo(), param);
        if (StringUtils.isEmpty(param.getCell()) || StringUtils.isEmpty(param.getLac())
                || StringUtils.isEmpty(param.getMnc())) {
            return ResultsBean.FAIL("参数为空或格式错误");
        }

        StopWatch stopWatch = new StopWatch("基站定位");
        try {
            //要求幂等
            stopWatch.start("查询数据库");
            Cell cell = lDYSCellServiceImpl.query(param);
            stopWatch.stop();
            if (cell != null) {
                CellBean result = cell.getResult();
//                logger.info("method = cell, {}  db result = {}", param.getRequestNo(), result);
                return ResultsBean.SUCCESS(result);
            }

            stopWatch.start("调用三方服务");
            LDYSCellClient.LDYSCellParam cellParam = new LDYSCellClient.LDYSCellParam();
            cellParam.setBts(cellParam.generateBts(param.getCell(), param.getMcc(), param.getMnc(), param.getLac()));
            ResultsBean<LDYSCellClient.StationRsp> resultsBean = ldysCellClient.stationQuery(cellParam);
            stopWatch.stop();
            if (resultsBean == null || resultsBean.notSuccess()
                    || resultsBean.getObject() == null) {
            	logger.info("method = cell, {} api result = {}", param.getRequestNo(), resultsBean);
                return ResultsBean.FAIL("三方未返回数据");
            }

            LDYSCellClient.StationRsp stationRsp = resultsBean.getObject();
            // update Or insert
            cell = convertCell(param, stationRsp);
            cell.setResultCode(resultsBean.getCode());
            cell.setReason(resultsBean.getMessage());
            cell.setRequestNo(param.getRequestNo());
            stopWatch.start("保存数据");
            lDYSCellServiceImpl.saveOrUpdate(cell);
            stopWatch.stop();
            return ResultsBean.SUCCESS(cell.getResult());
        }catch (Exception e) {
            logger.error("method = getGps, error", e);
            return ResultsBean.FAIL("系统异常");
        }finally {
            if(stopWatch.isRunning()){
                stopWatch.stop();
            }
            logger.info("method = getGps, stopWatch info {}", stopWatch.toString());
        }
    }

    /**
     * 数据转换
     *
     * @param param
     * @param stationRsp
     * @return
     */
    private Cell convertCell(CellParam param, LDYSCellClient.StationRsp stationRsp) {
        Cell cell = new Cell();
        cell.setMnc(param.getMnc());
        cell.setLac(param.getLac());
        cell.setAdCode(stationRsp.getAdCode());
        cell.setCell(param.getCell());
        cell.setdMnc(param.getMnc());
        cell.setdCell(param.getCell());
        cell.setdLac(param.getLac());
        if (!StringUtils.isEmpty(stationRsp.getLocation())) {
            String[] split = stationRsp.getLocation().split(",");
            if (split.length == 2) {
                cell.setdLng(split[0]);
                cell.setdLat(split[1]);
                cell.setoLng(split[0]);
                cell.setoLat(split[1]);
            }
        }

        cell.setAddress(stationRsp.getDesc());
        return cell;
    }

    @PostMapping("findById")
    ResultsBean<Cell> findById(@RequestParam("id") Long id) {
        logger.info("method = findById, param = {}", id);
        Cell cell = lDYSCellServiceImpl.findById(id);
        return ResultsBean.SUCCESS(cell);
    }

    @RequestMapping(value = "queryPage", method = RequestMethod.POST)
    public ResultsBean<PageInfo<Cell>> queryPage(@RequestBody Map<String, Object> params) throws Exception {
        logger.info("method = queryPage, param = {}", params);
        PageInfo<Cell> pageInfo = lDYSCellServiceImpl.findByCondition(params);
        return ResultsBean.SUCCESS(pageInfo);
    }
}
