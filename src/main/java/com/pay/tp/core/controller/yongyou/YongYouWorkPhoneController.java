package com.pay.tp.core.controller.yongyou;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.yongyou.YongYouCustomerReq;
import com.pay.tp.core.beans.yongyou.YongYouCustomerResp;
import com.pay.tp.core.biz.impl.YongYouBiz;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用友工作手机控制器
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@RestController
@RequestMapping("/yongYou")
public class YongYouWorkPhoneController {
    private final Logger logger = LoggerFactory.getLogger(YongYouWorkPhoneController.class);

    @Autowired
    private YongYouBiz yongYouBiz;

    /**
     * 添加客户
     *
     * @param customerReq 客户信息请求
     * @return 添加结果
     */
    @PostMapping("/customer/add")
    public ResultsBean<YongYouCustomerResp> addCustomer(@RequestBody @Valid YongYouCustomerReq customerReq) {
        logger.info("用友添加客户请求: {}", customerReq);

        YongYouCustomerResp result = yongYouBiz.addCustomer(customerReq);

        logger.info("用友添加客户响应: {}", result);

        return ResultsBean.SUCCESS(result);
    }
}
