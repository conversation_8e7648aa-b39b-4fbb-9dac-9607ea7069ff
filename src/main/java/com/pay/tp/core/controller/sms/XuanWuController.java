package com.pay.tp.core.controller.sms;

import com.pay.tp.core.entity.sms.SmsRes;
import com.pay.tp.core.remote.sms.XuanWuClient;
import com.pay.tp.core.service.sms.SmsMsgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.controller
 * @Description: TODO
 * @date Date : 2018年12月26日 21:30
 */
//@RestController
//@RequestMapping("/xuanwu")
public class XuanWuController {

    private final Logger logger = LoggerFactory.getLogger(XiaomaController.class);

    @Autowired
    private SmsMsgService smsMsgService;

    @Autowired
    private XuanWuClient xuanWuClient;



}
