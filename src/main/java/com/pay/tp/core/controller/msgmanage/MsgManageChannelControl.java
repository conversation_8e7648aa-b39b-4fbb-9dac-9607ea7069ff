package com.pay.tp.core.controller.msgmanage;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.tp.core.beans.sms.SmsMsgChannelConfigDto;
import com.pay.tp.core.beans.sms.SmsMsgChannelDto;
import com.pay.tp.core.beans.sms.SmsMsgRsp;
import com.pay.tp.core.biz.impl.DingDingBiz;
import com.pay.tp.core.entity.msgmanage.MsgChannel;
import com.pay.tp.core.entity.msgmanage.MsgChannelConfig;
import com.pay.tp.core.service.msgmanage.MsgChannelConfigService;
import com.pay.tp.core.service.msgmanage.MsgChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 消息通讯，通道管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/msg/manage/channel")
public class MsgManageChannelControl {

    @Autowired
    private MsgChannelService msgChannelService;
    @Autowired
    private MsgChannelConfigService msgChannelConfigService;
    @Autowired
    private DingDingBiz dingDingBiz;


    /**
     * 分页查询通道列表
     */
    @PostMapping("/pageChannel")
    public PageInfo<SmsMsgChannelDto> pageChannel(@RequestParam Map<String, Object> params) {
        log.info("page channel {}", params);
        Integer pageNum = Integer.valueOf(params.getOrDefault("currentPage", 1).toString());
        PageHelper.startPage(pageNum, 10);
        List<MsgChannel> channels = msgChannelService.findByParams(params);
        PageInfo page = new PageInfo(channels);
        List<SmsMsgChannelDto> dtoList = channels.stream().map(e -> new SmsMsgChannelDto().convertFor(e))
                .collect(Collectors.toList());
        page.setList(dtoList);
        return page;
    }

    /**
     * 单笔查询
     */
    @GetMapping("/findChannel")
    public SmsMsgChannelDto findChannel(@RequestParam("id") Long id) {
        log.info("find channel {}", id);
        MsgChannel channel = msgChannelService.findById(id);
        SmsMsgChannelDto dto = new SmsMsgChannelDto().convertFor(channel);
        return dto;
    }

    /**
     * 变更通道
     */
    @PostMapping("/updateChannel")
    public SmsMsgRsp updateChannel(@Validated @RequestBody SmsMsgChannelDto channelDto) {
        log.info("update channel {}", channelDto);
        MsgChannel channel = channelDto.convertToSmsMsgChannel(channelDto);
        msgChannelService.updateByPrimaryKey(channel);
        return new SmsMsgRsp().setCode("00").setMsg("变更成功");
    }


    /**
     * 新增通道
     */
    @PostMapping("/addChannel")
    public SmsMsgRsp addChannel(@Validated @RequestBody SmsMsgChannelDto channelDto) {
        log.info("add channel {}", channelDto);
        MsgChannel channel = channelDto.convertToSmsMsgChannel(channelDto);
        List<SmsMsgChannelConfigDto> configDtoList = channelDto.getConfigList();

        List<MsgChannelConfig> configList = new ArrayList();
        if (!CollectionUtils.isEmpty(configDtoList)) {
            configList = configDtoList.stream()
                    .map(dto -> dto.convertToSmsMsgChannelConfig(dto))
                    .collect(Collectors.toList());
        }
        msgChannelService.save(channel, configList);
        return new SmsMsgRsp().setCode("00").setMsg("新增通道成功");
    }

    /**
     * 新增通道配置
     */
    @PostMapping("/addChannelConfig")
    public SmsMsgRsp addChannelConfig(@Validated @RequestBody SmsMsgChannelConfigDto channelConfigDto) {
        log.info("add channel config {}", channelConfigDto);
        MsgChannelConfig channelConfig = channelConfigDto.convertToSmsMsgChannelConfig(channelConfigDto);
        msgChannelConfigService.save(channelConfig);
        return new SmsMsgRsp().setCode("00").setMsg("新增通道配置成功");
    }

    /**
     * 变更通道配置
     */
    @PostMapping("/updateChannelConfig")
    public SmsMsgRsp updateChannelConfig(@Validated @RequestBody SmsMsgChannelConfigDto channelConfigDto) {
        log.info("update channel config {}", channelConfigDto);
        MsgChannelConfig channelConfig = channelConfigDto.convertToSmsMsgChannelConfig(channelConfigDto);

        MsgChannelConfig uniqueConfig = msgChannelConfigService.findUnique(channelConfig);
        if(uniqueConfig != null){
            return new SmsMsgRsp().setCode("99").setMsg("配置重复" + channelConfigDto);
        }
        msgChannelConfigService.updateByPrimaryKey(channelConfig);
        return new SmsMsgRsp().setCode("00").setMsg("变更通道配置成功");
    }

    @PostMapping("/refreshChannel")
    public SmsMsgRsp refreshChannel(@RequestParam("code") String code) {
        log.info("refresh channel {}", code);
        MsgChannel channel = msgChannelService.findByCode(code);
        if ("DING_DING".equals(channel.getType())) {
            boolean isLoaded = dingDingBiz.loadAccessToken(channel.getCode());
            if (isLoaded) {
                return new SmsMsgRsp().setCode("00").setMsg("通道刷新成功");
            }
        }
        return new SmsMsgRsp().setCode("99").setMsg("通道刷新失败");
    }

    /**
     * 分页查询通道列表
     */
    @PostMapping("/pageChannelConfig")
    public PageInfo<SmsMsgChannelConfigDto> pageChannelConfig(@RequestParam Map<String, Object> params) {
        log.info("page channel config {}", params);
        Integer pageNum = Integer.valueOf(params.getOrDefault("currentPage", 1).toString());
        PageHelper.startPage(pageNum, 10);
        List<MsgChannelConfig> configs = msgChannelConfigService.findByParams(params);
        PageInfo page = new PageInfo(configs);
        List<SmsMsgChannelConfigDto> dtoList = configs.stream().map(e -> new SmsMsgChannelConfigDto().convertFor(e))
                .collect(Collectors.toList());
        page.setList(dtoList);
        return page;
    }

    /**
     * 单笔查询
     */
    @GetMapping("/findChannelConfig")
    public SmsMsgChannelConfigDto findChannelConfig(@RequestParam("id") Long id) {
        log.info("find channel config {}", id);
        MsgChannelConfig config = msgChannelConfigService.findById(id);
        SmsMsgChannelConfigDto dto = new SmsMsgChannelConfigDto().convertFor(config);
        return dto;
    }

}
