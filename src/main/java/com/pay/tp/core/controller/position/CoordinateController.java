package com.pay.tp.core.controller.position;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.pay.tp.core.beans.position.CoordinateBean;
import com.pay.tp.core.beans.position.CoordinateParam;
import com.pay.tp.core.beans.position.RegeoResponse;
import com.pay.tp.core.entity.position.Coordinate;
import com.pay.tp.core.remote.position.GaodeClient;


/**经纬度定位Api
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.controller
 * @Description: TODO
 * @date Date : 2018年12月24日 15:45
 */
//@RestController
//@RequestMapping("coordinate")
public class CoordinateController {
    private final Logger logger = LoggerFactory.getLogger(CoordinateController.class);

    @Autowired
    private GaodeClient gaodeClient;

    //经纬度定位
    @RequestMapping(value = "", method = RequestMethod.POST)
    public CoordinateBean coordinate(@Validated @RequestBody CoordinateParam param) throws Exception {
        logger.info("method = cell, param = {}", param);
        Map<String,Object> map = new HashMap<>();
        //要求幂等
        //Coordinate o = coordinateService.query(param);
		Coordinate o = new Coordinate(param.getRequestNo(), "", param.getLongitude(), param.getLatitude());
		RegeoResponse resp = gaodeClient.req(o.getRequestNo(), o.getLongitude(), o.getLatitude());
		BeanUtils.copyProperties(resp, o);

		if (StringUtils.isEmpty(resp.getFormattedAddress())) {
			// throw new NoValidDataException("该经纬度无法定位");
		}
		// coordinateService.update(o);
		return o.getResult();
    }
    
    
}
