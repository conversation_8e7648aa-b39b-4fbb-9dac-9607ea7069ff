package com.pay.tp.core.controller.sms;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.pay.tp.core.beans.sms.JiguangParam;
import com.pay.tp.core.remote.sms.JiguangClient;
import com.pay.tp.core.service.sms.SmsMovementService;


/**
 * <AUTHOR> zhang<PERSON>an
 * @Package com.pay.sms.core.controller
 * @Description: TODO
 * @date Date : 2018年12月24日 10:43
 */
@RestController
public class JiguangController {

    private final Logger logger = LoggerFactory.getLogger(JiguangController.class);
    @Autowired
    private JiguangClient jiguangClient;
    @Autowired
    private SmsMovementService smsMovementService;

    /**
     * @param param
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月19日 下午11:15:20
    @ApiOperation(value = "极光发送API", notes = "根据传递来的参数构建JPushClient，请求的参数appName和requestNo不能为空，将参数最终放进pushPayload中，执行sendPush发送通知")
    @ApiResponses(@ApiResponse(message = "发送成功", code = 200))
     */
    @RequestMapping(value = "/jiguang", method = RequestMethod.POST)
    ResponseEntity<String> send(@Valid @RequestBody JiguangParam param) throws Exception {
//        logger.info("method = send, param = {}", param);
        //要求幂等
        //SmsMovement smsMovement = smsMovementService.query(param);
        //if (StringUtils.isEmpty(smsMovement.getResult())) {
        String result = jiguangClient.request(param.getAppName(), param.getRequestNo(), param.getAlert(), param.getContent(), param.getExtras(), param.getTagsAnd(),
                param.getTags(), param.getDeviceNumbers(), param.getAliases(), param.getPlatforms(), param.getMutableContent());
        //    smsMovement.setResult(result);
        //    smsMovementService.update(smsMovement);
        //}

        return ResponseEntity.ok("");
    }
}