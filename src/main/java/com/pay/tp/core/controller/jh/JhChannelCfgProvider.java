package com.pay.tp.core.controller.jh;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.enums.cust.QrReportType;
import com.pay.tp.core.entity.jh.JhChannelCfg;
import com.pay.tp.core.service.jh.JhCHannelCfgService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/jhChannelCfg")
@Slf4j
public class JhChannelCfgProvider {
	
	@Autowired
	private JhCHannelCfgService jhCHannelCfgService;
	/**
	* 新增聚合渠道配置
	* <AUTHOR>
	* @date 2022/4/21 14:55
	 * @param jhChannelCfg
	 * @return com.pay.frame.common.base.bean.ResultsBean<java.lang.String>
	*/
	@RequestMapping("/add")
	public ResultsBean<String> add(@RequestBody JhChannelCfg jhChannelCfg) {
		jhCHannelCfgService.insert(jhChannelCfg);
		return ResultsBean.SUCCESS();
	}

	/**
	* 更新聚合渠道配置
	* <AUTHOR>
	* @date 2022/4/21 14:55
	 * @param jhChannelCfg
	 * @return com.pay.frame.common.base.bean.ResultsBean<java.lang.String>
	*/
	@RequestMapping("/update")
	public ResultsBean<String> update(@RequestBody JhChannelCfg jhChannelCfg) {
		jhCHannelCfgService.update(jhChannelCfg);
		return ResultsBean.SUCCESS();
	}

	/**
	* 分页查询聚合渠道配置
	* <AUTHOR>
	* @date 2022/4/21 14:56
	 * @param param
	 * @return com.pay.frame.common.base.bean.ResultsBean<com.github.pagehelper.PageInfo<com.pay.manage.core.entity.JhChannelCfg>>
	*/
	@RequestMapping("/findPage")
	public ResultsBean<PageInfo<JhChannelCfg>> findPage(@RequestBody Map<String, Object> param) {
		PageInfo<JhChannelCfg> page = jhCHannelCfgService.findPage(param);
		return ResultsBean.SUCCESS(page);
	}

	@RequestMapping("/findById")
	public ResultsBean<JhChannelCfg> findById(@RequestParam("id") Long id) {
		JhChannelCfg jhChannelCfg = jhCHannelCfgService.findById(id);
		return ResultsBean.SUCCESS(jhChannelCfg);
	}
	
	@RequestMapping("/findOneEffByChannelType")
	public ResultsBean<JhChannelCfg> findOneEff(@RequestParam("channelType")String channelType) {
    	List<JhChannelCfg> list = jhCHannelCfgService.findByChannel(channelType);
    	if(CollectionUtils.isNotEmpty(list)) {
			return ResultsBean.SUCCESS(list.get(0));
//			return ResultsBean.SUCCESS(list.get(new Random().nextInt(list.size())));
		}
		return ResultsBean.SUCCESS();
	}
	@RequestMapping("/findByAppIdAndChannelNo")
	public ResultsBean<JhChannelCfg> findByAppIdAndChannelNo(@RequestParam(value="appId",required=false)String appId
			,@RequestParam("channelNo")String channelNo
			) {
		JhChannelCfg page = jhCHannelCfgService.findByAppIdAndChannelNo(appId,channelNo);
		return ResultsBean.SUCCESS(page);
	}
}
