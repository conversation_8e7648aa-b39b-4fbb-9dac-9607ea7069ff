package com.pay.tp.core.controller.msgmanage;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.tp.core.beans.sms.SmsMsgRecordDto;
import com.pay.tp.core.entity.msgmanage.MsgRecord;
import com.pay.tp.core.service.msgmanage.MsgRecordService;

import lombok.extern.slf4j.Slf4j;


/**
 * 通讯记录
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/msg/manage/record")
public class MsgManageRecordControl {

    @Autowired
    private MsgRecordService msgRecordService;

    /**
     * 分页查询列表
     */
    @PostMapping("/pageRecord")
    public PageInfo<SmsMsgRecordDto> pageRecord(@RequestParam Map<String, Object> params) {
        log.info("page template {}", params);
        Integer pageNum = Integer.valueOf(params.getOrDefault("currentPage", 1).toString());
        PageHelper.startPage(pageNum, 10);
        List<MsgRecord> records = msgRecordService.findByParams(params);
        PageInfo page = new PageInfo(records);
        List<SmsMsgRecordDto> dtoList = records.stream().map(e -> new SmsMsgRecordDto().convertFor(e))
                .collect(Collectors.toList());
        page.setList(dtoList);
        return page;
    }

    /**
     * 单笔查询
     */
    @GetMapping("/findRecord")
    public SmsMsgRecordDto findRecord(@RequestParam("id") Long id) {
        log.info("find template {}", id);
        MsgRecord record = msgRecordService.findById(id);
        SmsMsgRecordDto dto = new SmsMsgRecordDto().convertFor(record);
        return dto;
    }
}
