# 号码检测功能说明

## 概述

号码检测功能提供空号检测、实名检测、风控检测等服务，支持单次检测和批量检测，具备缓存机制和完整的记录追踪功能。

## 功能特性

### 1. 检测类型
- **空号检测**: 检测手机号是否为空号或停机号码
- **实名检测**: 检测手机号的实名认证状态
- **风控检测**: 检测手机号的风险等级

### 2. 核心特性
- ✅ **智能缓存**: 24小时内相同号码检测结果自动缓存
- ✅ **批量处理**: 支持大批量号码检测，自动分批处理
- ✅ **幂等性**: 基于请求号保证接口幂等性
- ✅ **异常降级**: API异常时自动降级到模拟检测
- ✅ **完整记录**: 所有检测结果完整记录到数据库
- ✅ **统计分析**: 提供丰富的统计查询功能

## 文件结构

```
src/main/java/com/pay/tp/core/
├── beans/phonecheck/
│   ├── PhoneCheckReq.java                      # 检测请求Bean
│   └── PhoneCheckResp.java                     # 检测响应Bean
├── entity/phonecheck/
│   └── PhoneCheckRecord.java                   # 检测记录实体类
├── mapper/phonecheck/
│   └── PhoneCheckRecordMapper.java             # 数据访问接口
├── service/phonecheck/
│   └── PhoneCheckRecordService.java            # 数据服务类
├── remote/phonecheck/
│   └── PhoneCheckClient.java                   # 第三方API客户端
├── biz/impl/
│   └── PhoneCheckBiz.java                      # 业务逻辑类
└── controller/phonecheck/
    ├── PhoneCheckController.java               # 控制器
    ├── phone_check_record.sql                  # 数据库脚本
    └── 号码检测功能说明.md                      # 说明文档

src/main/resources/mybatis/
└── PhoneCheckRecordMapper.xml                  # MyBatis映射文件

src/test/java/com/pay/tp/core/controller/phonecheck/
└── PhoneCheckControllerTest.java               # 测试类
```

## API接口

### 1. 空号检测
```http
POST /api/phonecheck/empty
Content-Type: application/json

{
    "requestNo": "REQ_20250104_001",
    "phoneNumbers": ["13800138000", "13800138001"],
    "merchantNo": "MERCHANT_001",
    "brand": "PLUS",
    "extParams": "{\"source\":\"web\"}"
}
```

### 2. 实名检测
```http
POST /api/phonecheck/realname
Content-Type: application/json

{
    "requestNo": "REQ_20250104_002",
    "phoneNumbers": ["13800138000", "13800138001"],
    "merchantNo": "MERCHANT_001",
    "brand": "PLUS"
}
```

### 3. 风控检测
```http
POST /api/phonecheck/risk
Content-Type: application/json

{
    "requestNo": "REQ_20250104_003",
    "phoneNumbers": ["13800138000", "13800138001"],
    "merchantNo": "MERCHANT_001",
    "brand": "PLUS"
}
```

### 4. 批量检测
```http
POST /api/phonecheck/batch
Content-Type: application/json

{
    "requestNo": "REQ_20250104_004",
    "checkType": "EMPTY_NUMBER",
    "phoneNumbers": ["13800138000", "13800138001", ...],
    "merchantNo": "MERCHANT_001",
    "brand": "PLUS"
}
```

### 5. 查询检测结果
```http
GET /api/phonecheck/result/{requestNo}
```

### 6. 查询手机号历史
```http
GET /api/phonecheck/history/{phoneNumber}?checkType=EMPTY_NUMBER&limit=10
```

### 7. 分页查询记录
```http
GET /api/phonecheck/records?pageNum=1&pageSize=20&checkType=EMPTY_NUMBER
```

### 8. 统计查询
```http
GET /api/phonecheck/statistics/{merchantNo}?checkType=EMPTY_NUMBER
```

## 响应格式

### 成功响应
```json
{
    "success": true,
    "code": "0",
    "msg": "成功",
    "data": {
        "requestNo": "REQ_20250104_001",
        "code": "0",
        "message": "检测完成",
        "totalCount": 2,
        "successCount": 2,
        "failCount": 0,
        "costTime": 1250,
        "results": [
            {
                "phoneNumber": "13800138000",
                "status": 0,
                "statusDesc": "空号",
                "carrier": "CMCC",
                "province": "北京",
                "city": "北京",
                "numberType": 1,
                "checkTime": "2025-01-04 10:30:00"
            },
            {
                "phoneNumber": "13800138001",
                "status": 1,
                "statusDesc": "正常",
                "carrier": "CMCC",
                "province": "北京",
                "city": "北京",
                "numberType": 1,
                "checkTime": "2025-01-04 10:30:00"
            }
        ]
    }
}
```

### 失败响应
```json
{
    "success": false,
    "code": "-1",
    "msg": "请求参数错误",
    "data": null
}
```

## 状态码说明

### 检测状态 (status)
- `1`: 正常号码
- `0`: 空号/停机
- `-1`: 检测失败

### 实名状态 (realNameStatus)
- `1`: 已实名
- `0`: 未实名
- `-1`: 无法确定

### 风险等级 (riskLevel)
- `LOW`: 低风险
- `MEDIUM`: 中风险
- `HIGH`: 高风险

### 运营商 (carrier)
- `CMCC`: 中国移动
- `CUCC`: 中国联通
- `CTCC`: 中国电信

## 配置说明

### application.yml 配置
```yaml
# 号码检测配置
phonecheck:
  api:
    baseUrl: https://api.phonecheck.com    # 第三方API地址
    appKey: your-app-key                   # 应用Key
    appSecret: your-app-secret             # 应用密钥
    timeout: 30000                         # 超时时间(毫秒)
    emptyNumberUrl: /api/phone/empty       # 空号检测接口
    realNameUrl: /api/phone/realname       # 实名检测接口
    riskControlUrl: /api/phone/risk        # 风控检测接口
  cache:
    validHours: 24                         # 缓存有效期(小时)
  batch:
    maxSize: 100                           # 单次最大检测数量
```

## 数据库配置

### 表结构
执行 `phone_check_record.sql` 脚本创建数据库表和索引。

### 数据清理
系统提供自动数据清理功能，默认保留90天数据：
```sql
-- 手动清理
EXEC UBADMA.CLEAN_PHONE_CHECK_RECORDS(90);

-- 启用定时清理任务
-- 参考SQL脚本中的定时任务配置
```

## 使用示例

### Java代码示例
```java
@Autowired
private PhoneCheckController phoneCheckController;

// 空号检测
PhoneCheckReq request = new PhoneCheckReq();
request.setRequestNo("REQ_" + System.currentTimeMillis());
request.setPhoneNumbers(Arrays.asList("13800138000", "13800138001"));
request.setMerchantNo("MERCHANT_001");
request.setBrand("PLUS");

ResultsBean<PhoneCheckResp> result = phoneCheckController.checkEmptyNumber(request);
if (result.isSuccess()) {
    PhoneCheckResp response = result.getData();
    for (PhoneCheckResp.PhoneCheckResult checkResult : response.getResults()) {
        System.out.println("手机号: " + checkResult.getPhoneNumber() + 
                          ", 状态: " + checkResult.getStatusDesc());
    }
}
```

### curl示例
```bash
# 空号检测
curl -X POST http://localhost:8080/api/phonecheck/empty \
  -H "Content-Type: application/json" \
  -d '{
    "requestNo": "REQ_20250104_001",
    "phoneNumbers": ["13800138000", "13800138001"],
    "merchantNo": "MERCHANT_001",
    "brand": "PLUS"
  }'

# 查询结果
curl http://localhost:8080/api/phonecheck/result/REQ_20250104_001
```

## 性能优化

### 1. 缓存策略
- 24小时内相同号码检测结果自动缓存
- 减少重复API调用，提高响应速度

### 2. 批量处理
- 支持大批量号码检测
- 自动分批处理，避免单次请求过大

### 3. 数据库优化
- 合理的索引设计
- 分区表支持（可选）
- 定时数据清理

### 4. 异常处理
- API异常时自动降级到模拟检测
- 完整的错误信息记录

## 监控指标

建议监控以下指标：
- API调用成功率
- 平均响应时间
- 缓存命中率
- 检测结果分布
- 异常错误统计

## 注意事项

1. **请求号唯一性**: 确保请求号在系统中唯一，用于幂等性控制
2. **批量大小限制**: 单次检测建议不超过100个号码
3. **缓存有效期**: 根据业务需求调整缓存有效期
4. **数据清理**: 定期清理历史数据，避免数据库膨胀
5. **API配置**: 正确配置第三方API参数，确保服务可用性

## 扩展功能

### 1. 新增检测类型
在 `PhoneCheckClient` 中添加新的检测方法，并在 `PhoneCheckBiz` 中调用。

### 2. 自定义缓存策略
修改 `PhoneCheckBiz` 中的缓存逻辑，支持不同检测类型的不同缓存策略。

### 3. 多渠道支持
扩展 `PhoneCheckClient`，支持多个第三方API渠道，实现负载均衡和容错。

### 4. 实时通知
集成消息队列，实现检测结果的实时通知功能。
