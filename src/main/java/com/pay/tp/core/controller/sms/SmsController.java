package com.pay.tp.core.controller.sms;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.sms.SmsParam;
import com.pay.tp.core.biz.SmsChannelBiz;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR> z<PERSON><PERSON>an
 */
@RestController
public class SmsController {
    private final Logger logger = LoggerFactory.getLogger(SmsController.class);

    @Autowired
 	private SmsChannelBiz smsChannelBiz;
    

    /**
     * @param param
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月19日 下午11:15:20
            notes = "1、先根据请求号查找是否有这条短信记录\n" +
                    "（a）如果存在直接返回这条记录\n" +
                    "（b）如果不存在，则新增这条短信记录，并返回\n" +
                    "2、根据返回的短信记录，判断他的返回结果是否为空\n" +
                    "（a）如果不为空，直接解析这条记录返回响应结果\n" +
                    "（b）如果为空，再查询可用的短信通道；如果无可用通道，直接返回code=-1, msg=无可用短信通道；如果有可用通道，异步发送短信，返回code=0；并将返回的结果更新到这条短信记录中")
   code = 0，发送成功; code = -1, 发送失败; msg在失败时候为原因说明
    */
    @RequestMapping(value = "/sms", method = RequestMethod.POST)
    Map<String, Object> send(@Valid @RequestBody SmsParam param) {
//        logger.info("method = auth, param = {}", param);
        return smsChannelBiz.send(param);
    }
    
    
    
    /**
     * 切换通道
	 * @return
     */
	@RequestMapping(value = "/updateChannelStatus", method = RequestMethod.POST)
	public ResultsBean<String> updateChannelStatus(@RequestParam("id")String id) {
		smsChannelBiz.updateStatus(id);
		return ResultsBean.SUCCESS();
	}
    
    
}
