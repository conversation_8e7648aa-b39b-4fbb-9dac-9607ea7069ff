package com.pay.tp.core.biz.impl;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.phonecheck.PhoneCheckReq;
import com.pay.tp.core.beans.phonecheck.PhoneCheckResp;
import com.pay.tp.core.entity.phonecheck.PhoneCheckRecord;
import com.pay.tp.core.remote.phonecheck.PhoneCheckClient;
import com.pay.tp.core.service.phonecheck.PhoneCheckRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 号码检测业务逻辑类
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Component
public class PhoneCheckBiz {

    @Autowired
    private PhoneCheckRecordService phoneCheckRecordService;

    @Autowired
    private PhoneCheckClient phoneCheckClient;

    @Value("${phonecheck.cache.validHours:24}")
    private int cacheValidHours;

    @Value("${phonecheck.batch.maxSize:100}")
    private int maxBatchSize;

    /**
     * 号码检测
     * @param request 检测请求
     * @return 检测结果
     */
    @Transactional
    public ResultsBean<PhoneCheckResp> checkPhoneNumbers(PhoneCheckReq request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始号码检测，请求号: {}, 检测类型: {}, 手机号数量: {}",
                    request.getRequestNo(), request.getCheckType(), request.getPhoneNumbers().size());

            // 参数校验
            ResultsBean<String> validateResult = validateRequest(request);
            if (!validateResult.success()) {
                return ResultsBean.FAIL(validateResult.getMessage());
            }

            // 检查批量大小限制
            if (request.getPhoneNumbers().size() > maxBatchSize) {
                return ResultsBean.FAIL("单次检测手机号数量不能超过" + maxBatchSize + "个");
            }

            // 去重和格式化手机号
            List<String> uniquePhoneNumbers = deduplicateAndFormat(request.getPhoneNumbers());
            log.info("去重后手机号数量: {}", uniquePhoneNumbers.size());

            // 检查缓存，获取需要检测的手机号
            List<String> needCheckPhones = new ArrayList<>();
            List<PhoneCheckResp.PhoneCheckResult> cachedResults = new ArrayList<>();

            for (String phoneNumber : uniquePhoneNumbers) {
                PhoneCheckRecord cachedRecord = phoneCheckRecordService.getValidRecord(
                        phoneNumber, request.getCheckType(), cacheValidHours);

                if (cachedRecord != null) {
                    // 使用缓存结果
                    PhoneCheckResp.PhoneCheckResult cachedResult = convertToResult(cachedRecord);
                    cachedResults.add(cachedResult);
                    log.debug("使用缓存结果，手机号: {}", phoneNumber);
                } else {
                    // 需要重新检测
                    needCheckPhones.add(phoneNumber);
                }
            }

            log.info("缓存命中数量: {}, 需要检测数量: {}", cachedResults.size(), needCheckPhones.size());

            // 调用第三方API检测
            List<PhoneCheckResp.PhoneCheckResult> apiResults = new ArrayList<>();
            if (!needCheckPhones.isEmpty()) {
                apiResults = callThirdPartyApi(needCheckPhones, request.getCheckType());

                // 保存检测结果到数据库
                saveCheckResults(request, apiResults);
            }

            // 合并结果
            List<PhoneCheckResp.PhoneCheckResult> allResults = new ArrayList<>();
            allResults.addAll(cachedResults);
            allResults.addAll(apiResults);

            // 构建响应
            PhoneCheckResp response = buildResponse(request, allResults, startTime);

            log.info("号码检测完成，请求号: {}, 总数量: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    request.getRequestNo(), response.getTotalCount(), response.getSuccessCount(),
                    response.getFailCount(), response.getCostTime());

            return ResultsBean.SUCCESS(response);

        } catch (Exception e) {
            log.error("号码检测异常，请求号: {}", request.getRequestNo(), e);
            return ResultsBean.FAIL("号码检测异常: " + e.getMessage());
        }
    }

    /**
     * 批量号码检测（支持大批量）
     * @param request 检测请求
     * @return 检测结果
     */
    @Transactional
    public ResultsBean<PhoneCheckResp> batchCheckPhoneNumbers(PhoneCheckReq request) {
        try {
            log.info("开始批量号码检测，请求号: {}, 检测类型: {}, 手机号数量: {}",
                    request.getRequestNo(), request.getCheckType(), request.getPhoneNumbers().size());

            // 参数校验
            ResultsBean<String> validateResult = validateRequest(request);
            if (!validateResult.success()) {
                return ResultsBean.FAIL(validateResult.getMessage());
            }

            // 去重和格式化手机号
            List<String> uniquePhoneNumbers = deduplicateAndFormat(request.getPhoneNumbers());

            // 分批处理
            List<PhoneCheckResp.PhoneCheckResult> allResults = new ArrayList<>();
            int totalCount = uniquePhoneNumbers.size();
            int processedCount = 0;

            for (int i = 0; i < totalCount; i += maxBatchSize) {
                int endIndex = Math.min(i + maxBatchSize, totalCount);
                List<String> batchPhones = uniquePhoneNumbers.subList(i, endIndex);

                // 创建批次请求
                PhoneCheckReq batchRequest = new PhoneCheckReq();
                batchRequest.setRequestNo(request.getRequestNo() + "_BATCH_" + (i / maxBatchSize + 1));
                batchRequest.setCheckType(request.getCheckType());
                batchRequest.setPhoneNumbers(batchPhones);
                batchRequest.setMerchantNo(request.getMerchantNo());
                batchRequest.setBrand(request.getBrand());
                batchRequest.setExtParams(request.getExtParams());

                // 处理批次
                ResultsBean<PhoneCheckResp> batchResult = checkPhoneNumbers(batchRequest);
                if (batchResult.success() && batchResult.getObject() != null) {
                    allResults.addAll(batchResult.getObject().getResults());
                }

                processedCount += batchPhones.size();
                log.info("批量检测进度: {}/{}", processedCount, totalCount);
            }

            // 构建最终响应
            PhoneCheckResp response = new PhoneCheckResp();
            response.setRequestNo(request.getRequestNo());
            response.setCode(PhoneCheckResp.ResponseCode.SUCCESS);
            response.setMessage("批量检测完成");
            response.setResults(allResults);
            response.setTotalCount(allResults.size());

            int successCount = 0;
            int failCount = 0;
            for (PhoneCheckResp.PhoneCheckResult result : allResults) {
                if (PhoneCheckResp.PhoneStatus.FAIL.equals(result.getStatus())) {
                    failCount++;
                } else {
                    successCount++;
                }
            }
            response.setSuccessCount(successCount);
            response.setFailCount(failCount);

            log.info("批量号码检测完成，请求号: {}, 总数量: {}, 成功: {}, 失败: {}",
                    request.getRequestNo(), response.getTotalCount(), response.getSuccessCount(), response.getFailCount());

            return ResultsBean.SUCCESS(response);

        } catch (Exception e) {
            log.error("批量号码检测异常，请求号: {}", request.getRequestNo(), e);
            return ResultsBean.FAIL("批量号码检测异常: " + e.getMessage());
        }
    }

    /**
     * 校验请求参数
     * @param request 检测请求
     * @return 校验结果
     */
    private ResultsBean<String> validateRequest(PhoneCheckReq request) {
        if (request == null) {
            return ResultsBean.FAIL("请求参数不能为空");
        }

        if (StringUtils.isBlank(request.getRequestNo())) {
            return ResultsBean.FAIL("请求号不能为空");
        }

        if (StringUtils.isBlank(request.getCheckType())) {
            return ResultsBean.FAIL("检测类型不能为空");
        }

        if (!PhoneCheckReq.CheckType.EMPTY_NUMBER.equals(request.getCheckType()) &&
            !PhoneCheckReq.CheckType.REAL_NAME.equals(request.getCheckType()) &&
            !PhoneCheckReq.CheckType.RISK_CONTROL.equals(request.getCheckType())) {
            return ResultsBean.FAIL("不支持的检测类型: " + request.getCheckType());
        }

        if (request.getPhoneNumbers() == null || request.getPhoneNumbers().isEmpty()) {
            return ResultsBean.FAIL("手机号列表不能为空");
        }

        return ResultsBean.SUCCESS("校验通过");
    }

    /**
     * 去重和格式化手机号
     * @param phoneNumbers 原始手机号列表
     * @return 去重后的手机号列表
     */
    private List<String> deduplicateAndFormat(List<String> phoneNumbers) {
        List<String> result = new ArrayList<>();
        for (String phoneNumber : phoneNumbers) {
            if (StringUtils.isNotBlank(phoneNumber)) {
                String formatted = phoneNumber.trim().replaceAll("\\s+", "");
                if (!result.contains(formatted)) {
                    result.add(formatted);
                }
            }
        }
        return result;
    }

    /**
     * 调用第三方API
     * @param phoneNumbers 手机号列表
     * @param checkType 检测类型
     * @return 检测结果
     */
    private List<PhoneCheckResp.PhoneCheckResult> callThirdPartyApi(List<String> phoneNumbers, String checkType) {
        switch (checkType) {
            case PhoneCheckReq.CheckType.EMPTY_NUMBER:
                return phoneCheckClient.checkEmptyNumber(phoneNumbers);
            case PhoneCheckReq.CheckType.REAL_NAME:
                return phoneCheckClient.checkRealName(phoneNumbers);
            case PhoneCheckReq.CheckType.RISK_CONTROL:
                return phoneCheckClient.checkRiskControl(phoneNumbers);
            default:
                throw new IllegalArgumentException("不支持的检测类型: " + checkType);
        }
    }

    /**
     * 保存检测结果到数据库
     * @param request 原始请求
     * @param results 检测结果
     */
    private void saveCheckResults(PhoneCheckReq request, List<PhoneCheckResp.PhoneCheckResult> results) {
        List<PhoneCheckRecord> records = new ArrayList<>();
        Date now = new Date();

        for (PhoneCheckResp.PhoneCheckResult result : results) {
            PhoneCheckRecord record = new PhoneCheckRecord();
            record.setRequestNo(request.getRequestNo());
            record.setPhoneNumber(result.getPhoneNumber());
            record.setCheckType(request.getCheckType());
            record.setMerchantNo(request.getMerchantNo());
            record.setBrand(request.getBrand());
            record.setStatus(result.getStatus());
            record.setStatusDesc(result.getStatusDesc());
            record.setCarrier(result.getCarrier());
            record.setProvince(result.getProvince());
            record.setCity(result.getCity());
            record.setNumberType(result.getNumberType());
            record.setRiskLevel(result.getRiskLevel());
            record.setRealNameStatus(result.getRealNameStatus());
            record.setCheckChannel("API");
            record.setErrorMsg(result.getErrorMsg());
            record.setExtParams(request.getExtParams());
            record.setCreateTime(now);
            record.setUpdateTime(now);

            records.add(record);
        }

        if (!records.isEmpty()) {
            phoneCheckRecordService.batchInsert(records);
            log.info("保存检测结果到数据库，数量: {}", records.size());
        }
    }

    /**
     * 将数据库记录转换为响应结果
     * @param record 数据库记录
     * @return 响应结果
     */
    private PhoneCheckResp.PhoneCheckResult convertToResult(PhoneCheckRecord record) {
        PhoneCheckResp.PhoneCheckResult result = new PhoneCheckResp.PhoneCheckResult();
        result.setPhoneNumber(record.getPhoneNumber());
        result.setStatus(record.getStatus());
        result.setStatusDesc(record.getStatusDesc());
        result.setCarrier(record.getCarrier());
        result.setProvince(record.getProvince());
        result.setCity(record.getCity());
        result.setNumberType(record.getNumberType());
        result.setRiskLevel(record.getRiskLevel());
        result.setRealNameStatus(record.getRealNameStatus());
        result.setCheckTime(record.getCreateTime() != null ? record.getCreateTime().toString() : null);
        result.setErrorMsg(record.getErrorMsg());
        return result;
    }

    /**
     * 构建响应
     * @param request 原始请求
     * @param results 检测结果
     * @param startTime 开始时间
     * @return 响应对象
     */
    private PhoneCheckResp buildResponse(PhoneCheckReq request, List<PhoneCheckResp.PhoneCheckResult> results, long startTime) {
        PhoneCheckResp response = new PhoneCheckResp();
        response.setRequestNo(request.getRequestNo());
        response.setCode(PhoneCheckResp.ResponseCode.SUCCESS);
        response.setMessage("检测完成");
        response.setResults(results);
        response.setTotalCount(results.size());
        response.setCostTime(System.currentTimeMillis() - startTime);

        int successCount = 0;
        int failCount = 0;
        for (PhoneCheckResp.PhoneCheckResult result : results) {
            if (PhoneCheckResp.PhoneStatus.FAIL.equals(result.getStatus())) {
                failCount++;
            } else {
                successCount++;
            }
        }
        response.setSuccessCount(successCount);
        response.setFailCount(failCount);

        return response;
    }

    /**
     * 生成请求号
     * @return 请求号
     */
    public String generateRequestNo() {
        return "PC" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
