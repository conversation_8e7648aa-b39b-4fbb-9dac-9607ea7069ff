package com.pay.tp.core.biz.impl;

import com.pay.frame.common.base.util.JsonUtils;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.entity.msgmanage.MsgChannel;
import com.pay.tp.core.entity.msgmanage.MsgRecord;
import com.pay.tp.core.entity.msgmanage.MsgTemplate;
import com.pay.tp.core.entity.msgmanage.MsgUser;
import com.pay.tp.core.enums.ChannelType;
import com.pay.tp.core.remote.sms.WorkWechatMsgClient;
import com.pay.tp.core.service.msgmanage.*;
import com.pay.tp.core.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @package com.pay.tp.core.biz.impl
 * @dscription
 * @date 2024-03-20 17:33
 */
@Slf4j
@Component
public class WorkWechatBiz {


	@Autowired
	private MsgRecordService msgRecordService;

	@Autowired
	private MsgTemplateService msgTemplateService;

	@Autowired
	private MsgChannelService msgChannelService;

	@Autowired
	private MsgUserService msgUserService;

	@Autowired
	private MsgChannelConfigService msgChannelConfigService;

	@Autowired
	private WorkWechatMsgClient workWechatMsgClient;

	/**
	 * 指定模板发送自定义机器人消息
	 */
	public boolean sendRobotMsgTemplateCode(String requestNo,
											 String msgType,
											 String templateCode,
											 String templateParam) {

		// 幂等校验
		if (checkIdempotent(requestNo)) {
			return true;
		}
		MsgTemplate template = msgTemplateService.findByTemplateCode(templateCode);
		if(template == null){
			log.error("发送企业微信自定义机器人消息失败, 未查询到模板信息, templateCode：{}。", templateCode);
			return false;
		}
		MsgChannel channel = msgChannelService.findByCode(template.getChannelCode());
		if(channel == null){
			log.error("发送企业微信自定义机器人消息失败, 未查询到消息通道, templateCode：{}。", template.getChannelCode());
			return false;
		}
		List<MsgUser> userList = msgUserService.findByGroupNo(template.getChannelCode(), template.getUserGroupNo());

		if(ChannelType.WECHAT_ROBOT.name().equals(channel.getType())){
			return sendRobotMsg(userList, requestNo,msgType, template, channel, templateParam);
		}else{
			log.error("发送企业微信自定义机器人消息失败, 渠道类型错误 , channelCode: {}, channelType: {}。", channel.getCode(), channel.getType());
			return false;
		}
	}

	private boolean checkIdempotent(String requestNo) {
		List<MsgRecord> records = msgRecordService.findByRequestNo(requestNo);
		if (!CollectionUtils.isEmpty(records)) {
			log.info("work wechat msg record idempotent {}", requestNo);
			return true;
		}
		return false;
	}

	/**
	 * 发送自定义机器人群消息
	 *
	 * @param userList
	 * @param requestNo
	 * @param template
	 * @param channel
	 * @param templateParam
	 * @return
	 */
	private boolean sendRobotMsg(List<MsgUser> userList,
								 String requestNo,
								 String msgType,
								 MsgTemplate template,
								 MsgChannel channel,
								 String templateParam) {

		List<String> userPhoneList = userList.stream()
				.map(MsgUser::getPhoneNo)
				.collect(Collectors.toList());

		String msgContent = msgTemplateService.buildMsgContent(template, templateParam);

		Map<String, String> configMap = msgChannelConfigService.findFlatMapConfigByChannelCode(channel.getCode());
		String accessToken = configMap.get("access_token");

		boolean pass = extFilter(template, msgContent);

		Map<String, Object> result = new HashMap<>();
		if (pass) {
			result = workWechatMsgClient.robotSendMsg(accessToken, Optional.ofNullable(userPhoneList).orElse(Collections.singletonList("@all")), msgContent,
					msgType);
		} else {
			result.put("errcode", "-2");
			result.put("errmsg", "消息过滤器拦截");
		}

		String errcode = result.getOrDefault("errcode", -1).toString();
		String resultInfo = JsonUtil.toJson(result);

		msgRecordInsertOne(requestNo, template, templateParam, msgContent, errcode, resultInfo);

		return errcode.equals("0");
	}
	/**
	 * 扩展消息过滤功能
	 *
	 * @param msgTemplate
	 * @param msgContent
	 * @return
	 */
	public boolean extFilter(MsgTemplate msgTemplate, String msgContent) {
		try {
			String extInfo = msgTemplate.getExtInfo();
			if (StringUtils.isEmpty(extInfo)) {
				return true;
			}
			MsgTemplate.ExtInfo ext = JsonUtils.json2Bean(extInfo, MsgTemplate.ExtInfo.class);

			if (ext.getFilter() == null) {
				return true;
			}

			List<String> pass = ext.getFilter().getPass();
			if (!CollectionUtils.isEmpty(pass)) {
				return pass.stream().filter(p -> msgContent.contains(p)).count() > 0;
			}

			List<String> reject = ext.getFilter().getReject();
			if (!CollectionUtils.isEmpty(reject)) {
				return reject.stream().filter(r -> msgContent.contains(r)).count() == 0;
			}
		} catch (Exception e) {
			log.error("ext filter error {}, {}", msgTemplate, msgContent, e);
		}

		return true;
	}

	private void msgRecordInsertOne(String requestNo, MsgTemplate template, String templateParam, String msgContent, String errcode, String resultInfo) {
		MsgRecord record = msgRecordService.build(requestNo, template.getChannelCode(), template.getId(), msgContent,
				templateParam);
		record.setResultCode(errcode)
				.setResult(resultInfo);
		msgRecordService.insert(record);
	}
}
