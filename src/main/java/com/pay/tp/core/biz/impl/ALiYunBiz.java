package com.pay.tp.core.biz.impl;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.pay.frame.common.base.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.pay.frame.common.base.constants.FilePathConstants;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.tp.core.configuration.AliOss;
import com.pay.tp.core.service.AppFileLogService;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * @date 2022年08月18日 15:30
 */
@Slf4j
@Component
public class ALiYunBiz {

//    @Value("${proxy.squid.host:}")
//    private String proxyHost;
//    @Value("${proxy.squid.port:0}")
//    private int proxyPort;


//    @Value("${ali.yun.endpoint}")
//    private String endpoint;
//    @Value("${ali.yun.region}")
//    private String region;
//    @Value("${ali.yun.accessKeyId}")
//    private String accessKeyId;
//    @Value("${ali.yun.accessKeySecret}")
//    private String accessKeySecret;
//    @Value("${ali.yun.roleArn}")
//    private String roleArn;
//    @Value("${ali.yun.bucketName}")
//    private String bucketName;
//    @Value("${ali.yun.roleSessionName}")
//    private String roleSessionName;

//    @Value("${ali.yun.accessUrl:https://d2df7jcl45mdldys.oss-cn-beijing.aliyuncs.com/}")
//    private String accessUrl;


    @Autowired
    private AppFileLogService appFileLogService;
    @Autowired
    private AliOss aliOss;
    @Autowired
    private OSS ossClient;
    @Autowired
    private DefaultAcsClient defaultAcsClient;

    /**
     * 获取token
     *
     * @return
     * @throws ClientException
     * @throws ServerException
     */
    public Map<String, String> findToken(Map<String, String> params) throws Exception {
//        IClientProfile profile = DefaultProfile.getProfile(region, accessKeyId, accessKeySecret);
//        DefaultAcsClient client = new DefaultAcsClient(profile);
        final AssumeRoleRequest request = new AssumeRoleRequest();
        request.setRoleArn(aliOss.getRoleArn());
        request.setRoleSessionName(aliOss.getRoleSessionName());
        request.setPolicy(null); // 如果policy为空，则用户将获得该角色下所有权限。
        request.setDurationSeconds(3600L); // 设置临时访问凭证的有效时间为3600秒。
//        final AssumeRoleResponse response = client.getAcsResponse(request);
        final AssumeRoleResponse response = defaultAcsClient.getAcsResponse(request);
        com.aliyuncs.auth.sts.AssumeRoleResponse.Credentials credentials = response.getCredentials();
        Map<String, String> map = new HashMap<>();
        map.put("accessKeyId", credentials.getAccessKeyId());
        map.put("accessKeySecret", credentials.getAccessKeySecret());
        map.put("securityToken", credentials.getSecurityToken());
        map.put("bucketName", aliOss.getBucketName());
        map.put("fileName", RandomUtils.getUUID() + ".mp4");

        if (params != null && params.size() > 0 && ("APP_FILE_LOG").equals(params.get("type"))) {
            String fileName = RandomUtils.getUUID() + ".xlog";
            appFileLogService.insert(params, fileName);
            map.put("fileName", fileName);
        }
        return map;
    }


    /**
     * @param fileName
     * @param pathName
     * @throws Exception
     */
    public String downFile(String fileName, String pathName) {
        // 创建OSSClient实例。
//        ClientBuilderConfiguration cfg = new ClientBuilderConfiguration();
//        cfg.setProxyHost(proxyHost);
//        cfg.setProxyPort(proxyPort);
//        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, cfg);
        try {
            File file = new File(pathName);
            // 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
            // 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
            ossClient.getObject(new GetObjectRequest(aliOss.getBucketName(), fileName), file);
            String remotePath = FilePathConstants.REMOTE_CUSTOMER_PATH_PREFIX + FilePathConstants.MULTI_MEDIA_PATH;
            remotePath = FileUtil.getRemotePath(remotePath, aliOss.getRoleSessionName());
            String path = SftpClientUtils.upload(file.getAbsolutePath(), remotePath);

//            ossClient.deleteObject(aliOss.getBucketName(), fileName);
            return path;
        } catch (OSSException oe) {
            log.error(oe.getMessage(), oe);
            log.info("Error Message:{},{},{},{},{},{}", oe.getErrorMessage(), oe.getErrorCode(), oe.getRequestId(), oe.getHostId(), fileName, pathName);
            throw new ServerException(oe.getMessage());

        } catch (Exception e) {
            log.error("阿里云下载错误：{},{},{},{}", e.getMessage(), fileName, pathName, e);
            throw new ServerException(e.getMessage());

        }
    }


    public void deleteFile(String fileName) {
//        // 创建OSSClient实例。
//        ClientBuilderConfiguration cfg = new ClientBuilderConfiguration();
//        cfg.setProxyHost(proxyHost);
//        cfg.setProxyPort(proxyPort);
//        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, cfg);
        try {
            ossClient.deleteObject(aliOss.getBucketName(), fileName);
        } catch (OSSException oe) {
            log.error(oe.getMessage(), oe);
            log.info("Error Message:{},{},{},{},{},{}", oe.getErrorMessage(), oe.getErrorCode(), oe.getRequestId(),
                    oe.getHostId(), fileName);
            throw new ServerException(oe.getMessage());
        } catch (Exception e) {
            log.error("阿里云下载错误：{},{},{},{}", e.getMessage(), fileName, e);
            throw new ServerException(e.getMessage());
        }
    }

    @SneakyThrows
    public String getSignedUrl(String fileName, boolean snapshotYn) {
        log.info("aliyun oss fileName:{}", fileName);
        // 创建OSSClient实例。
//        ClientBuilderConfiguration cfg = new ClientBuilderConfiguration();
//        cfg.setProxyHost(proxyHost);
//        cfg.setProxyPort(proxyPort);
//        DefaultCredentialProvider defaultCredentialProvider = CredentialsProviderFactory.newDefaultCredentialProvider(accessKeyId, accessKeySecret);
//        OSS ossClient = new OSSClientBuilder().build(endpoint, defaultCredentialProvider, cfg);
////        ossClient.setBucketTransferAcceleration();

        // 指定生成的签名URL过期时间，单位为毫秒。本示例以设置过期时间为2小时
        Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000L * 2);
        // 生成签名URL。
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(aliOss.getBucketName(), fileName, HttpMethod.GET);
        // 设置过期时间。
        request.setExpiration(expiration);
        if (snapshotYn){
            // 使用精确时间模式截取视频6s处的内容，输出为JPG格式的图片，宽度为800，高度为600。
            String style = "video/snapshot,t_6000,f_jpg,w_800,h_600";
            request.setProcess(style);
        }
        // 通过HTTP GET请求生成签名URL。
        URL signedUrl = ossClient.generatePresignedUrl(request);
        log.info("aliyun oss signed url:{}", signedUrl);
        return signedUrl.toString();
    }

    @SneakyThrows
    public String uploadBase64File(String fileName, String base64) {
        log.info("ali oss uploadBase64File fileName:{} base64 size:{}", fileName, StringUtils.isBlank(base64) ? 0 : base64.length());
        // 解码Base64字符串
        byte[] decodedBytes = Base64Utils.decode(base64);

        // 创建ByteArrayInputStream
        ossClient.putObject(aliOss.getBucketName(), fileName, new ByteArrayInputStream(decodedBytes));
        String ossPath = aliOss.getAccessUrl() + fileName;
        log.info("aliyun oss uploadBase64File success fileName:{} ossPath:{}", fileName, ossPath);
        return ossPath;
    }
}
