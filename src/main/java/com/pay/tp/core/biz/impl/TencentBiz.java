package com.pay.tp.core.biz.impl;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.pay.frame.cache.util.RedisUtil;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.FilePathConstants;
import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.enums.OwnerRole;
import com.pay.frame.common.base.enums.Status;
import com.pay.frame.common.base.enums.YesNo;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.Base64DecodeMultipartFile;
import com.pay.frame.common.base.util.DateUtil;
import com.pay.frame.common.base.util.FileUtil;
import com.pay.frame.common.base.util.FmtDate;
import com.pay.frame.common.base.util.RandomUtils;
import com.pay.frame.common.base.util.RedisKeyUtil;
import com.pay.frame.common.base.util.SftpClientUtils;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.tencent.AccessTokenRes;
import com.pay.tp.core.beans.tencent.ApiAppWillFaceResultRes;
import com.pay.tp.core.beans.tencent.ApiTicketRes;
import com.pay.tp.core.beans.tencent.ApiTicketRes.Ticket;
import com.pay.tp.core.beans.tencent.AppWillBodyResultReq;
import com.pay.tp.core.beans.tencent.EidWillBodyResultReq;
import com.pay.tp.core.beans.tencent.WillBodyBean;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.enums.WillType;
import com.pay.tp.core.remote.CustomerQueryClient;
import com.pay.tp.core.remote.OriginChannelClient;
import com.pay.tp.core.remote.WillBodyClient;
import com.pay.tp.core.remote.bean.CustomerRsp;
import com.pay.tp.core.remote.bean.JlQuerySlResultReq;
import com.pay.tp.core.remote.bean.JlQuerySlResultRsp;
import com.pay.tp.core.service.WillBodyRecordService;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.GetEidResultRequest;
import com.tencentcloudapi.faceid.v20180301.models.GetEidResultResponse;
import com.tencentcloudapi.faceid.v20180301.models.GetEidTokenConfig;
import com.tencentcloudapi.faceid.v20180301.models.GetEidTokenRequest;
import com.tencentcloudapi.faceid.v20180301.models.GetEidTokenResponse;
import com.tencentcloudapi.faceid.v20180301.models.IntentionQuestion;
import com.tencentcloudapi.faceid.v20180301.models.IntentionQuestionResult;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 腾讯云 识别业务
 */
@Slf4j
@Component
public class TencentBiz {

    @Value("${common.register.qa:false}")
    private boolean registerQa;

    // PLUS
    @Value("${tencent.sl.appidQa:}")
    private String slAppidQa;
    @Value("${tencent.sl.secretQa:}")
    private String slSecretQa;

    @Value("${tencent.sl.appid:}")
    private String slAppid;
    @Value("${tencent.sl.secret:}")
    private String slSecret;


    // UK
    @Value("${tencent.sl.appidUkQa:}")
    private String slAppidUkQa;
    @Value("${tencent.sl.secretUkQa:}")
    private String slSecretUkQa;

    @Value("${tencent.sl.appidUk:}")
    private String slAppidUk;
    @Value("${tencent.sl.secretUk:}")
    private String slSecretUk;


    // 请求地址
    @Value("${tencent.sl.accessTokenUrl:}")
    private String accessTokenUrl;
    @Value("${tencent.sl.apiTicketUrl:}")
    private String apiTicketUrl;
    @Value("${tencent.sl.apiAppWillFaceResultUrl:}")
    private String apiAppWillFaceResultUrl;

    //
    @Value("${tencent.sl.eidAppid:}")
    private String eidAppid;
    @Value("${tencent.sl.eidSecret:}")
    private String eidSecret;
    @Value("${tencent.sl.eidUrl:}")
    private String eidUrl;

    @Value("${tencent.sl.eidMerchantId:}")
    private String eidMerchantId;
    @Value("${tencent.sl.eidMerchantIdUk:}")
    private String eidMerchantIdUk;

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;
    @Value("${ali.yun.accessUrl:https://d2df7jcl45mdldys.oss-cn-beijing.aliyuncs.com/}")
    private String accessUrl;

    private static Map<String, String> eidFinalCodeMap = new HashMap<String, String>() {
        { put("0", "认证通过");  put("-1", "认证未通过"); put("-2", "浏览器内核不兼容，无法进行意愿校验"); }
    };

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WillBodyRecordService willBodyRecordService;

    @Autowired
    private WillBodyClient willBodyClient;

    @Autowired
    private ALiYunBiz aLiYunBiz;
    @Autowired
    private DingDingBiz dingDingBiz;
    @Autowired
    private CustomerQueryClient customerQueryClient;
    @Autowired
    private OriginChannelClient originChannelClient;


    @Value("${spring.application.name}")
    private String appName;


    /**
     * 获取 ticket
     * @param param
     * @return
     */
    public Map<String, String> findTicket(@RequestBody Map<String,String> param){
        Map<String, String> map1 = getAppIdSecret(param.get("brand"));
        String appId=map1.get("appId"), secret=map1.get("secret");

        String type=param.getOrDefault("type", "SIGN");
        String userId=param.get("userId");
        String nonce = RandomUtils.getUUID();
        String ticket = getCacheTicket(appId, secret,type,userId);
        String version="1.0.0";
        List<String> signList = new ArrayList<>();
        signList.add(appId);
        signList.add(userId);
        signList.add(nonce);
        signList.add(version);
        String sign = sign(signList, ticket);
        Map<String, String> map = new HashMap<>();
        map.put("appId", appId);
        map.put("version", version);
        map.put("sign", sign);
        map.put("userId", userId);
        map.put("nonce", nonce);
        if("SIGN".equals(type)) {
            map.put("orderNo", nonce);
            // 意愿核身次数限制
            LimitCount(userId);
        }
        log.info("tencent findTicket return {} ", map);
        return map;
    }

    private Map<String, String> getAppIdSecret(String brand) {
    	Map<String, String> map = new HashMap<String, String>();
    	String appId="", secret="";
        if(Brand.PLUS.name().equals(brand)){
           appId = slAppidQa;
           secret = slSecretQa;
            if(!registerQa) {
                appId = slAppid;
                secret = slSecret;
            }
        }else if(Brand.UK.name().equals(brand)){
           appId = slAppidUkQa;
           secret = slSecretUkQa;
            if(!registerQa) {
                appId = slAppidUk;
                secret = slSecretUk;
            }
        }else{
            throw new ServerException("品牌类型错误");
        }
        map.put("appId", appId);
        map.put("secret", secret);
        return map;
    }
    /**
     *
     */
    private void LimitCount(String userId) {
        String key = DateUtil.formatDate() +"_"+ userId;
        key = RedisKeyUtil.pubKey("TP-CORE:willBodyTicket", key);

        Integer count = (Integer) RedisUtil.get(key);
        log.info("metho = willBodyTicket LimitCount, key = {}, count = {}", key, count);
        if (count == null) {
            count = 0;
        }else if (count.intValue() >= 20){
            throw new ServerException("意愿核身次数超限，请次日重试");
        }
        RedisUtil.set(key, count + 1, FmtDate.tomorrowHours(), TimeUnit.HOURS);
    }


    public String getCacheTicket(String appId,String secret,String type,String userId) {
    	Object obj = null;
    	String key = getTicketKey(appId, type);
//    	Object obj = RedisUtil.get(key);
    	if(obj == null || StringUtils.isBlank(obj.toString())) {
    		String access_token = getCacheAccessToken(appId, secret,type);
//    		https://miniprogram-kyc.tencentcloudapi.com/api/oauth2/api_ticket?app_id=xxx&access_token=xxx&type=NONCE&version=1.0.0&user_id=xxx
//    		https://miniprogram-kyc.tencentcloudapi.com/api/oauth2/api_ticket?app_id=xxx&access_token=xxx&type=SIGN&version=1.0.0
    		String getTicketUrl = String.format(apiTicketUrl + "?app_id=%s&access_token=%s&&type=%s&version=1.0.0", appId, access_token,type);
    		if("NONCE".equals(type)) {
    			getTicketUrl = getTicketUrl+"&user_id="+userId;
    		}
        	log.info("tencent获取 findTicket url {}", getTicketUrl);
        	ApiTicketRes result = restTemplate.getForObject(getTicketUrl, ApiTicketRes.class); 
            log.info("tencent findTicket result {} ", result);
        	if(!result.success()) {
            	log.error("获取findTicket异常：{}", result);
            	throw new ServerException(result.getMsg());
            }
        	Ticket ticket = result.getTickets().get(0);
        	obj = ticket.getValue();
//        	RedisUtil.set(key, obj, ticket.getExpire_in()-200, TimeUnit.SECONDS);
    	}
    	return obj.toString();
    }


    /**
     *
     * @param appId
     * @param secret
     * @param type
     * @return
     */
    public String getCacheAccessToken(String appId,String secret,String type) {
    	Object accessToken = null;
//        String key = getAccTokenKey(appId, type);
//        Object accessToken = RedisUtil.get(key);
        if(accessToken == null || StringUtils.isBlank(accessToken.toString())) {
//        	RedisUtil.del(getTicketKey(appId, type));
        	String getTokenUrl = String.format(accessTokenUrl + "?app_id=%s&secret=%s&grant_type=client_credential&version=1.0.0", appId, secret);
            log.info("tencent获取access_token url {}", getTokenUrl);

            AccessTokenRes result = restTemplate.getForObject(getTokenUrl, AccessTokenRes.class);
            log.info("tencent access_token result {} ", result);
            if(!result.success()) {
            	log.error("获取access_token异常：{}", result);
            	throw new ServerException(result.getMsg());
            }
            accessToken= result.getAccess_token();
//            RedisUtil.set(key, accessToken, result.getExpire_in()-200, TimeUnit.SECONDS);
        }
        return accessToken.toString();
    }

    private String getAccTokenKey(String appId,String type) {
    	return RedisKeyUtil.pubKey("tencent_access_token_"+type, appId);
    }
    private String getTicketKey(String appId,String type) {
    	return RedisKeyUtil.pubKey("tencent_api_ticket_"+type, appId);
    }



    /**
     * APP 获取意愿核身
     * @param req
     */
    @Transactional
    public void appWillBody(AppWillBodyResultReq req) {
        WillBodyRecord record = willBodyRecordService.findByOrderNo(req.getOrderNo(), req.getBrand());
        if(null != record){
            log.info("订单号已经初始化了:{}", req);
            return;
        }

        Map map = JSON.parseObject(req.getVariableValue(), Map.class);
        String yjAmt = map.get("yjAmt").toString();

        // 保存腾讯云意愿核身 记录
        WillBodyRecord willBodyRecord = initWillBodyRecord(req, yjAmt,WillType.APP);
        willBodyRecordService.insert(willBodyRecord);


        // 商户双录
        if("0".equals(req.getFaceCode()) && "0".equals(req.getWillCode())
                && (StringUtils.isNotBlank(req.getWillVideoPath()) || StringUtils.isNotBlank(req.getAliVideoPath()))){
            saveWillBody(req.getOwnerNo(), yjAmt, Status.TRUE.getCode(), null, req.getBrand(), req.getVariableValue());
        }
    }

    /**
     * APP 获取意愿核身
     * @param req
     */
    @SneakyThrows
    public void unAppWillBody(AppWillBodyResultReq req) {
        WillBodyRecord record = willBodyRecordService.findByOrderNo(req.getOrderNo(), req.getBrand());
        if(null != record){
            log.info("订单号已经初始化了:{}", req);
            return;
        }

        Map map = JSON.parseObject(req.getVariableValue(), Map.class);
        String yjAmt = map.get("yjAmt").toString();
        CustomerRsp cust = customerQueryClient.findCustByNo(req.getOwnerNo()).getObject();
        WillType  willType = Origin.JL == cust.getOrigin()?WillType.OUT_H5:WillType.APP;
        // 保存腾讯云意愿核身 记录
        record = initWillBodyRecord(req, yjAmt,willType);
        willBodyRecordService.insert(record);
        // 嘉联单独处理
        if (Origin.JL == cust.getOrigin()){
            jlSaveSl(req, cust, record);
            return;
        }

        // 商户双录
        if("0".equals(req.getFaceCode()) && "0".equals(req.getWillCode())
                && (StringUtils.isNotBlank(req.getWillVideoPath()) || StringUtils.isNotBlank(req.getAliVideoPath()))){
            uploadSlToChannel(record);
        }
    }

    /**
     * 嘉联双录处理
     * @param req
     * @param cust
     * @param record
     */
    private void jlSaveSl(AppWillBodyResultReq req, CustomerRsp cust, WillBodyRecord record) {
        JlQuerySlResultReq slReq = new JlQuerySlResultReq(cust.getOrigin().name(), cust.getThirdCustNo(), req.getOrderNo());
        log.info("嘉联双录结果查询参数：{}", JSON.toJSONString(slReq));
        ResultsBean<JlQuerySlResultRsp> slResultBean = originChannelClient.querySlResultToJl(slReq);
        log.info("嘉联双录结果查询结果：{}", JSON.toJSONString(slResultBean));
        if (slResultBean.notSuccess()){
            throw new ServerException(slResultBean.getMessage());
        }

        JlQuerySlResultRsp result = slResultBean.getObject();
        if (result.getSlResult()){
            req.setFaceCode("0");
            req.setWillCode("0");
            unSaveWillBody(record.getOwnerNo(), record.getAmount(),
                    Status.TRUE.getCode(), null, null, record.getBrand(), record.getContent1(), null);

            //更新双录记录
            record.setValidateStatus("FINISH_VERIFY");
            record.setWillCode("0");
            record.setFaceCode("0");
            record.setFaceMsg(req.getTransId());
            willBodyRecordService.update(record);
        }else {
            if (req.isSupplySl()){
               throw new ServerException("双录认证失败，请重新认证");
            }
            log.info("嘉联商户绑机双录认证失败 customerNo：{}", req.getOwnerNo());
        }

    }

    @SneakyThrows
    public void uploadSlToChannel(WillBodyRecord record) throws URISyntaxException {
        log.info("uploadSlToChannel param：{}", JSON.toJSONString(record));

        CustomerRsp customerRsp = customerQueryClient.findCustByNo(record.getOwnerNo()).getObject();
        if (customerRsp.getOrigin() == Origin.JL){
            throw new ServerException("嘉联商户不支持推送双录");
        }

        String signedUrl = null;
        String snapshotUrl = null;
        String snapshotBase64 = null;
        if (StringUtils.isBlank(record.getUrl())){
            //生成视频公共访问链接失效60分钟
            signedUrl = accessUrl+record.getAliPath();
//            aLiYunBiz.getSignedUrl(record.getAliPath(), false);
            snapshotUrl = aLiYunBiz.getSignedUrl(record.getAliPath(), true);

            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(new URI(snapshotUrl), HttpMethod.GET, null, byte[].class);
            //获取entity中的数据
            byte[] body = responseEntity.getBody();
            if (Objects.isNull(body)){
                throw new ServerException("双录视频截图失败");
            }
            snapshotBase64 = java.util.Base64.getEncoder().encodeToString(body);
        }else {
            //如果文件已经下载过了，那就再上传一次oss，给中付传链接过去
            String objName = record.getUrl().substring(record.getUrl().lastIndexOf("/") + 1);
            signedUrl = aLiYunBiz.uploadBase64File(objName, SftpClientUtils.downloadBase64(record.getUrl()));
            log.info("双录视频上传成功，customerNo:{} objName:{} url:{}", record.getOwnerNo(), objName, signedUrl);
        }
        //====================================
        unSaveWillBody(record.getOwnerNo(), record.getAmount(),
                Status.TRUE.getCode(), signedUrl, snapshotBase64, record.getBrand(), record.getContent1(), record.getUrl());

        //更新双录记录
        WillBodyRecord willBodyRecord = willBodyRecordService.findByOrderNo(record.getOrderNo(), record.getBrand());
        willBodyRecord.setValidateStatus("FINISH_VERIFY");
        willBodyRecordService.update(willBodyRecord);
    }

    // 初始化意愿核身 记录
    private WillBodyRecord initWillBodyRecord(AppWillBodyResultReq req, String yjAmt,WillType willType) {
        WillBodyRecord willBodyRecord = new WillBodyRecord();
        willBodyRecord.setOwnerNo(req.getOwnerNo());
        willBodyRecord.setOrderNo(req.getOrderNo());
        willBodyRecord.setWillType(willType.name());
        willBodyRecord.setSource(req.getSource());
        willBodyRecord.setOperator(req.getOperator());
        willBodyRecord.setCreateTime(new Date());
        willBodyRecord.setUpdateTime(new Date());
        willBodyRecord.setAmount(yjAmt);
        willBodyRecord.setUpdateTime(new Date());

        willBodyRecord.setContent1(req.getVariableValue());

        willBodyRecord.setUrl(req.getWillVideoPath());
        willBodyRecord.setAliPath(req.getAliVideoPath());
        willBodyRecord.setAliFlag(YesNo.N.getCode());

        willBodyRecord.setLiveRate(req.getLiveRate());
        willBodyRecord.setSimilarity(req.getSimilarity());   // 人脸比对得分
        willBodyRecord.setValidateStatus("UNKNOWN");         // 验证状态
        willBodyRecord.setFaceCode(req.getFaceCode());      // 意愿核身结果code
        willBodyRecord.setFaceMsg(req.getFaceMsg());        // 意愿核身结果描述
        willBodyRecord.setWillCode(req.getWillCode());      // 意愿核身结果详细实际原因
        willBodyRecord.setWillMsg(req.getWillMsg());        // 意愿核身结果详细实际原因
        willBodyRecord.setBrand(req.getBrand());
        return willBodyRecord;
    }

    /**
     * url不会落库，仅透传到通道
     * @param ownerNo
     * @param amount
     * @param status
     * @param willVideoPath
     * @param brand
     * @param content1
     * @param url
     */
    private void unSaveWillBody(String ownerNo, String amount, String status,
                                String willVideoPath, String snapshotBase64, String brand, String content1, String url) {
        WillBodyBean willBody = new WillBodyBean();
        willBody.setAmount(new BigDecimal(amount));
        willBody.setOwnerNo(ownerNo);
        willBody.setOwnerRole(OwnerRole.CUSTOMER.getCode());
        willBody.setStatus(status);
        willBody.setTemporaryVideoUrl(willVideoPath);
        willBody.setTemporarySnapshotBase64(snapshotBase64);
        willBody.setUrl(url);
        willBody.setCreateTime(new Date());
        willBody.setUpdateTime(new Date());
        willBody.setBrand(brand);
        willBody.setContent1(content1);
        ResultsBean<String> resultsBean = willBodyClient.unSaveOrUpdate(willBody);
        log.info("商户双录状态保存：{},{}", resultsBean, willBody);
        if(resultsBean.notSuccess()){
            throw new ServerException("商户双录状态保存异常");
        }
    }


    // 保存
    private void saveWillBody(String ownerNo, String amount, String status,
                              String willVideoPath, String brand, String content1) {
        WillBodyBean willBody = new WillBodyBean();
        willBody.setAmount(new BigDecimal(amount));
        willBody.setOwnerNo(ownerNo);
        willBody.setOwnerRole(OwnerRole.CUSTOMER.getCode());
        willBody.setStatus(status);
        willBody.setUrl(willVideoPath);
        willBody.setCreateTime(new Date());
        willBody.setUpdateTime(new Date());
        willBody.setBrand(brand);
        willBody.setContent1(content1);
        ResultsBean<String> resultsBean = willBodyClient.saveOrUpdate(willBody);
        log.info("商户双录状态保存：{},{}", resultsBean, willBody);
        if(resultsBean.notSuccess()){
            throw new ServerException("商户双录状态保存异常");
        }
    }


    /**
     * 查询意愿核身结果
     * @param record
     */
    @Transactional
    public void willBodyResult(WillBodyRecord record) {
        Map<String, String> map1 = getAppIdSecret(record.getBrand());
        String appId=map1.get("appId"), secret=map1.get("secret");
        String ticket = getCacheTicket(appId, secret, "SIGN", record.getOwnerNo());

        String version="1.0.0";
        List<String> signList = new ArrayList<>();
        signList.add(appId);
        signList.add(record.getOrderNo());
        signList.add(record.getOrderNo());
        signList.add(version);
        String sign = sign(signList, ticket);

        Map<String, String> map = new HashMap<>();
        map.put("appId", appId);
        map.put("version", "1.0.0");
        map.put("sign", sign);
        map.put("orderNo", record.getOrderNo());
        map.put("nonce", record.getOrderNo());
        // 是否需要获取人脸识别的视频和文件，值为1则返回视频和照片、值为2则返回照片、值为3则返回视频；其他则不返回
        map.put("getFile", "0");
        // 是否需要获取意愿表达的音频文件，默认值为1 值为1则返回，其他值则不返回
        map.put("getWillFile", "0");

        String appWillFaceResultUrl = String.format(apiAppWillFaceResultUrl + "?orderNo=%s", record.getOrderNo());

        log.info("意愿核身识别请求参数 findYsWillBodyResult url,map,record {}, {}, {}", appWillFaceResultUrl, map, record);
        ApiAppWillFaceResultRes result = restTemplate.postForObject(appWillFaceResultUrl, map, ApiAppWillFaceResultRes.class);
        log.info("意愿核身识别结果查询 findYsWillBodyResult result,map,record {},{},{}", result, map, record);

        if(null != result.getResult()){
            ApiAppWillFaceResultRes.WillBodyResult bodyResult = result.getResult();

            if(StringUtils.isNotBlank(record.getFaceCode()) && StringUtils.isNotBlank(record.getWillCode())
                    && StringUtils.isNotBlank(bodyResult.getFaceCode()) && StringUtils.isNotBlank(bodyResult.getWillCode()) ){

                if(!record.getFaceCode().equals(bodyResult.getFaceCode())
                        && !record.getWillCode().equals(bodyResult.getWillCode()) ){
                    record.setFaceCode(bodyResult.getFaceCode());
                    record.setFaceMsg(bodyResult.getFaceMsg());
                    record.setWillCode(bodyResult.getWillCode());
                    record.setWillMsg(bodyResult.getWillMsg());
                    record.setLiveRate(bodyResult.getLiveRate());
                    record.setSimilarity(bodyResult.getSimilarity());

                    String status = result.success() ? Status.TRUE.getCode() : Status.FALSE.getCode();
                    saveWillBody(record.getOwnerNo(), record.getAmount(), status, null, record.getBrand(), record.getContent1());
                }

            }

            record.setContent(JSON.toJSONString(bodyResult));
            record.setValidateStatus("FINISH_VERIFY");
            record.setUpdateTime(new Date());
            willBodyRecordService.update(record);
        }
    }


    //  签名算法
    public static String sign(List<String> values, String ticket) { //values传ticket外的其他参数
        if (values == null) {
            throw new NullPointerException("values is null");
        }
        values.removeAll(Collections.singleton(null));// remove null
        values.add(ticket);
        java.util.Collections.sort(values);

        StringBuilder sb = new StringBuilder();
        for (String s : values) {
            sb.append(s);
        }
        return Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
    }


    /**
     * 下载
     * @param record
     */
    @Transactional
    public void downALiPath(WillBodyRecord record) {
        String filePath = aLiYunBiz.downFile(record.getAliPath(),
                FilePathConstants.getLoaclTempPath(appName)+ record.getOrderNo() +".mp4");
        log.info("下载到ftp地址：{}", filePath);
        record.setUrl(filePath);
        record.setAliFlag(YesNo.Y.getCode());

        saveWillBody(record.getOwnerNo(), record.getAmount(), null, filePath, record.getBrand(), record.getContent1());
        willBodyRecordService.update(record);
        log.info("意愿核身下载视频成功：{}", record);
    }


    /**
     * 小程序意愿核身 获取 EidToken
     * @param param
     * @return
     */
    @Transactional
    public GetEidTokenResponse findEidToken(Map<String, String> param) {
        try{
            FaceidClient client = getFaceidClient();

            // 实例化一个请求对象,每个接口都会对应一个request对象
            GetEidTokenRequest req = new GetEidTokenRequest();
            if (Brand.UK.name().equals(param.get("brand"))) {
                req.setMerchantId(eidMerchantIdUk);
            }else {
                req.setMerchantId(eidMerchantId);
            }
            req.setIdCard(param.get("idNo"));
            req.setName(param.get("name"));

            GetEidTokenConfig config = new GetEidTokenConfig();
            config.setInputType("4");
            config.setIntentionRecognition(Boolean.TRUE);
            config.setUseIntentionVerify(Boolean.TRUE);
            config.setIntentionMode("2");

            IntentionQuestion intentionQuestion = new IntentionQuestion();
            intentionQuestion.setQuestion(param.get("question"));
            intentionQuestion.setAnswers(new String[] {param.get("answer")});
            IntentionQuestion[] IntentionQuestions = new IntentionQuestion[]
                    {intentionQuestion};
            config.setIntentionQuestions(IntentionQuestions);

            req.setConfig(config);

            // 返回的resp是一个GetEidTokenResponse的实例，与请求对象对应
            GetEidTokenResponse resp = client.GetEidToken(req);

            log.info("eid 意愿核身 EidToken:{}", GetEidTokenResponse.toJsonString(resp));

            if(org.springframework.util.StringUtils.isEmpty(resp.getEidToken())){
                throw new ServerException("意愿核身异常");
            }

            // 把请求eidToken 放入缓存
            initRedisWillBody(resp, param);

            // 校验获取次数
            LimitCount(param.get("customerNo"));

            return resp;
        } catch (Exception e) {
            log.error("意愿核身获取eidToekn异常：{}，{}", param, e);
            throw new ServerException(e.getMessage());
        }
    }


    private void initRedisWillBody(GetEidTokenResponse resp, Map<String, String> param) {
        WillBodyRecord record = new WillBodyRecord();
        record.setOrderNo(resp.getEidToken());
        record.setContent1(param.get("variableValue"));
        record.setOwnerNo(param.get("customerNo"));
        record.setOperator(param.get("customerNo"));
        record.setBrand(param.get("brand"));

        Map yjMap = JSON.parseObject(param.get("variableValue"), Map.class);
        String yjAmt = yjMap.get("yjAmt").toString();
        record.setAmount(yjAmt);

        Map<String, Object> map = new HashMap();
        map.put("willStandAnswer", param.get("answer"));   // 要回答的内容
        map.put("willStandText", param.get("question"));     // 播报内容
        record.setContent(JSON.toJSONString(map));

        RedisUtil.set(resp.getEidToken(), JSON.toJSONString(record), 10, TimeUnit.MINUTES);
    }


    /**
     *
     * @param param
     * @return
     * @throws TencentCloudSDKException 
     */
    @Transactional
    public WillBodyRecord saveEidResult(EidWillBodyResultReq param) throws TencentCloudSDKException {

        Object obj = RedisUtil.get(param.getEidToken());
        if(obj == null){
            log.info("eidToken对应的不存在:{}", param);
            throw new ServerException("请重新扫码 意愿核验");
        }

            WillBodyRecord record = JSON.parseObject(obj.toString(), WillBodyRecord.class);

            FaceidClient client = getFaceidClient();

            GetEidResultRequest req = new GetEidResultRequest();
            req.setEidToken(param.getEidToken());
            req.setInfoType("6");

            // 返回的resp是一个GetEidResultResponse的实例，与请求对象对应
            GetEidResultResponse resp = client.GetEidResult(req);
            log.info("eid 意愿核身eidResult :{},{},{}", resp.getIntentionQuestionResult().getFinalResultCode(),
                    resp.getIntentionQuestionResult().getResultCode(), resp.getIntentionQuestionResult().getAsrResult());

            // 组装参数
            record = computerRecord(resp, record, param.getSource());
            willBodyRecordService.insert(record);

            // 商户双录
            if("0".equals(record.getFaceCode()) && "0".equals(record.getWillCode()) ) {
                saveWillBody(record.getOwnerNo(), record.getAmount(), Status.TRUE.getCode(), null, record.getBrand(), record.getContent1());
            }

            return record;

    }


    private WillBodyRecord computerRecord(GetEidResultResponse resp, WillBodyRecord record, String source) {

        if(org.springframework.util.StringUtils.isEmpty(resp.getIntentionQuestionResult())
            || org.springframework.util.StringUtils.isEmpty(resp.getIntentionQuestionResult().getFinalResultCode()) ){
            record.setFaceCode("17");
            record.setFaceMsg("本次流程未完成");
        }else {

            IntentionQuestionResult questionResult = resp.getIntentionQuestionResult();

            record.setFaceCode(questionResult.getFinalResultCode());
            String msg = StringUtils.isEmpty(eidFinalCodeMap.get(questionResult.getFinalResultCode()))
                    ? "双录失败" : eidFinalCodeMap.get(questionResult.getFinalResultCode());
            record.setFaceMsg(msg);
            if("0".equals(record.getFaceCode()) ){
                record.setWillCode(record.getFaceCode());
                record.setWillMsg(record.getFaceMsg());
            }


            Map<String, Object> content = JSON.parseObject(record.getContent(), Map.class);
            if(null != questionResult.getAsrResult() && questionResult.getAsrResult().length > 0){
                content.put("willUserAnswer", JSON.toJSONString(questionResult.getAsrResult()));
            }
            if(null != questionResult.getResultCode() && questionResult.getResultCode().length > 0){
                content.put("resultCode", JSON.toJSONString(questionResult.getResultCode()));
            }
            record.setContent(JSON.toJSONString(content));
        }

        record.setWillType(WillType.EID.name());
        record.setValidateStatus("FINISH_VERIFY");
        record.setSource(source);
        record.setAliFlag(YesNo.N.getCode());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        return record;
    }



    private FaceidClient getFaceidClient(){
        // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey,此处还需注意密钥对的保密
        // 密钥可前往https://console.cloud.tencent.com/cam/capi网站进行获取
        Credential cred = new Credential(eidAppid, eidSecret);

        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(eidUrl);
        if (!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)) {
            httpProfile.setProxyHost(proxyHost);
            httpProfile.setProxyPort(proxyPort);
        }

        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        // 实例化要请求产品的client对象,clientProfile是可选的
        FaceidClient client = new FaceidClient(cred, "", clientProfile);

        return client;
    }


    /**
     * 获取eid 视频
     * @param record
     */
    public void getEIdvideo(WillBodyRecord record) {
        try{
            FaceidClient client = getFaceidClient();

            GetEidResultRequest req = new GetEidResultRequest();
            req.setEidToken(record.getOrderNo());
            req.setInfoType("6");

            // 返回的resp是一个GetEidResultResponse的实例，与请求对象对应
            GetEidResultResponse resp = client.GetEidResult(req);
            IntentionQuestionResult question = resp.getIntentionQuestionResult();
            log.info("eid 意愿核身 result:{},{},{}", question.getFinalResultCode(), question.getResultCode(), question.getAsrResult());

//              try {
//                  // 双录视频截图
//                  byte[] screenshotByte = Base64.decodeBase64(resp.getIntentionQuestionResult().getVideo());
//                  MultipartFile screenshotFile = new Base64DecodeMultipartFile(screenshotByte, "data:image/jpg;base64");
//                  if(screenshotFile.getSize()==0) {
//                      log.info("eid 意愿核身截图不存在,{}", resp.getIntentionQuestionResult().getVideo());
//                      throw new ServerException("意愿核身截图文件为空");
//                  }
//                  String remotePath = FilePathConstants.REMOTE_CUSTOMER_PATH_PREFIX + FilePathConstants.MULTI_MEDIA_PATH;
//                  String screenshotPath = SftpClientUtils.uploadFile(appName, screenshotFile, FileUtil.getRemotePath(remotePath, record.getOwnerNo()),null);
//                  log.info("eid意愿核身截图:ftp ：{},{}", screenshotPath, screenshotFile.getSize());
//
//                  JSONObject jsonObject = StringUtils.isBlank(record.getContent1()) ? new JSONObject() : JSON.parseObject(record.getContent1());
//                  jsonObject.put("screenshotPath", screenshotPath);
//                  record.setContent1(jsonObject.toJSONString());
//              }catch (Exception ex){
//                  log.error("双录视频截图下载失败", ex);
//              }

            byte[] b = Base64.decodeBase64(resp.getIntentionQuestionResult().getVideo());
            MultipartFile multipartFile = new Base64DecodeMultipartFile(b, "data:video/mp4;base64");
            if(multipartFile.getSize()==0) {
            	log.info("eid 意愿核身 video,{}", resp.getIntentionQuestionResult().getVideo());
            	throw new ServerException("双录文件为空");
            }
            String remotePath = FilePathConstants.REMOTE_CUSTOMER_PATH_PREFIX + FilePathConstants.MULTI_MEDIA_PATH;
            String filePath = SftpClientUtils.uploadFile(appName, multipartFile, FileUtil.getRemotePath(remotePath, record.getOwnerNo()),null);
            log.info("eid视频上传:ftp ：{},{}", filePath, multipartFile.getSize());

            record.setUrl(filePath);
            record.setAliFlag(YesNo.Y.getCode());
            willBodyRecordService.update(record);
            saveWillBody(record.getOwnerNo(), record.getAmount(), null, filePath, record.getBrand(), record.getContent1());
        } catch (Exception e) {
        	log.error("eid意愿核身获取视频 异常：{}，{}", record, e);
        	if(e instanceof ServerException) {
        		String ddMsg = "{\"msg\":\"双录文件异常:no:%s,%s\"}";
        		String content = String.format(ddMsg, record.getOwnerNo(),e.getMessage());
        		dingDingBiz.sendWorkMsg4TemplateCode(record.getOrderNo(), "SYS_003", content);
        	}
            throw new ServerException(e.getMessage());
        }
    }


}
