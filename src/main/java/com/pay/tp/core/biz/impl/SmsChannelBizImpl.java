package com.pay.tp.core.biz.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.enums.tp.SmsChannelType;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.sms.SmsParam;
import com.pay.tp.core.biz.SmsChannelBiz;
import com.pay.tp.core.entity.sms.SmsChannel;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.enums.Status;
import com.pay.tp.core.remote.sms.SmsClient;
import com.pay.tp.core.service.sms.SmsChannelContext;
import com.pay.tp.core.service.sms.SmsChannelService;
import com.pay.tp.core.service.sms.SmsClientAysnc;
import com.pay.tp.core.service.sms.SmsMsgService;

/**
 * @Description: 短信发送业务实现
 * @see: SMSSendBizImpl 此处填写需要参考的类
 */
@Component
public class SmsChannelBizImpl implements SmsChannelBiz {
	private Logger logger = LoggerFactory.getLogger(getClass());

	@Autowired
	private SmsChannelService smsChannelService;

	@Autowired
	private SmsMsgService smsMsgService;

	@Autowired
	private SmsChannelContext smsChannelContext;

	@Autowired
	private SmsClientAysnc smsClientAysnc;


	/**
	 * 切换短信通道
	 * @param id
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
	public void updateStatus(String id) {
		logger.info("短信通道切换：{}",id);

		if(StringUtils.isEmpty(id)){
			throw new ServerException("参数异常");
		}

		SmsChannel smsChannel = smsChannelService.findById(id);
		logger.info("短信通道切换,smsChannel:{}", smsChannel);
		if(smsChannel == null){
			throw new ServerException("不存在指定短信通道");
		}

		//将所有启用的禁用
		smsChannelService.updateDisableAll(smsChannel.getChannelType());

		//检查是否还有启用的
		List<SmsChannel> channelList = smsChannelService.findByStatus(smsChannel.getChannelType(), Status.ENABLE.name());
		logger.info("短信通道切换,channelList:{}", JSON.toJSONString(channelList));
		if(channelList != null && !channelList.isEmpty()){
			throw new ServerException("短信通道数据异常");
		}

		smsChannelService.updateStatusById(Status.ENABLE.name(), smsChannel.getId());

		logger.info("短信通道切换结束：{}",id);
	}

	/**
	 * 短信发送
	 * @param param
	 */
	@Override
	public Map<String, Object> send(SmsParam param) {
		Map<String, Object> map = new HashMap<>();
		//要求幂等
		SmsMsg sms = smsMsgService.query(param);
		if (!org.apache.commons.lang3.StringUtils.isEmpty(sms.getResult())) {
			SmsClient smsClient = smsChannelContext.get(sms.getChannelCode());
			return smsClient.parse(sms);
		} else {
			
			List<SmsChannel> smsChannels = null;
			if(Brand.UK.name().equals(param.getBrand())
//					&&StringUtils.isNotBlank(param.getOwnerRole()) 
//					&& OwnerRole.AGENT.name().equals(param.getOwnerRole())
					) {
				smsChannels = smsChannelService.findValid(SmsChannelType.UK_AGENT.name());
			}
			if (smsChannels == null || smsChannels.isEmpty()) {
				smsChannels = smsChannelService.findValid(SmsChannelType.OTHER.name());
			}
//			logger.info("send sms channels:{}", JSON.toJSONString(smsChannels));
			if (smsChannels == null || smsChannels.isEmpty()) {
				map.put("code", "-1");
				map.put("msg", "无可用短信通道");
				return map;
			}

			SmsChannel smsChannel = smsChannels.get(0);
			SmsClient smsClient = smsChannelContext.get(smsChannel.getCode());
			smsClientAysnc.clientRequest(smsClient, sms, smsChannel.getCode());

			map.put("code", 0);
			return map;
		}
	}


}
