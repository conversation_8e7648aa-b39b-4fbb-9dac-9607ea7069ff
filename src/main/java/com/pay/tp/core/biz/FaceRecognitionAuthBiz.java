package com.pay.tp.core.biz;

import java.util.Map;

import com.pay.tp.core.beans.auth.FaceRecognitionAuthParam;

/**
 * @Description: 人脸识别认证业务
 * @see: FaceRecognitionAuthBiz 此处填写需要参考的类
 */
public interface FaceRecognitionAuthBiz {

	/**
	 * @Description  人脸识别认证
	 * @param param
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
	public Map<String, Object> auth(FaceRecognitionAuthParam param) throws Exception;

}
