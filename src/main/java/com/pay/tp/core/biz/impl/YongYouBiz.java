package com.pay.tp.core.biz.impl;

import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.yongyou.YongYouCustomerReq;
import com.pay.tp.core.beans.yongyou.YongYouCustomerResp;
import com.pay.tp.core.remote.yongyou.YongYouClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.rmi.ServerException;

/**
 * 用友业务处理类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Component
public class YongYouBiz {

    @Autowired
    private YongYouClient yongYouClient;

    @Value("${yongyou.api.signKey:}")
    private String signKey;

    @Value("${yongyou.api.partnerId:}")
    private String partnerId;

    @Value("${yongyou.api.company:}")
    private String company;

    /**
     * 添加客户
     *
     * @param customerReq 客户信息请求
     * @return 添加结果
     */
    public YongYouCustomerResp addCustomer(YongYouCustomerReq customerReq) {
        try {
            // 参数校验
            if (customerReq == null || customerReq.getCustomers() == null || customerReq.getCustomers().isEmpty()) {
                throw new ServerException("客户信息不能为空");
            }

            // 检查配置
            if (StringUtils.isBlank(signKey) || StringUtils.isBlank(partnerId) || StringUtils.isBlank(company)) {
                throw new ServerException("用友API配置信息不完整");
            }

            // 业务逻辑处理
            // 这里可以添加客户信息的业务校验、数据转换等逻辑
            validateCustomerInfo(customerReq);

            // 调用客户端发送请求
            YongYouCustomerResp response = yongYouClient.addCustomer(customerReq);

            // 处理响应结果
            if (response != null && Boolean.TRUE.equals(response.getSuccess())) {
                // 可以在这里添加成功后的业务处理逻辑
                log.info("用友添加客户成功，成功数量: {}, 失败数量: {}",
                        response.getData() != null && response.getData().getSuccess() != null ? response.getData().getSuccess().size() : 0,
                        response.getData() != null && response.getData().getFailed() != null ? response.getData().getFailed().size() : 0);

                return response;
            } else {
                String errorMsg = response != null ? response.getMessage() : "添加客户失败";
                throw new ServerException(errorMsg);
            }

        } catch (Exception e) {
            log.error("用友添加客户异常", e);
        }
        return null;
    }

    /**
     * 校验客户信息
     *
     * @param customerReq 客户请求
     */
    private void validateCustomerInfo(YongYouCustomerReq customerReq) {
        // 业务校验逻辑
        for (YongYouCustomerReq.CustomerInfo customer : customerReq.getCustomers()) {
            if (StringUtils.isBlank(customer.getCustomerId())) {
                throw new IllegalArgumentException("客户ID不能为空");
            }
            if (StringUtils.isBlank(customer.getName())) {
                throw new IllegalArgumentException("客户姓名不能为空");
            }
            // 可以添加更多业务校验规则
        }
    }
}
