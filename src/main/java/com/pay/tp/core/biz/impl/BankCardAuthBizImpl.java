package com.pay.tp.core.biz.impl;

import com.pay.frame.cache.util.RedisUtil;
import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.util.DateUtil;
import com.pay.frame.common.base.util.FmtDate;
import com.pay.frame.common.base.util.RedisKeyUtil;
import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.biz.BankCardAuthBiz;
import com.pay.tp.core.configuration.AuthChannelContext;
import com.pay.tp.core.entity.auth.BankcardAuth;
import com.pay.tp.core.entity.auth.TpAuthChannel;
import com.pay.tp.core.enums.BusinessCode;
import com.pay.tp.core.enums.ChannelStatus;
import com.pay.tp.core.exception.AuthRemoteException;
import com.pay.tp.core.mapper.auth.BankCardAuthMapper;
import com.pay.tp.core.remote.auth.BankCardAuthClient;
import com.pay.tp.core.beans.auth.BankCardRes;
import com.pay.tp.core.service.auth.AuthChannelService;
import com.pay.tp.core.service.auth.BankCardAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class BankCardAuthBizImpl implements BankCardAuthBiz{
	private final Logger logger = LoggerFactory.getLogger(BankCardAuthBizImpl.class);
	
	
	@Autowired
	private BankCardAuthService bankCardAuthService;

	@Autowired
	private BankCardAuthMapper bankCardMapper;
	
	@Autowired
	private AuthChannelContext authChannelContext;
	
	@Autowired
	private AuthChannelService authChannelService;
    @Autowired
    private DingDingBiz dingDingBiz;
	
	/**
	 * @Description  鉴权认证
	 * @param param
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
	public Map<String, Object> auth(BankCardAuthParam param) throws Exception {
		//要求幂等
		long start = System.currentTimeMillis();
		BankcardAuth auth = bankCardAuthService.query(param);
		logger.info("auth query times = {} ",(System.currentTimeMillis()-start));
		if (auth != null && !StringUtils.isEmpty(auth.getRspCod())) {
			// 请求参数存在请求结果，则直接返回解析
			return parse(auth);
		} else {
			// 根据路由配置，选择鉴权通道
			List<TpAuthChannel> channels = authChannelService.findByBusiCodeAndStatus(BusinessCode.BANK_CARD_AUTH.name(), ChannelStatus.ENABLE.name());
//			if(Brand.UK.name().equals(param.getBrand())) {
//				channels = channels.stream().filter(p->!p.getChannelNo().equals(ChannelCode.XL_JILONG.name())).collect(Collectors.toList());
//			}else {
//				channels = channels.stream().filter(p->!p.getChannelNo().equals(ChannelCode.XL_BJLD.name())).collect(Collectors.toList());
//			}
			
			Map<String, Object> map = null;
			if(channels != null && !channels.isEmpty()) {
				for (TpAuthChannel channel : channels) {
					//查看每日鉴权结果是否超限
					map = LimitCount(param, channel.getChannelNo()) ;
					if(map != null && !map.isEmpty()) {
						// 鉴权次数超限，使用其他鉴权通道
						continue;
					}
					// 初始化鉴权结果
					auth = create(param, channel.getChannelNo());
					try {
						// 请求鉴权
						BankCardAuthClient bankCardAuthClient = authChannelContext.get(channel.getChannelNo());
						BankCardRes bankCardRes = bankCardAuthClient.request(auth.getRequestNo(),
								auth.getCardNo().trim(), auth.getName().trim(),
								auth.getCidNo().trim(), auth.getMobile(), auth.getCustomerName().trim(),param.getBrand());
						// 更新鉴权结果
						BeanUtils.copyProperties(bankCardRes, auth, "id", "optismistic", "createTime", "requestNo", "name", "cidNo", "mobile", "cardNo","insId");
						bankCardAuthService.update(auth);
						// 解析结果
						map = parse(auth);
					} catch (Exception e) {
						logger.info("鉴权异常，通道：{}，param：{}，err：{}", channel.getChannelName(), param, e.getMessage(), e);
						if(e instanceof AuthRemoteException) {
							try {
								String ddMsg = "{\"msg\":\"卡鉴权通道异常:channelNo:%s,%s\"}";
								String content = String.format(ddMsg, channel.getChannelNo(),e.getMessage());
								dingDingBiz.sendWorkMsg4TemplateCode(param.getRequestNo(), "SYS_003", content);
							} catch (Throwable e2) {
								logger.error(e.getMessage()+"，发送失败",e2);
							}
						}
					}
					if(map != null && !map.isEmpty() && "01".equals(map.get("result"))) {
						// 鉴权成功，跳出
						break;
					}
				}
			}
			return map;
		}
	}
	/** 相同三要素，同通道 每日最多五次错误鉴权 超出提示限制
	 *  商户编号不为空的情况下    用户编号+通道号  最多五次
	 * */
	private Map<String, Object> LimitCount(BankCardAuthParam param, String channelBrand) {
		if(!StringUtils.isEmpty(param.getOwnerNo())){
			String key = DateUtil.formatDate() +"_"+ param.getOwnerNo();
			key = RedisKeyUtil.pubKey("TP-CORE:bankCardAuth", key);
			Integer count = (Integer) RedisUtil.get(key);
			Map<String, Object> map = new HashMap<>();
			logger.info("metho = ownerNo LimitCount, key = {}, count = {}", key, count);
			if (count == null) {
				count = 0;
			}else if (count.intValue() >= 10){
				map.put("result", "02");
				map.put("msg", "验证次数超限，请次日重试");
			}
			RedisUtil.set(key, count + 1, FmtDate.tomorrowHours(), TimeUnit.HOURS);

			return map;
		}

		// 今天 + 姓名 + 卡号 + 身份证号 + 鉴权通道 + 手机号
		String key = DateUtil.formatDate()+"_"+ param.getName() +"_"+ param.getCardNo() +"_"+ param.getCidNo() + "_" +channelBrand + "_"+ param.getMobile() ;
		key=RedisKeyUtil.pubKey("TP-CORE:bankCardAuth", key);
		Integer count = (Integer) RedisUtil.get(key);
		Map<String, Object> map = new HashMap<>();
		logger.info("metho = LimitCount, key = {}, count = {}",key, count);
		if (count == null) {
			count = 0;
		}else if (count.intValue() >= 5){
			map.put("result", "02");
			map.put("msg", "验证次数超限，请次日重试");
		}
		RedisUtil.set(key, count + 1, FmtDate.tomorrowHours(), TimeUnit.HOURS);

		return map;
	}

	/** 初始化鉴权记录 */
	private BankcardAuth create(BankCardAuthParam param, String channelBrand) {
		BankcardAuth auth = new BankcardAuth(param.getRequestNo(), "", param.getCardNo(), param.getCidNo(),
				param.getMobile(), param.getName(), param.getCustomerName(), channelBrand,param.getInsId(),
				param.getOwnerNo(), param.getAuthType());
		auth.setBrand(Optional.ofNullable(param.getBrand()).orElse(Brand.PLUS.name()));
		logger.info("{}",auth);
		bankCardMapper.insert(auth);
		
		return auth;
	}
	
	
	/**
	 * @Description 认证解析
	 * @param auth
	 * @return
	 * @see 01-认证一致 （收费） 
	 * 		02-认证不一致（收费） 
	 * 		03-认证不确定（不收费） 
	 * 		04-认证失败 （不收费） 
	 * 		05-认证受限（收费）
	 */
	private Map<String, Object> parse(BankcardAuth auth) {

        Map<String, Object> map = new HashMap<>();

        if ("000000".equals(auth.getRspCod())) {
            String validateStatus = auth.getValidateStatus();
            if ("01".equals(validateStatus) || "02".equals(validateStatus) || "03".equals(validateStatus)) {
                map.put("result", validateStatus);
                String remark = auth.getRemark();
                if (remark != null) {
                    remark = remark.replace("交易成功,", "");
                }
                map.put("msg", remark);
            } else {
                map.put("result", "03");
                map.put("msg", auth.getRemark());
            }
        } else if ("000097".equals(auth.getRspCod())) {
            map.put("result", "02");
            if (!StringUtils.isEmpty(auth.getRemark())) {
                map.put("msg", auth.getRemark());
            } else {
                map.put("msg", auth.getRspMsg());
            }

        } else {
            map.put("result", "03");
            map.put("msg", auth.getRemark());
        }

        logger.info("metho = parse_{}, auth = {}, map = {}", auth.getChannelBrand(), auth, map);

        return map;
    }
	


	public static Integer getRemainSecondsOneDay(Date currentDate) {
		//使用plusDays加传入的时间加1天，将时分秒设置成0
		LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
						ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
				.withSecond(0).withNano(0);
		LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
				ZoneId.systemDefault());
		//使用ChronoUnit.SECONDS.between方法，传入两个LocalDateTime对象即可得到相差的秒数
		long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
		return (int) seconds;
	}

}
