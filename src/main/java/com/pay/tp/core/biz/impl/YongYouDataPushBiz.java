package com.pay.tp.core.biz.impl;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.yongyou.YongYouCustomerReq;
import com.pay.tp.core.beans.yongyou.YongYouCustomerResp;
import com.pay.tp.core.entity.yongyou.YongYouWorkPhoneBusiness;
import com.pay.tp.core.remote.yongyou.YongYouClient;
import com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;

/**
 * 用友数据推送业务处理类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Component
public class YongYouDataPushBiz {

    @Autowired
    private YongYouWorkPhoneBusinessService yongYouWorkPhoneBusinessService;

    @Autowired
    private YongYouClient yongYouClient;

    /**
     * 批量处理用友数据推送
     *
     * @param batchSize 批量处理大小
     * @return 处理结果
     */
    @Transactional
    public ResultsBean<String> batchProcessPush(int batchSize) {
        try {
            log.info("开始批量处理用友数据推送，批量大小: {}", batchSize);

            // 查询需要推送的数据
            List<YongYouWorkPhoneBusiness> pendingRecords = yongYouWorkPhoneBusinessService.findPendingPushRecords(batchSize);

            if (pendingRecords.isEmpty()) {
                log.info("没有需要推送的数据");
                return ResultsBean.SUCCESS("没有需要推送的数据");
            }

            log.info("查询到需要推送的记录数: {}", pendingRecords.size());


            int updateCount = yongYouWorkPhoneBusinessService.batchIncrementPushReturnNum(pendingRecords);
            log.info("批量更新推送次数完成，更新记录数: {}", updateCount);

            int successCount = 0;
            int failCount = 0;

            // 逐个处理推送
            try {
                // 构建用友客户请求
                YongYouCustomerReq customerReq = buildCustomerRequest(pendingRecords);

                // 调用用友API推送数据
                YongYouCustomerResp result = yongYouClient.addCustomer(customerReq);

                if (result.getSuccess() && result.getData() != null && Boolean.TRUE.equals(result.getSuccess())) {
                    YongYouCustomerResp.CustomerResult data = result.getData();
                    if (data.getSuccess() != null && !data.getSuccess().isEmpty()) {
                        List<String> successIds = new ArrayList<>(data.getSuccess().keySet());
                        handlePushSuccess(successIds, result);
                        successCount = successIds.size();
                    }
                    if (data.getFailed() != null && !data.getFailed().isEmpty()) {
                        List<String> failedIds = new ArrayList<>(data.getFailed().keySet());
                        handlePushFail(failedIds, result);
                        failCount = failedIds.size();
                    }
                }

            } catch (Exception e) {
                List<String> ids = pendingRecords.stream()
                        .map(record -> String.valueOf(record.getId()))
                        .collect(Collectors.toList());
                // 处理异常
                handlePushException(ids, e);
                failCount = ids.size();

            }

            String resultMsg = String.format("批量推送完成，总处理: %d, 成功: %d, 失败: %d",
                    pendingRecords.size(), successCount, failCount);
            log.info(resultMsg);

            return ResultsBean.SUCCESS(resultMsg);

        } catch (Exception e) {
            log.error("批量处理用友数据推送异常", e);
            return ResultsBean.FAIL("批量处理异常: " + e.getMessage());
        }
    }

    /**
     * 处理推送成功的记录
     *
     * @param ids      业务记录
     * @param response 用友响应
     */
    @Transactional
    public void handlePushSuccess(List<String> ids, YongYouCustomerResp response) {
        String returnValue = response.getMessage() != null ? response.getMessage() : "SUCCESS";
        String returnMessage = "推送成功";

        // 如果有详细的成功信息，可以进一步处理
        if (response.getData() != null && response.getData().getSuccess() != null && !response.getData().getSuccess().isEmpty()) {
            returnMessage += ", 成功数量: " + response.getData().getSuccess().size();
        }

        yongYouWorkPhoneBusinessService.updatePushStatusBatch(
                ids,
                YongYouWorkPhoneBusiness.PushStatus.SUCCESS,
                returnValue,
                returnMessage
        );
    }

    /**
     * 处理推送失败的记录
     *
     * @param ids    业务记录
     * @param result 推送结果
     */
    @Transactional
    public void handlePushFail(List<String> ids, YongYouCustomerResp result) {
        String returnValue = result != null ? result.getMessage() : "FAIL";
        String returnMessage = "推送失败: " + returnValue;

        // 如果有详细的失败信息，可以进一步处理
        if (result.getData() != null && result.getData() != null &&
                result.getData().getFailed() != null && !result.getData().getFailed().isEmpty()) {
            returnMessage += ", 失败数量: " + result.getData().getFailed().size();
        }

        yongYouWorkPhoneBusinessService.updatePushStatusBatch(
                ids,
                YongYouWorkPhoneBusiness.PushStatus.FAIL,
                returnValue,
                returnMessage
        );
    }

    /**
     * 处理推送异常的记录
     *
     * @param ids       业务记录
     * @param exception 异常信息
     */
    @Transactional
    public void handlePushException(List<String> ids, Exception exception) {
        String errorMessage = "推送异常: " + exception.getMessage();
        yongYouWorkPhoneBusinessService.updatePushStatusBatch(
                ids,
                YongYouWorkPhoneBusiness.PushStatus.FAIL,
                "EXCEPTION",
                errorMessage
        );
    }

    /**
     * 构建用友客户请求
     *
     * @param records 用友工作手机业务记录
     * @return 用友客户请求
     */
    private YongYouCustomerReq buildCustomerRequest(List<YongYouWorkPhoneBusiness> records) {
        YongYouCustomerReq customerReq = new YongYouCustomerReq();
        List<YongYouCustomerReq.CustomerInfo> customers = new ArrayList<>();

        for (YongYouWorkPhoneBusiness record : records) {
            YongYouCustomerReq.CustomerInfo customerInfo = new YongYouCustomerReq.CustomerInfo();
            customerInfo.setCustomerId(record.getId() + "");
            customerInfo.setName(maskName(record.getRealName())); // 使用脱敏后的姓名
            customerInfo.setPhone(record.getPhoneNo());
            customerInfo.setAddress(record.getOrganCode());
            customers.add(customerInfo);
        }
        customerReq.setCustomers(customers);

        return customerReq;
    }

    private String maskName(String fullName) {
        if (StringUtils.isBlank(fullName)) {
            return fullName;
        }

        fullName = fullName.trim();

        if (fullName.length() == 1) {
            return fullName;
        }

        if (fullName.length() == 2) {
            return fullName.charAt(0) + "*";
        }

        StringBuilder maskedName = new StringBuilder();
        maskedName.append(fullName.charAt(0)); // 保留姓氏

        // 其余部分用*代替
        for (int i = 1; i < fullName.length(); i++) {
            maskedName.append("*");
        }

        return maskedName.toString();
    }

    /**
     * 获取待推送记录统计信息
     *
     * @return 统计信息
     */
    public ResultsBean<String> getPendingPushStatistics() {
        try {
            List<YongYouWorkPhoneBusiness> pendingRecords = yongYouWorkPhoneBusinessService.findPendingPushRecords(Integer.MAX_VALUE);
            String statistics = String.format("当前待推送记录数: %d", pendingRecords.size());
            log.info(statistics);
            return ResultsBean.SUCCESS(statistics);
        } catch (Exception e) {
            log.error("获取待推送记录统计信息异常", e);
            return ResultsBean.FAIL("获取统计信息异常: " + e.getMessage());
        }
    }
}
