package com.pay.tp.core.biz.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.baidu.aip.face.AipFace;
import com.baidu.aip.ocr.AipOcr;
import com.pay.frame.common.base.enums.FileBussinessType;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.Base64Utils;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.BaiDuOcrBean;
import com.pay.tp.core.beans.BaiDuPersonVerifyBean;
import com.pay.tp.core.entity.TpPersonVerifyLog;
import com.pay.tp.core.service.TpPersonVerifyLogService;

import lombok.extern.slf4j.Slf4j;

/**
 * 百度 识别业务
 */
@Slf4j
@Component
public class BaiDuBiz {

    @Value("${common.register.qa:false}")
    private boolean registerQa;
//    @Value("${baidu.acessTokenUrl:}")
//    private String acessTokenUrl;

    // 文字识别
    @Value("${baidu.text.appid:}")
    private String textAppid;
    @Value("${baidu.text.appkey:}")
    private String textAppkey;
    @Value("${baidu.text.secretkey:}")
    private String textSecretkey;

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

//  @Autowired
//  private RestTemplate restTemplate;
  @Autowired
  private TpPersonVerifyLogService tpPersonVerifyLogService;

    
    private static AipOcr client ;
    private static AipFace faceClient ;

    @PostConstruct
    public void init() {
        client = new AipOcr(textAppid, textAppkey, textSecretkey);
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);
        client.setHttpProxy(proxyHost, proxyPort);
        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
//        log.info(client.toString());

        // 可选：设置网络连接参数
//        faceClient = new AipFace(faceAppid, faceAppkey, faceSecretkey);
         faceClient = new AipFace(textAppid, textAppkey, textSecretkey);
        faceClient.setConnectionTimeoutInMillis(2000);
        faceClient.setSocketTimeoutInMillis(60000);
        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
        faceClient.setHttpProxy(proxyHost, proxyPort);  // 设置http代理
    }




    /**
     * 银行卡ocr识别
     * https://ai.baidu.com/ai-doc/OCR/ak3h7xxg3
     * @param bean
     * @return
     * {
     * 	"result": {
     * 		"valid_date": "11/26",
     * 		"bank_card_number": "6259 2333 1128 3491",
     * 		"bank_name": "浦东发展银行",
     * 		"bank_card_type": 2,
     * 		"holder_name": "ZHANG SAN"
     *   },
     * 	"log_id": 1538142261716096521,
     * 	"direction": 3
     * }
     */
    public Map<String, Object> ocrbankcard(BaiDuOcrBean bean){
        try {
            JSONObject jsonObject = client.bankcard(Base64Utils.decode(bean.getImage()), new HashMap<String, String>());
            log.info("baidu 银行卡 ocr 识别结果：{}, {}", bean.getImageType(), jsonObject);
            if(!jsonObject.has("result")){
            	throw new ServerException("银行卡识别异常");
            }
            Map<String, Object> map =  JSON.parseObject(jsonObject.get("result").toString(), Map.class) ;
            if(map==null
            		||map.get("bank_card_type")==null
            		||"0".equals(map.get("bank_card_type").toString())
            		||StringUtils.isBlank(map.get("bank_card_type").toString())
            		||map.get("bank_card_number")==null
            		||StringUtils.isBlank(map.get("bank_card_number").toString())) {
            	throw new ServerException("图片中不包含银行卡或者图片模糊");
            }
            return map;
        } catch (ServerException e) {
        	log.error("baidu 银行卡 ocr识别失败：{},{}", bean.getImageType(), e);
        	throw new ServerException(e.getMessage());
        } catch (Exception e) {
            log.error("baidu 银行卡 ocr识别失败：{},{}", bean.getImageType(), e);
            throw new ServerException("银行卡识别异常");
        }
    }

    /**
     * 身份证 ocr识别
     * https://ai.baidu.com/ai-doc/OCR/rk3h7xzck
     * @param bean
     *
     * 正面的
     *  {
     * 	"words_result": {
     * 		"姓名": {
     * 			"location": { "top": 1108, "left": 1984, "width": 183, "height": 086 },
     * 			"words": "张三"
     * 		},
     * 		"民族": {
     * 			"location": { "top": 1904, "left": 1632, "width": 127, "height": 166 },
     * 			"words": "汉"
     *        },
     * 		"住址": {
     * 			"location": { "top": 1008, "left": 1009, "width": 358, "height": 1391 },
     * 			"words": "北京市xxx几号楼几单元"
     *        },
     * 		"公民身份号码": {
     * 			"location": { "top": 1921, "left": 380, "width": 214, "height": 1099 },
     * 			"words": "350781196403079766"
     *        },
     * 		"出生": {
     * 			"location": { "top": 1133, "left": 1274, "width": 137, "height": 1126 },
     * 			"words": "19640307"
     *        },
     * 		"性别": {
     * 			"location": { "top": 1133, "left": 1448, "width": 243, "height": 139 },
     * 			"words": "男"
     *        }
     *    },
     * 	"words_result_num": 6,
     * 	"idcard_number_type": 1,
     * 	"image_status": "normal",
     * 	"log_id": 1538103310312338700
     * }
     *
     *
     *  身份证反面
     * {
     * 	"words_result": {
     * 		"失效日期": { "words": "20360810",
     * 			"location": { "top": 319, "left": 407, "width": 167, "height": 33 }
     * 		},
     * 		"签发机关": {
     * 			"words": "公安局",
     * 			"location": { "top": 360, "left": 201, "width": 107, "height": 31 }
     *        },
     * 		"签发日期": {
     * 			"words": "20160810",
     * 			"location": { "top": 123, "left": 456, "width": 129, "height": 42 }
     *        }
     *    },
     * 	"log_id": 1538389689228895996,
     * 	"words_result_num": 3,
     * 	"image_status": "reversed_side"
     * }
     *
     */
    public Map<String, Object> ocrIdCard(BaiDuOcrBean bean){
        FileBussinessType fileType = FileBussinessType.valueOf(bean.getImageType());
        try {
            String idCardSide = FileBussinessType.ID_CAED_RS.getCode().equals(bean.getImageType()) ? "front" : "back";

            JSONObject jsonObject = client.idcard(Base64Utils.decode(bean.getImage()), idCardSide, new HashMap<String, String>());
            log.info("baidu "+ fileType.getName() +" ocr 识别结果：{}, {}", bean.getImageType(), jsonObject);

            String errorMsg = idCaed(jsonObject.get("image_status"));
            if(StringUtils.isNotBlank(errorMsg)){
            	throw new ServerException(errorMsg);
            }
            
            if(!jsonObject.has("words_result")){
                throw new ServerException("识别异常");
            }
            
            return JSON.parseObject(jsonObject.get("words_result").toString(), Map.class) ;
        } catch (ServerException e) {
        	log.error("baidu "+ fileType.getName() +" ocr识别失败：{},{}", bean.getImageType(), e);
        	throw new ServerException(e.getMessage());
        } catch (Exception e) {
            log.error("baidu "+ fileType.getName() +" ocr识别失败：{},{}", bean.getImageType(), e);
            throw new ServerException(fileType.getName() +"识别异常");
        }
    }

    private String idCaed(Object image_status) {
//      image_status	是	string	
//    	normal-识别正常
//      reversed_side-身份证正反面颠倒
//      non_idcard-上传的图片中不包含身份证
//      blurred-身份证模糊
//      other_type_card-其他类型证照
//      over_exposure-身份证关键字段反光或过曝
//      over_dark-身份证欠曝（亮度过低）
//      unknown-未知状态
    	 if("normal".equals(image_status)){
    		 return null;
         }
    	if("reversed_side".equals(image_status)) {
    		return "身份证正反面颠倒";
    	}else if("non_idcard".equals(image_status)) {
    		return "上传的图片中不包含身份证";
    	}else if("blurred".equals(image_status)||"other_type_card".equals(image_status)||"unknown".equals(image_status)) {
    		return "图片中不包含身份证或者图片模糊";
    	}else if("over_exposure".equals(image_status)) {
    		return "身份证关键字段反光或过曝";
    	}else if("over_dark".equals(image_status)) {
    		return "身份证欠曝（亮度过低）";
    	}
    	return "图片中不包含身份证或者图片模糊";
    }


    /**
     * 人脸识别
     * https://ai.baidu.com/ai-doc/FACE/yk37c1u4t
     * @param bean
     * @return
     * {
     * 	"result": {
     * 		"face_num": 1,
     * 		"face_list": [{
     * 			"angle": {
     * 				"roll": -3.16,
     * 				"pitch": 7.77,
     * 				"yaw": 1.37
     *           },
     * 			"face_token": "763527891ea0da24e24237d055bce8df",
     * 			"location": {
     * 				"top": 137.84,
     * 				"left": 62.29,
     * 				"rotation": -2,
     * 				"width": 170,
     * 				"height": 161
     *           },
     * 			 "face_probability": 1
     * 		  }]
     *    },
     * 	"log_id": 3581118148,
     * 	"error_msg": "SUCCESS",
     * 	"cached": 0,
     * 	"error_code": 0,
     * 	"timestamp": 1655618381
     * }
     */
    public Map<String, Object> ocrFaceDetect(BaiDuOcrBean bean) {
        try {
            String imageType = "BASE64";
            HashMap<String, String> map = new HashMap<>();
            map.put("face_field", "quality,eye_status");
            map.put("max_face_num", "1");
            map.put("liveness_control", "NORMAL");

            // 人脸检测
            JSONObject jsonObject = faceClient.detect(bean.getImage(), imageType, map);
            if ("222202".equals(jsonObject.get("error_code"))) {
                throw new ServerException("未检测到人脸");
            }
            log.info("baidu 人脸检测 ocr 识别结果：{}, {}", bean.getImageType(), jsonObject);
            if(!jsonObject.has("result")){
                throw new ServerException("人脸检测识别异常");
            }
            return JSON.parseObject(jsonObject.get("result").toString(), Map.class) ;
        } catch (ServerException e) {
        	log.error("baidu 人脸检测 ocr识别失败：{},{}", bean.getImageType(), e);
        	throw new ServerException(e.getMessage());
        } catch (Exception e) {
            log.error("baidu 人脸检测 ocr识别失败：{},{}", bean.getImageType(), e);
            throw new ServerException("人脸检测识别异常");
        }
    }
    /**
     * 
     * {
    "error_code": 0,
    "error_msg": "SUCCESS",
    "log_id": 4575553579001,
    "timestamp": 1626782958,
    "cached": 0,
    "result": 
    {
        "score": 84.8769989
    }
}
错误码：https://aca.bce.baidu.com/doc/FACE/s/zl958mm8k
     * @param bean
     * @return
     */
    public Map<String, Object> personVerify(BaiDuPersonVerifyBean bean) {
    	try {
    		String imageType = "BASE64";
    		HashMap<String, String> map = new HashMap<>();
    		JSONObject jsonObject = faceClient.personVerify(bean.getImage(), imageType, bean.getIdentityNo(), bean.getLegalPerson(), map);
    		log.info("baidu 人脸实名认证 ocr 识别结果：{}, {},{},{}", bean.getImageType(),bean.getIdentityNo(),bean.getLegalPerson(), jsonObject);
//    		{"error_msg":"No permission to access data","error_code":6}
//    		{"result":{"score":94.017},"log_id":860449440,"error_msg":"SUCCESS","cached":0,"error_code":0,"timestamp":1657246460}
    		TpPersonVerifyLog tpPersonVerifyLog = new TpPersonVerifyLog();
    		tpPersonVerifyLog.setBrand(bean.getBrand());
    		if(jsonObject.has("error_code")) {
    			tpPersonVerifyLog.setErrorCode(jsonObject.get("error_code").toString());
    		}
    		if(jsonObject.has("error_msg")) {
    			tpPersonVerifyLog.setErrorMsg(StringUtils.substring(jsonObject.get("error_msg").toString(), 0, 50));
    		}
    		tpPersonVerifyLog.setIdentityNo(bean.getIdentityNo());
    		tpPersonVerifyLog.setLegalPerson(bean.getLegalPerson());
    		Map map2 = null;
    		if(jsonObject.has("result")&&jsonObject.get("result")!=null&&StringUtils.isNotBlank(jsonObject.get("result").toString())) {
    			map2 = JSON.parseObject(jsonObject.get("result").toString(), Map.class);
    			if(map2!=null&&map2.get("score")!=null) {
    				tpPersonVerifyLog.setScore(map2.get("score").toString());
    			}
    		}
    		if(jsonObject.has("log_id")&&jsonObject.get("log_id")!=null) {
    			tpPersonVerifyLog.setSourceLogId(jsonObject.get("log_id").toString());
    		}
    		tpPersonVerifyLog.setUserNo(bean.getUserNo());
    		tpPersonVerifyLog.setVerifySource("BAIDU");
			tpPersonVerifyLogService.insert(tpPersonVerifyLog);
			if(null == map2){
    			throw new ServerException("识别异常");
    		}
			//TODO 测试关闭人脸
			if(!registerQa) {
				if(Float.valueOf(tpPersonVerifyLog.getScore()).floatValue()<75) {
	    			throw new ServerException("活体比对不合格，请重试");
				}
			}
    		return map2;
    	} catch (ServerException e) {
        	log.error("baidu 活体认证 ocr识别失败：{},{}", bean.getImageType(), e);
        	throw new ServerException(e.getMessage());
    	} catch (Exception e) {
    		log.error("baidu 活体认证 ocr识别失败：{},{}", bean.getImageType(), e);
    		throw new ServerException("实名认证识别异常");
    	}
    }
//    @Override
//    public String verifyToken() {
//        String accessToken = getCacheAccessToken(faceAppkey, faceSecretkey, "BAIDU_FACE_ACCESS_TOKEN");
//        if(StringUtils.isBlank(accessToken.toString())) {
//            log.error("获取accessToken失败");
//            throw new ServerException("获取信息失败，请重试");
//        }
//        String getTokenUrl = String.format(verifyTokenUrl + "?access_token=%s", accessToken);
//
//        Map<String,String> map = new HashMap<String, String>();
//        map.put("plan_id", planId);
//
//        Map<String, Object> result = restTemplate.postForObject(getTokenUrl, map, Map.class);
//        log.info("login params {}  result {} ", planId, result);
//
//        if(result.get("success") == null) {
//            log.error("获取verifyToken失败：{}", result);
//            throw new ServerException("调取人脸识别失败，请重试");
//        }
//
//        if(Boolean.valueOf(result.get("success").toString())){
//            Map<String, String> tokenMap = (Map<String, String>) result.get("result");
//            return tokenMap.get("verify_token");
//        }else {
//            throw new ServerException(result.get("result").toString());
//        }
//    }


    /**
     *
     * @param appkey
     * @param secretkey
     * @return
     */
//    public String getCacheAccessToken(String appkey,String secretkey, String keyPrefix) {
//        String key = RedisKeyUtil.pubKey(keyPrefix, appkey);
//        Object accessToken = RedisUtil.get(key);
//        if(accessToken == null || StringUtils.isBlank(accessToken.toString())) {
//            accessToken = getAccessToken(appkey, secretkey);
//            RedisUtil.set(key, accessToken, 25, TimeUnit.DAYS);
//        }
//        return accessToken.toString();
//    }


//    /**
//     * 获取  access_token
//     * @param appkey
//     * @param secretkey
//     * @return
//     */
//    private String getAccessToken(String appkey, String secretkey) {
//        String getTokenUrl = String.format(acessTokenUrl + "?grant_type=client_credentials&client_id=%s&client_secret=%s", appkey, secretkey);
//        log.info("baidu获取access_token url {}", getTokenUrl);
//
//        Map<String, Object> result = restTemplate.postForObject(getTokenUrl, null, Map.class);
//        log.info("baidu access_token result {} ", result);
//
//        if(result.get("access_token") == null) {
//            log.error("获取access_token异常：{}", result);
//            throw new ServerException("调用百度异常，请重试");
//        }
//
//        String accessToken = result.get("access_token").toString();
//        return accessToken;
//    }

//-------------------------------------------

    /**
     * 银行卡ocr识别
     * https://ai.baidu.com/ai-doc/OCR/ak3h7xxg3
     * @param bean
     * @return
     * {
     * 	"result": {
     * 		"valid_date": "11/26",
     * 		"bank_card_number": "6259 2333 1128 3491",
     * 		"bank_name": "浦东发展银行",
     * 		"bank_card_type": 2,
     * 		"holder_name": "ZHANG SAN"
     *   },
     * 	"log_id": 1538142261716096521,
     * 	"direction": 3
     * }
     * 银行卡类型，0：不能识别; 1：借记卡; 2：贷记卡（原信用卡大部分为贷记卡）; 3：准贷记卡; 4：预付费卡
     */
    
    public Map<String,Object> ocrbankcardYs(BaiDuOcrBean bean){
        try {
        	HashMap<String, String> a = new HashMap<String, String>();
            a.put("detect_direction", "true");
            JSONObject jsonObject = client.bankcard(Base64Utils.decode(bean.getImage()), a);
            jsonObject.remove("card_image");
            log.info("baidu 银行卡 ocr 识别结果：{}，{}, {}", bean.getUserNo(), bean.getImageType(), jsonObject);
            if(!jsonObject.has("result")){
            	throw new ServerException("银行卡识别异常");
            }
            Map<String, Object> resultMap =  JSON.parseObject(jsonObject.get("result").toString(), Map.class) ;
            if(resultMap==null
            		||resultMap.get("bank_card_number")==null
            		||StringUtils.isBlank(resultMap.get("bank_card_number").toString())
            		||("0".equals(resultMap.get("bank_card_type").toString())
                    		&&StringUtils.isBlank(resultMap.get("bank_name").toString())
            				)) {
            	throw new ServerException("图片中不包含银行卡或者图片模糊");
            }
//            Map<String, Object> retMap = new HashMap<String, Object>();
//            retMap.put("bankAccountNo", resultMap.get("bank_card_number").toString());
//            if(resultMap.get("bank_card_type")!=null) {
//            	retMap.put("cardType", resultMap.get("bank_card_type").toString());
//            }
////        	:{"valid_date":"06/26","bank_card_number":"6217 0000 1008 3046 790","bank_name":"建设银行","bank_card_type":1,"holder_name":""}
            Map wordsResult = JSON.parseObject(jsonObject.toString(), Map.class) ;
            return wordsResult;
        } catch (ServerException e) {
        	log.error("baidu 银行卡 ocr识别失败：{},{}", bean.getImageType(), e);
        	throw new ServerException(e.getMessage());
        } catch (Exception e) {
            log.error("baidu 银行卡 ocr识别失败：{},{}", bean.getImageType(), e);
            throw new ServerException("银行卡识别异常");
        }
    }

    /**
     * 身份证 ocr识别
     * https://ai.baidu.com/ai-doc/OCR/rk3h7xzck
     * @param bean
     *
     * 正面的
     *  {
     * 	"words_result": {
     * 		"姓名": {
     * 			"location": { "top": 1108, "left": 1984, "width": 183, "height": 086 },
     * 			"words": "张三"
     * 		},
     * 		"民族": {
     * 			"location": { "top": 1904, "left": 1632, "width": 127, "height": 166 },
     * 			"words": "汉"
     *        },
     * 		"住址": {
     * 			"location": { "top": 1008, "left": 1009, "width": 358, "height": 1391 },
     * 			"words": "北京市xxx几号楼几单元"
     *        },
     * 		"公民身份号码": {
     * 			"location": { "top": 1921, "left": 380, "width": 214, "height": 1099 },
     * 			"words": "350781196403079766"
     *        },
     * 		"出生": {
     * 			"location": { "top": 1133, "left": 1274, "width": 137, "height": 1126 },
     * 			"words": "19640307"
     *        },
     * 		"性别": {
     * 			"location": { "top": 1133, "left": 1448, "width": 243, "height": 139 },
     * 			"words": "男"
     *        }
     *    },
     * 	"words_result_num": 6,
     * 	"idcard_number_type": 1,
     * 	"image_status": "normal",
     * 	"log_id": 1538103310312338700
     * }
     *
     *
     *  身份证反面
     * {
     * 	"words_result": {
     * 		"失效日期": { "words": "20360810",
     * 			"location": { "top": 319, "left": 407, "width": 167, "height": 33 }
     * 		},
     * 		"签发机关": {
     * 			"words": "公安局",
     * 			"location": { "top": 360, "left": 201, "width": 107, "height": 31 }
     *        },
     * 		"签发日期": {
     * 			"words": "20160810",
     * 			"location": { "top": 123, "left": 456, "width": 129, "height": 42 }
     *        }
     *    },
     * 	"log_id": 1538389689228895996,
     * 	"words_result_num": 3,
     * 	"image_status": "reversed_side"
     * }
     *
     */
    
    public Map<String, Object> ocrIdCardYs(BaiDuOcrBean bean){
        FileBussinessType fileType = FileBussinessType.valueOf(bean.getImageType());
        try {
            String idCardSide = FileBussinessType.ID_CAED_RS.getCode().equals(bean.getImageType()) ? "front" : "back";
            HashMap<String, String> a = new HashMap<String, String>();
            a.put("detect_direction", "true");
            a.put("detect_quality", "true");
            a.put("detect_risk", "false");
            a.put("detect_card", "true");
//            log.info(bean.getImage());
           JSONObject jsonObject = client.idcard(Base64Utils.decode(bean.getImage()), idCardSide, a);
           jsonObject.remove("card_image");
            log.info("baidu "+ fileType.getName() +" ocr 识别结果：{},{}, {}", bean.getUserNo(),bean.getImageType(), jsonObject);
            String errorMsg = idCaed(jsonObject.get("image_status"));
            if(StringUtils.isNotBlank(errorMsg)){
            	throw new ServerException(errorMsg);
            }
//            if(!jsonObject.has("words_result")){
//                throw new ServerException("识别异常");
//            }
            Map wordsResult = JSON.parseObject(jsonObject.toString(), Map.class) ;
            return wordsResult;
            
//            Map<String, Object> retMap = new HashMap<String, Object>();
//            Map wordsResult = JSON.parseObject(jsonObject.get("words_result").toString(), Map.class) ;
//            if(FileBussinessType.ID_CARD_RS.getCode().equals(bean.getImageType())) {
//            	Map<String, Object> nameMap = (Map<String, Object>) wordsResult.get("姓名");
//            	Map<String, Object> idMap = (Map<String, Object>) wordsResult.get("公民身份号码");
//            	retMap.put("legalPerson", nameMap.get("words").toString());
//            	retMap.put("identityNo", idMap.get("words").toString());
//            }else {
//            	Map<String, Object> idStartMap = (Map<String, Object>) wordsResult.get("签发日期");
//                Map<String, Object> idEndMap = (Map<String, Object>) wordsResult.get("失效日期");
//                retMap.put("identityExpDataStart", idStartMap.get("words").toString());
//                retMap.put("identityExpDataEnd", idEndMap.get("words").toString());
//            }
//            return retMap;
        } catch (ServerException e) {
        	log.error("baidu "+ fileType.getName() +" ocr识别失败：{},{}", bean.getImageType(), e);
        	throw new ServerException(e.getMessage());
        } catch (Exception e) {
            log.error("baidu "+ fileType.getName() +" ocr识别失败：{},{}", bean.getImageType(), e);
            throw new ServerException(fileType.getName() +"识别异常");
        }
    }
    
    /**
     * 人脸识别
     * https://ai.baidu.com/ai-doc/FACE/yk37c1u4t
     * @param bean
     * @return
     * {
     * 	"result": {
     * 		"face_num": 1,
     * 		"face_list": [{
     * 			"angle": {
     * 				"roll": -3.16,
     * 				"pitch": 7.77,
     * 				"yaw": 1.37
     *           },
     * 			"face_token": "763527891ea0da24e24237d055bce8df",
     * 			"location": {
     * 				"top": 137.84,
     * 				"left": 62.29,
     * 				"rotation": -2,
     * 				"width": 170,
     * 				"height": 161
     *           },
     * 			 "face_probability": 1
     * 		  }]
     *    },
     * 	"log_id": 3581118148,
     * 	"error_msg": "SUCCESS",
     * 	"cached": 0,
     * 	"error_code": 0,
     * 	"timestamp": 1655618381
     * }
     */
    
    public Map<String, Object> ocrFaceDetectYs(BaiDuOcrBean bean) {
        try {
            String imageType = "BASE64";
            HashMap<String, String> map = new HashMap<>();
            map.put("face_field", "quality,eye_status");
            map.put("max_face_num", "1");
            map.put("liveness_control", "NORMAL");

            // 人脸检测
            JSONObject jsonObject = faceClient.detect(bean.getImage(), imageType, map);
            Map wordsResult = JSON.parseObject(jsonObject.toString(), Map.class) ;
            return wordsResult;
            
//            if ("222202".equals(jsonObject.get("error_code"))) {
//                throw new ServerException("未检测到人脸");
//            }
//            log.info("baidu 人脸检测 ocr 识别结果：{}, {}", bean.getImageType(), jsonObject);
//            if(!jsonObject.has("result")){
//                throw new ServerException("人脸检测识别异常");
//            }
//            return JSON.parseObject(jsonObject.get("result").toString(), Map.class) ;
        } catch (ServerException e) {
        	log.error("baidu 人脸检测 ocr识别失败：{},{}", bean.getImageType(), e);
        	throw new ServerException(e.getMessage());
        } catch (Exception e) {
            log.error("baidu 人脸检测 ocr识别失败：{},{}", bean.getImageType(), e);
            throw new ServerException("人脸检测识别异常");
        }
    }
}
