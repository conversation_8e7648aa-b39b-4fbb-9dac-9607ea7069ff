package com.pay.tp.core.biz.impl;

import com.pay.frame.common.base.util.JsonUtils;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.sms.SmsMsgReq;
import com.pay.tp.core.entity.msgmanage.*;
import com.pay.tp.core.enums.ChannelType;
import com.pay.tp.core.enums.Status;
import com.pay.tp.core.remote.sms.DingDingMsgClient;
import com.pay.tp.core.service.msgmanage.*;
import com.pay.tp.core.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉消息业务处理
 *
 * <AUTHOR>
 */
@Component
public class DingDingBiz {
    Logger log = LoggerFactory.getLogger(DingDingBiz.class);

    @Autowired
    private MsgChannelService msgChannelService;

    @Autowired
    private MsgChannelConfigService msgChannelConfigService;

    @Autowired
    private MsgRecordService msgRecordService;

    @Autowired
    private MsgTemplateService msgTemplateService;

    @Autowired
    private MsgUserService msgUserService;

    @Autowired
    private DingDingMsgClient dingDingMsgClient;

    /**
     * 获取所有钉钉通道
     *
     * @return
     */
    public List<MsgChannel> findAllChannel() {
        return msgChannelService.findByType(ChannelType.DING_DING.name());
    }

    /**
     * 指定用户发送钉钉工作通知
     */
    private boolean sendWorkMsg4Users(List<MsgUser> userList,
                                      String requestNo,
                                      MsgTemplate template,
                                      MsgChannel channel,
                                      String templateParam) {



        String channelUserNoListStr = userList.stream()
                .collect(Collectors.mapping(u -> u.getChannelUserNo(),
                        Collectors.joining(",")));

        String msgContent = msgTemplateService.buildMsgContent(template, templateParam);

        Map<String, String> configMap = msgChannelConfigService.findFlatMapConfigByChannelCode(channel.getCode());
        String accessToken = configMap.get("access_token");
        String agentId = configMap.get("agent_id");

        boolean pass = extFilter(template, msgContent);

        Map<String, Object> result = new HashMap<>();
        if (pass) {
            result = dingDingMsgClient.asyncSendV2(accessToken, agentId, channelUserNoListStr,
                    requestNo + " : " + msgContent);
        } else {
            result.put("errcode", "-2");
            result.put("errmsg", "消息过滤器拦截");
        }

        String errcode = result.getOrDefault("errcode", -1).toString();
        String resultInfo = JsonUtil.toJson(result);
        
        msgRecordInsertBatch(userList, requestNo, template, templateParam, msgContent, errcode, resultInfo);
//        log.info("send work msg 4 users 【template: {}, content: {}, result: {}】", template, msgContent, result);
        return errcode.equals("0");
    }

    private boolean checkIdempotent(String requestNo) {
        List<MsgRecord> records = msgRecordService.findByRequestNo(requestNo);
        if (!CollectionUtils.isEmpty(records)) {
            log.info("ding ding msg record idempotent {}", requestNo);
            return true;
        }
        return false;
    }

    private void msgRecordInsertBatch(List<MsgUser> userList, String requestNo, MsgTemplate template, String templateParam, String msgContent, String errcode, String resultInfo) {
        List<MsgRecord> recordList = userList.stream()
                .map(u -> {
                    MsgRecord record = msgRecordService.build(requestNo, u, template.getId(), msgContent,
                            templateParam);
                    record.setResultCode(errcode)
                            .setResult(resultInfo);
                    return record;
                }).collect(Collectors.toList());
        msgRecordService.batchInsert(recordList);
    }

    /**
     * 发送自定义机器人群消息
     *
     * @param userList
     * @param requestNo
     * @param template
     * @param channel
     * @param templateParam
     * @return
     */
    private boolean sendRobotMsg(List<MsgUser> userList,
                                 String requestNo,
                                 MsgTemplate template,
                                 MsgChannel channel,
                                 String templateParam) {

        List<String> userIdList = userList.stream()
                .map(MsgUser::getChannelUserNo)
                .collect(Collectors.toList());

        String msgContent = msgTemplateService.buildMsgContent(template, templateParam);

        Map<String, String> configMap = msgChannelConfigService.findFlatMapConfigByChannelCode(channel.getCode());
        String accessToken = configMap.get("access_token");
        String secret = configMap.get("secret");

        boolean pass = extFilter(template, msgContent);

        Map<String, Object> result = new HashMap<>();
        if (pass) {
            result = dingDingMsgClient.robotSendMsg(accessToken, secret, null, userIdList,
                    requestNo + " : " + msgContent);
        } else {
            result.put("errcode", "-2");
            result.put("errmsg", "消息过滤器拦截");
        }

        String errcode = result.getOrDefault("errcode", -1).toString();
        String resultInfo = JsonUtil.toJson(result);

        msgRecordInsertOne(requestNo, template, templateParam, msgContent, errcode, resultInfo);

        return errcode.equals("0");
    }

    private void msgRecordInsertOne(String requestNo, MsgTemplate template, String templateParam, String msgContent, String errcode, String resultInfo) {
        MsgRecord record = msgRecordService.build(requestNo, template.getChannelCode(), template.getId(), msgContent,
                templateParam);
        record.setResultCode(errcode)
                .setResult(resultInfo);
        msgRecordService.insert(record);
    }



    /**
     * 指定模板发送消息
     */
    public boolean sendWorkMsg4TemplateCode(String requestNo,
                                            String templateCode,
                                            String templateParam) {
        // 幂等校验
        if (checkIdempotent(requestNo)) {
            return true;
        }

        MsgTemplate template = msgTemplateService.findByTemplateCode(templateCode);
        if(template == null){
            log.error("发送钉钉工作通知失败, 未查询到模板信息, templateCode：{}。", templateCode);
            return false;
        }
        MsgChannel channel = msgChannelService.findByCode(template.getChannelCode());
        if(channel == null){
            log.error("发送钉钉工作通知失败, 未查询到消息通道, channelCode：{}。", template.getChannelCode());
            return false;
        }
        List<MsgUser> userList = msgUserService.findByGroupNo(template.getChannelCode(), template.getUserGroupNo());
        if (CollectionUtils.isEmpty(userList)) {
            log.error("发送钉钉工作通知失败, 缺失通知人列表, channelCode: {}, userGroupNo: {}。", template.getChannelCode(), template.getUserGroupNo());
            return false;
        }

        return sendWorkMsg4Users(userList, requestNo, template, channel, templateParam);
    }

    /**
     * 指定模板发送自定义机器人消息
     */
    public boolean sendRobotMsg2TemplateCode(String requestNo,
                                             String templateCode,
                                             String templateParam) {

        // 幂等校验
        if (checkIdempotent(requestNo)) {
            return true;
        }
        MsgTemplate template = msgTemplateService.findByTemplateCode(templateCode);
        if(template == null){
            log.error("发送钉钉自定义机器人消息失败, 未查询到模板信息, templateCode：{}。", templateCode);
            return false;
        }
        MsgChannel channel = msgChannelService.findByCode(template.getChannelCode());
        if(channel == null){
            log.error("发送钉钉自定义机器人消息失败, 未查询到消息通道, templateCode：{}。", template.getChannelCode());
            return false;
        }
        List<MsgUser> userList = msgUserService.findByGroupNo(template.getChannelCode(), template.getUserGroupNo());



        if(ChannelType.DING_DING.name().equals(channel.getType())){
            if (CollectionUtils.isEmpty(userList)) {
                log.error("发送钉钉工作通知失败, 缺失通知人列表, channelCode: {}, userGroupNo: {}。", template.getChannelCode(), template.getUserGroupNo());
                return false;
            }

            return sendWorkMsg4Users(userList, requestNo, template, channel, templateParam);
        }else if(ChannelType.DING_ROBOT.name().equals(channel.getType())){
            return sendRobotMsg(userList, requestNo, template, channel, templateParam);
        }else{
            log.error("发送钉钉自定义机器人消息失败, 渠道类型错误 , channelCode: {}, channelType: {}。", channel.getCode(), channel.getType());
            return false;
        }
    }

    /**
     * 装载 accessToken
     */
    public boolean loadAccessToken(String channelCode) {
        String token = getAccessToken(channelCode);
        MsgChannelConfig accessToken = new MsgChannelConfig();
        accessToken.setCfgParam("access_token")
                .setCfgValue(token)
                .setChannelCode(channelCode)
                .setStatus(Status.ENABLE);
        msgChannelConfigService.save(accessToken);
        return healthCheck(channelCode);
    }

    public String getAccessToken(String channelCode) {
        MsgChannelConfig appKey = msgChannelConfigService.findByChannelCodeAndCfgParam(channelCode, "appkey");
        MsgChannelConfig appsecret = msgChannelConfigService.findByChannelCodeAndCfgParam(channelCode, "appsecret");
        String token = dingDingMsgClient.getAccessToken(appKey.getCfgValue(), appsecret.getCfgValue());
        return token;
    }


    /**
     * 钉钉通道心跳检测
     */
    public boolean healthCheck(String channelCode) {
        log.info("ding ding health check {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        MsgChannelConfig tokenConfig = msgChannelConfigService.findByChannelCodeAndCfgParam(channelCode, "access_token");
        if (tokenConfig == null) {
            return false;
        }
        String accessToken = getAccessToken(channelCode);
        return accessToken.equals(tokenConfig.getCfgValue());
    }

    /**
     * 激活钉钉用户（拉取通道方用户ID，获取成功使其生效）
     *
     * @param user
     * @return
     */
    public boolean activeUser(MsgUser user) {
        MsgUser existsUser = msgUserService.findByUserNo(user.getUserNo(), user.getChannelCode());
        if (existsUser == null) {
            log.error("active user false {}", existsUser);
            return false;
        }
        String accessToken = getAccessToken(user.getChannelCode());
        String channelUserNo = dingDingMsgClient.getUserIdByMobile(accessToken, user.getPhoneNo());
        existsUser.setChannelUserNo(channelUserNo);
        existsUser.setStatus(Status.ENABLE);
        msgUserService.updateByPrimaryKey(existsUser);
        return true;
    }

    /**
     * 扩展消息过滤功能
     *
     * @param msgTemplate
     * @param msgContent
     * @return
     */
    public boolean extFilter(MsgTemplate msgTemplate, String msgContent) {
        try {
            String extInfo = msgTemplate.getExtInfo();
            if (StringUtils.isEmpty(extInfo)) {
                return true;
            }
            MsgTemplate.ExtInfo ext = JsonUtils.json2Bean(extInfo, MsgTemplate.ExtInfo.class);

            if (ext.getFilter() == null) {
                return true;
            }

            List<String> pass = ext.getFilter().getPass();
            if (!CollectionUtils.isEmpty(pass)) {
                return pass.stream().filter(p -> msgContent.contains(p)).count() > 0;
            }

            List<String> reject = ext.getFilter().getReject();
            if (!CollectionUtils.isEmpty(reject)) {
                return reject.stream().filter(r -> msgContent.contains(r)).count() == 0;
            }
        } catch (Exception e) {
            log.error("ext filter error {}, {}", msgTemplate, msgContent, e);
        }

        return true;
    }


}
