package com.pay.tp.core.exception;

/**
 * 定义服务异常
 *
 * <AUTHOR>
 */
public class AuthRemoteException extends RuntimeException {
	private static final long serialVersionUID = 1L;

	protected String errCode;

    protected String errMsg;

    public AuthRemoteException() {
    	super();
    }
    public AuthRemoteException(String errMsg) {
    	super(errMsg);
    	this.errMsg = errMsg;
    }
    public AuthRemoteException(String errCode, String errMsg) {
        super(errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public AuthRemoteException(String errCode, String errMsg, String message) {
        super(message);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public AuthRemoteException(String errCode, String errMsg, Throwable e) {
        super(errMsg, e);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
