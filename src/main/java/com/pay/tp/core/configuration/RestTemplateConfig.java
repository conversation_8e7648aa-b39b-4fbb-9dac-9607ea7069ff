package com.pay.tp.core.configuration;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.SocketAddress;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021-05-27 10:14:25
 */
@Configuration
@Slf4j
public class RestTemplateConfig {

    @Value("${proxy.squid.flag:}")
    private String flag;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:}")
    private Integer proxyPort;

    @Value("${httpClient.net.maxPoolNum}")
    private int maxPoolNum;
    @Value("${httpClient.net.socketTimeout}")
    private int socketTimeout;
    @Value("${httpClient.net.connectTimeout}")
    private int connectTimeout;
    @Value("${httpClient.net.connectionRequestTimeout}")
    private int connectionRequestTimeout;


    @Bean
    public RestTemplate restTemplate(@Qualifier("simpleClientHttpRequestFactory") ClientHttpRequestFactory factory) {
        RestTemplate restTemplate = new RestTemplate(factory);
        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        for (HttpMessageConverter c : messageConverters) {
            if (c instanceof StringHttpMessageConverter) {
                ((StringHttpMessageConverter) c).setDefaultCharset(Charset.forName("utf-8"));
            }
        }

        return restTemplate;
    }


    /**
     * 用友推送数据接口
     *
     * @return
     */
    @Bean("yyProxyRestTemplate")
    public RestTemplate yyProxyRestTemplate() {
        RestTemplate restTemplate;
        if (!StringUtils.isEmpty(proxyHost) && flag.equals("Y")) {
            restTemplate = new RestTemplate(proxyClientHttpRequestFactory());
        } else {
            restTemplate = new RestTemplate(clientHttpRequestFactory());
        }
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

    /**
     * 号码检测API客户端
     *
     * @return
     */
    @Bean("pcProxyRestTemplate")
    public RestTemplate pcProxyRestTemplate() {
        RestTemplate restTemplate;
        if (!StringUtils.isEmpty(proxyHost) && flag.equals("Y")) {
            restTemplate = new RestTemplate(proxyClientHttpRequestFactory());
        } else {
            restTemplate = new RestTemplate(clientHttpRequestFactory());
        }
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

    private ClientHttpRequestFactory clientHttpRequestFactory() {
        CloseableHttpClient httpClient = httpClientBuilder().build();
        return new HttpComponentsClientHttpRequestFactory(httpClient);
    }


    private ClientHttpRequestFactory proxyClientHttpRequestFactory() {
        HttpHost proxyHttpHost = new HttpHost(proxyHost, proxyPort);
        CloseableHttpClient httpClient = httpClientBuilder()
                .setProxy(proxyHttpHost)
                .build();
        return new HttpComponentsClientHttpRequestFactory(httpClient);
    }

    @Bean("simpleClientHttpRequestFactory")
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setReadTimeout(socketTimeout);
        requestFactory.setConnectTimeout(connectTimeout);
        requestFactory.setOutputStreaming(false);
        if (!StringUtils.isEmpty(proxyHost)) {
            requestFactory.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort)));
        }
        return requestFactory;
    }


    @SneakyThrows
    private HttpClientBuilder httpClientBuilder() {
        PlainConnectionSocketFactory plainConnectionSocketFactory = PlainConnectionSocketFactory.getSocketFactory();

        SSLContext sslContext = SSLContextBuilder.create()
                .loadTrustMaterial((x509Certificates, s) -> true).build();

        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(
                sslContext,
                new String[]{"SSLv3", "TLSv1", "TLSv1.2"},
                null,
                NoopHostnameVerifier.INSTANCE);

        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", plainConnectionSocketFactory)
                .register("https", sslConnectionSocketFactory)
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        // 设置整个连接池最大连接数 根据自己的场景决定
        connectionManager.setMaxTotal(maxPoolNum);
        // 路由是对maxTotal的细分
        connectionManager.setDefaultMaxPerRoute(100);
        connectionManager.setValidateAfterInactivity(10 * 1000);
        connectionManager.closeIdleConnections(50, TimeUnit.SECONDS);

        RequestConfig requestConfig = RequestConfig.custom()
                // 服务器返回数据(response)的时间，超过该时间抛出read timeout
                .setSocketTimeout(socketTimeout)
                // 连接上服务器(握手成功)的时间，超出该时间抛出connect timeout
                .setConnectTimeout(connectTimeout)
                // 从连接池中获取连接的超时时间，超过该时间未拿到可用连接，
                // 会抛出org.apache.http.conn.ConnectionPoolTimeoutException: Timeout waiting for connection from pool
                .setConnectionRequestTimeout(connectionRequestTimeout)
                .build();
        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager);
    }

}
