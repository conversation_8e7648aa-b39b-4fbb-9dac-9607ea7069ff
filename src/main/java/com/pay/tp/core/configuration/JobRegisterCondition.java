//package com.pay.tp.core.configuration;
//
//import org.springframework.context.annotation.Condition;
//import org.springframework.context.annotation.ConditionContext;
//import org.springframework.core.type.AnnotatedTypeMetadata;
//
///**
// * <AUTHOR> z<PERSON><PERSON><PERSON>
// * @Package com.pay.duiyu.common
// * @Description: TODO
// * @date Date : 2019年12月23日 09:39
// */
//public class JobRegisterCondition implements Condition {
//
//    private String jobRegisterKey = "common.job.register";
//
//    @Override
//    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
//        Boolean jobRegisterable = context.getEnvironment().getProperty(jobRegisterKey, Boolean.class);
//        return jobRegisterable == null ? true : jobRegisterable;
//    }
//}
