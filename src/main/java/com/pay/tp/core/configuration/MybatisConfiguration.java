package com.pay.tp.core.configuration;

import com.github.pagehelper.PageHelper;
import com.pay.frame.common.base.plugins.SensitiveTypeHandler;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;


@MapperScan(value = "com.pay.tp.core.mapper")
@Configuration
public class MybatisConfiguration {

    @Value("${crypto.sensitive.sm4}")
    private String sm4Key;


    private SensitiveTypeHandler sensitiveTypeHandler() {
        SensitiveTypeHandler typeHandler = new SensitiveTypeHandler(sm4Key);
        return typeHandler;
    }


    /**
     * @return
     * @Description 配置 mybatis 全局配置
     */
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return new ConfigurationCustomizer() {
            @Override
            public void customize(org.apache.ibatis.session.Configuration configuration) {
                // 开启驼峰命名
                configuration.setMapUnderscoreToCamelCase(true);
                // 配置TypeHandler 加解密
                TypeHandlerRegistry registry = configuration.getTypeHandlerRegistry();
                SensitiveTypeHandler typeHandler = sensitiveTypeHandler();
                registry.register(typeHandler);
            }
        };
    }

    /**
     * @Description 分页插件
     * @return
     */
    @Bean
    public PageHelper pageHelper() {
        PageHelper pageHelper = new PageHelper();
        Properties p = new Properties();
        // 设置为true时，会将RowBounds第一个参数offset当成pageNum页码使用，和startPage中的pageNum效果一样
        p.setProperty("offsetAsPageNum", "true");
        // 设置为true时，使用RowBounds分页会进行count查询
        p.setProperty("rowBoundsWithCount", "true");
        // 分页参数合理化，默认false禁用。启用合理化时，如果pageNum<1会查询第一页，如果pageNum>pages会查询最后一页，禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据
        p.setProperty("reasonable", "false");
        pageHelper.setProperties(p);
        return pageHelper;
    }

}
