package com.pay.tp.core.configuration;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @version 创建时间：2018年12月19日 下午11:09:17
 * @ClassName
 * @Description
 */
@MapperScan(basePackages = {"com.pay.tp.core.mapper"})
@EnableEurekaClient
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.pay.tp.core.remote"})
@ComponentScan(basePackages = {"com.pay"})
public class Application {

    /**
     * @param args
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月19日 下午11:13:41
     */
    public static void main(String[] args) throws Exception {
        SpringApplication.run(Application.class, args);
    }

}