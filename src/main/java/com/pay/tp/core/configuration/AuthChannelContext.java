package com.pay.tp.core.configuration;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.pay.tp.core.enums.ChannelCode;
import com.pay.tp.core.remote.auth.BankCardAuthClient;

@Configuration
public class AuthChannelContext {

	private Map<String, BankCardAuthClient> bankCardAuthMap = new HashMap<>();

	/** 掌讯 */
	@Autowired
	private BankCardAuthClient zxAuthClient;
	@Autowired
	private BankCardAuthClient ldysAuthClient;

	/** 吉信 */
	@Autowired
	private BankCardAuthClient jxAuthClient;

	/** 信联（新国都） */
	@Autowired
	private BankCardAuthClient xlBjldAuthClient;
	@Autowired
	private BankCardAuthClient xlJilongAuthClient;

	/** 挡板 */
	@Autowired
	private BankCardAuthClient mockAuthClient;

	@PostConstruct
	public void init() {
		bankCardAuthMap.put(ChannelCode.LDYS.name(), ldysAuthClient);
		bankCardAuthMap.put(ChannelCode.ZX.name(), zxAuthClient);
		bankCardAuthMap.put(ChannelCode.JX.name(), jxAuthClient);
		bankCardAuthMap.put(ChannelCode.XL_BJLD.name(), xlBjldAuthClient);//uk
		bankCardAuthMap.put(ChannelCode.XL_JILONG.name(), xlJilongAuthClient);//plus
		bankCardAuthMap.put(ChannelCode.MOCK.name(), mockAuthClient);
	}

	public BankCardAuthClient get(String code) {
		return bankCardAuthMap.get(code);
	}
}
