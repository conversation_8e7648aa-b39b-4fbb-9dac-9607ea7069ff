package com.pay.tp.core.remote.sms;

import com.alibaba.fastjson.JSON;
import com.pay.tp.core.utils.JsonUtil;
import lombok.Builder;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @package com.pay.tp.core.remote.sms
 * @dscription
 * @date 2024-03-20 17:42
 */
@Component
public class WorkWechatMsgClient {


	private final Logger log = LoggerFactory.getLogger(getClass());

	@Value("${workWechat.api.robotSend}")
	private String robotSend;

	@Autowired
	private RestTemplate restTemplate;

	private static final String TEXT="text";
	private static final String MARKDOWN="markdown";


	@Data
	@Builder
	static class WorkWechatMsg {

		public static String buildWorkWechatMsg(String msgtype,List<String> mobileList,String content){
			if (MARKDOWN.equals(msgtype)){
				return JSON.toJSONString(WorkWechatMarkdownMsg.builder().msgtype(msgtype).markdown(Markdown.builder().content(content).build()).build());
			} else if (TEXT.equals(msgtype)) {
				return JSON.toJSONString(WorkWechatTextMsg.builder().msgtype(msgtype).text(Text.builder().content(content).mentioned_mobile_list(mobileList).build()).build());
			}else{
				throw new RuntimeException("不支持的消息类型");
			}
		}

	}

	@Data
	@Builder
	static class WorkWechatMarkdownMsg{
		private String msgtype;
		private Markdown markdown;
	}

	@Data
	@Builder
	static class Markdown{
		private String content;
	}

	@Data
	@Builder
	static class Text{
		private String content;
		private List<String> mentioned_mobile_list;
	}

	@Data
	@Builder
	static class WorkWechatTextMsg{
		private String msgtype;
		private Text text;
	}

	public Map<String, Object> robotSendMsg(String token,List<String> atMobiles, String msg,String msgType) {
		String robotSendUrl = String.format(robotSend
						+ "?key=%s"
				, token);

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

		String workWechatMsg = WorkWechatMsg.buildWorkWechatMsg(msgType, atMobiles, msg);

		HttpEntity<String> httpEntity = new HttpEntity(workWechatMsg, headers);

		log.info("work wechat robot send msg url: {}, params:{}", robotSend, JsonUtil.toJson(httpEntity));

		Map<String,Object> sendResult = restTemplate.postForObject(robotSendUrl, httpEntity, Map.class);
		log.info("work wechat robot send msg url: {}, req body: {}, result: {}",
				robotSendUrl, httpEntity, JsonUtil.toJson(sendResult));

		return sendResult;
	}

}
