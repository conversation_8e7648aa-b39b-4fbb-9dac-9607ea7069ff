package com.pay.tp.core.remote;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.remote.bean.JlQuerySlResultReq;
import com.pay.tp.core.remote.bean.JlQuerySlResultRsp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = "brand-channel-core")
@RequestMapping(value = "/brand-channel-core/channel")
public interface OriginChannelClient {

	@PostMapping(value = "/pos/querySlResultToJl")
	ResultsBean<JlQuerySlResultRsp> querySlResultToJl(@RequestBody JlQuerySlResultReq dto);
}
