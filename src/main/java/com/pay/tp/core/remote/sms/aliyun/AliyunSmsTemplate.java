package com.pay.tp.core.remote.sms.aliyun;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
public class AliyunSmsTemplate {

    private String code;                  // 模板编号
    private String template;              // 模板内容


    /**
     * 格式化模板内容
     *
     * @param params 参数值映射
     * @return 格式化后的内容
     */
    public String format(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return template;
        }

        String result = template;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            result = result.replace("${" + entry.getKey() + "}", entry.getValue());
        }
        return result;
    }

    /**
     * 从模板中提取所有参数名
     *
     * @return 参数名集合
     */
    public List<String> extractParamNames() {
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
        Matcher matcher = pattern.matcher(template);
        List<String> params = new ArrayList<>();

        while (matcher.find()) {
            params.add(matcher.group(1));
        }

        return params;
    }

    /**
     * 构建参数占位符到正则表达式捕获组的映射
     *
     * @return 正则表达式模式
     */
    public String buildRegexPattern() {
        return template.replaceAll("\\$\\{([^}]+)}", "(.+?)");
    }
}
