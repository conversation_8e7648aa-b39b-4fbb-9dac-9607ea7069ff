package com.pay.tp.core.remote.sms.aliyun;

import com.pay.frame.common.base.exception.ServerException;
import com.pay.tp.core.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class AliyunSmsTemplateUtil {

    private final Map<String, AliyunSmsTemplate> templateCache = new ConcurrentHashMap<>();

    @Autowired
    private AliyunSmsTemplateConfig aliyunSmsTemplateConfig;

    @PostConstruct
    public void init() {
        List<AliyunSmsTemplate> templates = aliyunSmsTemplateConfig.getTemplates();
        if (templates != null) {
            for (AliyunSmsTemplate template : templates) {
                templateCache.put(template.getCode(), template);
            }
        }
    }


    /**
     * 根据模板编号获取模板
     *
     * @param code 模板编号
     * @return 短信模板
     */
    public AliyunSmsTemplate getTemplate(String code) {
        return templateCache.get(code);
    }


    /**
     * 格式化消息内容
     *
     * @param code   模板编号
     * @param params 参数值映射
     * @return 格式化后的内容，如果模板不存在返回null
     */
    public String format(String code, Map<String, String> params) {
        AliyunSmsTemplate template = getTemplate(code);
        if (template == null) {
            return null;
        }

        // 检查必要的参数是否都提供了
        List<String> requiredParams = template.extractParamNames();
        if (!params.keySet().containsAll(requiredParams)) {
            throw new IllegalArgumentException("Missing required parameters: " +
                    new HashSet<>(requiredParams).removeAll(params.keySet()));
        }

        return template.format(params);
    }


    /**
     * 根据填充后的内容反向解析模板
     *
     * @param filledContent 填充后的内容
     * @return 解析结果，包含匹配的模板和提取的参数值
     */
    public AliyunSmsTemplateReverse parseTemplate(String filledContent) {
        if (filledContent == null || filledContent.trim().isEmpty()) {
            return null;
        }

        for (AliyunSmsTemplate template : aliyunSmsTemplateConfig.getTemplates()) {
            String regex = template.buildRegexPattern();
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(filledContent);
//            log.info("pattern matcher pattern: [{}], filledContent: [{}]", pattern, filledContent);
            if (matcher.matches()) {
                // 提取参数名和对应的值
                Map<String, String> params = new HashMap<>();
                List<String> paramNames = template.extractParamNames();

                for (int i = 0; i < paramNames.size(); i++) {
                    params.put(paramNames.get(i), matcher.group(i + 1));
                }

                return new AliyunSmsTemplateReverse(template.getCode(), template.getTemplate(), JsonUtil.toJson(params));
            }
        }
        throw new ServerException("缺少对应的模板：" + filledContent);
    }

}
