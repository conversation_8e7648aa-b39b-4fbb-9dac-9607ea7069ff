package com.pay.tp.core.remote.auth.xinlian;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Properties;

import javax.crypto.Cipher;

import org.apache.commons.codec.binary.Base64;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import sun.misc.BASE64Decoder;

/**
 * RSAHelper - 对RSA 签名&验签/分段加密&分段解密 的包装 签名算法: "SHA1withRSA", 私钥进行签名;
 * 公钥进行验签. 加密算法: "RSA/ECB/PKCS1Padding", 公钥进行加密;
 * 私钥进行解密.
 *
 * [localPrivKey]是自己的私钥, 自己的公钥给通信对方. [peerPubKey]是对方的公钥, 对方的私钥在对方那边.
 * 为了方便, 这里假定双方的密钥长度一致, 签名和加密的规则也一致.
 *
 * 以`Base64Str`结尾的参数表示内容是Base64编码的字符串, 其他情况都是raw字符串.
 */

/**
 * <AUTHOR>
 * @version 创建时间：2018年12月19日 下午11:22:43
 * @ClassName
 * @Description
 */
public class RSAHelper {
    // /**
    // * 合作方自己的私钥
    // */
    // public static final String privKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDiza6lqV92iEmx"
    // + "GEyFjJXiHoKlxfCl+/yyi4MD22r6mJvDscIA5FufYicASO96GB28dBDvcpigxyPs"
    // + "vBQVpwZHv0PXi896S1clnwKFxVVyl2JlzI44p3ZPE+PgZcCLsfxoclKJw7ppfVPO"
    // + "tnmRybct39L0STQJO4MjqGfGoVSP7i2hi/H7ZBrjTmAcEldJ8IHYaS8ashPy8f3C"
    // + "mWlGaqyikQ05V6VpUi4NW1BidKjSvXPXwMFwUaIGywkuh8xW3Oi12GS2R7dwIb5w"
    // + "25PKGcjtE47M7afgpFaM4xVllbAGtjfenCwpFHfFa7yx9so8jA/iRpDf6UYPSkbZ"
    // + "+TWE+lJhAgMBAAECggEAFEh6tvwHuo0Dsh/PMB5bhSZfXr3uAJohhkItzFmCHrkp"
    // + "LP4nsHa7ruxTOpZLPGsNtb3XieKAvdgxYUmMrkcKq73yLkOloXU9bPLkgdwdASuC"
    // + "tEHv8icf0ICh336aEqQvQ5P9x65GbIq1xQXSp3QXurWKoygszCqTVswHw97HtjtW"
    // + "kmtoZsIT6cLsF/A9ZF9Ao2qFQ3DANmr25JPZTvcds2wj36Uaa4e5o6CXNrOqLu1G"
    // + "7BLANE9nUGCn0x0wd0x9u2Zw/yl+3o4uBc9EuW4qp5mF5BTNFS49ZTvavW9Jlncb"
    // + "AIVLqOI5xfXyrrUhZGKGGlPdEI0dnNp3mSIYpUOzcQKBgQD/w0PRy18qsAyPqACo"
    // + "qC9Gy5d+8vmEg/f7AEDAscesfkyHKaxSMue9p1JowqY2WdG/KiYxMyhEa8WBouDM"
    // + "CF4o3zdmo1TwuuhuunvYajcgccMroHTgmsWNzuyecSzRiGfps+gdVOJ6YRuqIJiQ"
    // + "NLHWBxwv1iepp0NH7HjPMZw2BQKBgQDjA4pZqUswELbsPTG6T5xowJfT+SiywxgB"
    // + "HpAioGlQ1gvcEtvZXYugmrN5+nHaiFGzqIH11SLq6kysSgvqwHRPEKuLnB8FbZbC"
    // + "G2PFTYBgrzJFfVWQ+zWiuJRR8kSomtdkVAKgtRnk7HiUAKAiHbIFOt3heobEz3Rk"
    // + "D6C7Q8JdrQKBgH2QsQgbr2I2wkP4+DHVODiqlXr28PdVDvcEvcWcwmn2K74kAHzu"
    // + "jwV2UygpgA6o9CfFGrEG65sDyhiGDZU9+nRYekuCnp39NUW/ejPamavtDiOqCBeJ"
    // + "BLpFP7fd2mIYdOOwtqFH3lS0vi89B4msxS5NmVIG8rwA6TAzcXBPa+C9AoGAJC4z"
    // + "RZj6t71iOgKCw2vexL81M355Yww+7ia92Bby0gRbPYbv7RPApichxaYJsUeapeSM"
    // + "We7PMtuGvsrKXW6w2s0QWh7Wvtm5dlRBMXfppv8lJvgTxBiVcsqyMOFI2gpbm8zb"
    // + "4lsatmaNzSDQZL+Q2M6KAF6zzfg2V6A6AL6K4r0CgYEA7EPu0Y1BMDLoFIN+Y44G"
    // + "PJqS5ti3xSd66ukupaKp2NHjjCbfZTq2nkCzx0965jEA51bbxaAOy3CK12mtIueN"
    // + "NCYV3XSFIvAWMB8whUva4JoixXUFPnc8HWj1FO5LEb5F6P6J1E57MdaH54C5bAID"
    // + "lDm7JpsIEgsiJrfNfFQ0t9E=";
    // /**
    // * 信联提供的公钥
    // */
    // public static final String pubKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4s2upalfdohJsRhMhYyV"
    // + "4h6CpcXwpfv8souDA9tq+pibw7HCAORbn2InAEjvehgdvHQQ73KYoMcj7LwUFacG"
    // + "R79D14vPektXJZ8ChcVVcpdiZcyOOKd2TxPj4GXAi7H8aHJSicO6aX1TzrZ5kcm3"
    // + "Ld/S9Ek0CTuDI6hnxqFUj+4toYvx+2Qa405gHBJXSfCB2GkvGrIT8vH9wplpRmqs"
    // + "opENOVelaVIuDVtQYnSo0r1z18DBcFGiBssJLofMVtzotdhktke3cCG+cNuTyhnI"
    // + "7ROOzO2n4KRWjOMVZZWwBrY33pwsKRR3xWu8sfbKPIwP4kaQ3+lGD0pG2fk1hPpS"
    // + "YQIDAQAB";
    public static final String KEY_ALGORITHM = "RSA";
    public static final String SIGNATURE_ALGORITHM = "SHA1withRSA";
    public static final String CIPHER_ALGORITHM = "RSA/ECB/PKCS1Padding"; // 加密block需要预留11字节
    public static final int KEYBIT = 2048;
    public static final int RESERVEBYTES = 11;
    public static final int BLOCK = 8;
    /**
     * 合作方自己的私钥
     */
    private static String privKey = null;
    /**
     * 信联提供的公钥
     */
    private static String pubKey = null;

    static {
        try {
            Properties prop = PropertiesLoaderUtils.loadAllProperties("initKey.properties");

            privKey = prop.getProperty("privKey").trim();
            pubKey = prop.getProperty("xlpubKey").trim();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private PrivateKey localPrivKey;
    private PublicKey peerPubKey;
    public RSAHelper() { }

    /**
     * 初始化自己的私钥,对方的公钥以及密钥长度.
     *  Base64编码的私钥,PKCS#8编码. (去掉pem文件中的头尾标识)
     *  Base64编码的公钥. (去掉pem文件中的头尾标识)
     * @param keysize 密钥长度, 一般2048
     */
    public void initKey(int keysize) {

        try {
            localPrivKey = getPrivateKey(privKey);
            peerPubKey = getPublicKey(pubKey);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 从文件中输入流中加载公钥
     *
     * @param in 公钥输入流
     * @return 返回公钥
     * @throws Exception 加载公钥时产生的异常
     */
    public RSAPublicKey getPublicKey(InputStream in) throws Exception {
        BufferedReader br = new BufferedReader(new InputStreamReader(in));
        try {
            String readLine = null;
            StringBuilder sb = new StringBuilder();
            while ((readLine = br.readLine()) != null) {
                if (readLine.charAt(0) == '-') {
                    continue;
                } else {
                    sb.append(readLine);
                    sb.append('\r');
                }
            }
            return getPublicKey(sb.toString());
        } catch (IOException e) {
            throw new Exception("公钥数据流读取错误");
        } catch (NullPointerException e) {
            throw new Exception("公钥输入流为空");
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
            } catch (Exception e) {
                throw new Exception("关闭输入缓存流出错");
            }

            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                throw new Exception("关闭输入流出错");
            }
        }
    }

    /**
     * 从字符串中加载公钥
     *
     * @param publicKeyStr 公钥数据字符串
     * @return 返回公钥
     * @throws Exception 加载公钥时产生的异常
     */
    public RSAPublicKey getPublicKey(String publicKeyStr) throws Exception {
        try {
            BASE64Decoder base64Decoder = new BASE64Decoder();
            byte[] buffer = base64Decoder.decodeBuffer(publicKeyStr);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
            RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);
            return publicKey;
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("公钥非法");
        } catch (IOException e) {
            throw new Exception("公钥数据内容读取错误");
        } catch (NullPointerException e) {
            throw new Exception("公钥数据为空");
        }
    }

    /**
     * 从文件中加载私钥
     * @param in 私钥流
     * @return 是否成功
     * @throws Exception 异常
     */
    public RSAPrivateKey getPrivateKey(InputStream in) throws Exception {
        BufferedReader br = new BufferedReader(new InputStreamReader(in));
        try {
            String readLine = null;
            StringBuilder sb = new StringBuilder();
            while ((readLine = br.readLine()) != null) {
                if (readLine.charAt(0) == '-') {
                    continue;
                } else {
                    sb.append(readLine);
                    sb.append('\r');
                }
            }
            return getPrivateKey(sb.toString());
        } catch (IOException e) {
            throw new Exception("私钥数据读取错误");
        } catch (NullPointerException e) {
            throw new Exception("私钥输入流为空");
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
            } catch (Exception e) {
                throw new Exception("关闭输入缓存流出错");
            }

            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                throw new Exception("关闭输入流出错");
            }
        }
    }

    /**
     * 从字符串中加载私钥
     *
     * @param privateKeyStr 公钥数据字符串
     * @return 返回私钥
     * @throws Exception 加载私钥时产生的异常
     */
    public RSAPrivateKey getPrivateKey(String privateKeyStr) throws Exception {
        try {
            BASE64Decoder base64Decoder = new BASE64Decoder();
            byte[] buffer = base64Decoder.decodeBuffer(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            RSAPrivateKey privateKey = (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
            return privateKey;
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("私钥非法");
        } catch (IOException e) {
            throw new Exception("私钥数据内容读取错误");
        } catch (NullPointerException e) {
            throw new Exception("私钥数据为空");
        }
    }

    /**
     * RAS加密
     *
     * @param plainBytes 参数
     * @param useBase64Code 加密算法
     * @param charset   参数
     * @return 结果
     * @throws Exception 异常
     */
    public byte[] encryptRSA(byte[] plainBytes, boolean useBase64Code, String charset) throws Exception {
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        int decryptBlock = KEYBIT / BLOCK; // 256 bytes
        int encryptBlock = decryptBlock - RESERVEBYTES; // 245 bytes
        // 计算分段加密的block数 (向上取整)
        int nBlock = (plainBytes.length / encryptBlock);
        if ((plainBytes.length % encryptBlock) != 0) { // 余数非0，block数再加1
            nBlock += 1;
        }
        // 输出buffer, 大小为nBlock个decryptBlock
        ByteArrayOutputStream outbuf = new ByteArrayOutputStream(nBlock * decryptBlock);
        cipher.init(Cipher.ENCRYPT_MODE, peerPubKey);
        // cryptedBase64Str =
        // Base64.encodeBase64String(cipher.doFinal(plaintext.getBytes()));
        // 分段加密
        for (int offset = 0; offset < plainBytes.length; offset += encryptBlock) {
            // block大小: encryptBlock 或 剩余字节数
            int inputLen = (plainBytes.length - offset);
            if (inputLen > encryptBlock) {
                inputLen = encryptBlock;
            }
            // 得到分段加密结果
            byte[] encryptedBlock = cipher.doFinal(plainBytes, offset, inputLen);
            // 追加结果到输出buffer中
            outbuf.write(encryptedBlock);
        }
        // 如果是Base64编码，则返回Base64编码后的数组
        if (useBase64Code) {
            return Base64.encodeBase64String(outbuf.toByteArray()).getBytes(charset);
        } else {
            return outbuf.toByteArray(); // ciphertext
        }
    }

    /**
     * RSA解密
     *
     * @param useBase64Code     加密算法
     *  @param charset          私钥
     * @param cryptedBytes 待解密信息
     * @return byte[]
     * @throws Exception    异常
     */
    public byte[] decryptRSA(byte[] cryptedBytes, boolean useBase64Code, String charset) throws Exception {
        byte[] data = null;

        // 如果是Base64编码的话，则要Base64解码
        if (useBase64Code) {
            data = Base64.decodeBase64(new String(cryptedBytes, charset));
        } else {
            data = cryptedBytes;
        }

        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        int decryptBlock = KEYBIT / BLOCK; // 256 bytes
        int encryptBlock = decryptBlock - RESERVEBYTES; // 245 bytes
        // 计算分段解密的block数 (理论上应该能整除)
        int nBlock = (data.length / decryptBlock);
        // 输出buffer, , 大小为nBlock个encryptBlock
        ByteArrayOutputStream outbuf = new ByteArrayOutputStream(nBlock * encryptBlock);
        cipher.init(Cipher.DECRYPT_MODE, localPrivKey);
        // plaintext = new
        // String(cipher.doFinal(Base64.decodeBase64(cryptedBase64Str)));
        // 分段解密
        for (int offset = 0; offset < data.length; offset += decryptBlock) {
            // block大小: decryptBlock 或 剩余字节数
            int inputLen = (data.length - offset);
            if (inputLen > decryptBlock) {
                inputLen = decryptBlock;
            }

            // 得到分段解密结果
            byte[] decryptedBlock = cipher.doFinal(data, offset, inputLen);
            // 追加结果到输出buffer中
            outbuf.write(decryptedBlock);
        }
        outbuf.flush();
        outbuf.close();
        return outbuf.toByteArray();
    }

    /**
     * RSA签名
     *
     * @param useBase64Code     加密算法
     * @param charset            私钥
     * @param plainBytes 需要签名的信息
     * @return byte[]
     * @throws Exception    异常
     */
    public byte[] signRSA(byte[] plainBytes, boolean useBase64Code, String charset) throws Exception {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(localPrivKey);
        signature.update(plainBytes);

        // 如果是Base64编码的话，需要对签名后的数组以Base64编码
        if (useBase64Code) {
            return Base64.encodeBase64String(signature.sign()).getBytes(charset);
        } else {
            return signature.sign();
        }
    }

    /**
     * 验签操作
     *
     * @param useBase64Code     加密算法
     * @param charset            公钥
     * @param plainBytes    需要验签的信息
     * @param signBytes 签名信息
     * @return boolean
     * @exception Exception     异常
     */
    public boolean verifyRSA(byte[] plainBytes, byte[] signBytes, boolean useBase64Code, String charset)
            throws Exception {
        boolean isValid = false;
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(peerPubKey);
        signature.update(plainBytes);

        // 如果是Base64编码的话，需要对验签的数组以Base64解码
        if (useBase64Code) {
            isValid = signature.verify(Base64.decodeBase64(new String(signBytes, charset)));
        } else {
            isValid = signature.verify(signBytes);
        }
        return isValid;
    }

}
