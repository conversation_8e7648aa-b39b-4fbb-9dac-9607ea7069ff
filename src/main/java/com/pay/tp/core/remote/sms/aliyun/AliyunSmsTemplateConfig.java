package com.pay.tp.core.remote.sms.aliyun;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@Configuration
@ConfigurationProperties(prefix = "sms.aliyun")
public class AliyunSmsTemplateConfig {
    private List<AliyunSmsTemplate> templates;
}