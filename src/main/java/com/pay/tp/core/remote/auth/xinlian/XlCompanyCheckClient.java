package com.pay.tp.core.remote.auth.xinlian;

import java.util.Map;

import com.pay.tp.core.entity.auth.CompanyCheck;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.remote.xinlian
 * @Description: TODO
 * @date Date : 2019年01月11日 18:38
 */
public interface XlCompanyCheckClient {

    public Map<String,Object> parse(CompanyCheck companyCheck);

    public Map<String,Object> request(String serialNo, String entName, String regNo, String frName, String cidNo) throws Exception;
}
