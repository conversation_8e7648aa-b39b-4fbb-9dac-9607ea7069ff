package com.pay.tp.core.remote.auth.jixin;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.pay.tp.core.exception.AuthRemoteException;
import com.pay.tp.core.remote.auth.BankCardAuthClient;
import com.pay.tp.core.beans.auth.BankCardRes;
import com.pay.tp.core.service.auth.JxCloudService;

/**
 * @Description: 吉信鉴权
 * @see: DefaultJXBankCardAuthClient 此处填写需要参考的类
 * @version Nov 20, 2019 3:05:32 PM 
 * <AUTHOR>
 */
@Service("jxAuthClient")
//@Profile({"proc"})
public class DefaultJXBankCardAuthClient implements BankCardAuthClient {
    private final Logger logger = LoggerFactory.getLogger(DefaultJXBankCardAuthClient.class);

    /** 地址 */
    @Value("${custom.jixin.bankcard.auth.url:}")
    private String url;
    
    /** 商户号 */
    @Value("${custom.jixin.bankcard.auth.merchno:}")
    private String merchno;
    
    /** 吉信密钥 */
    @Value("${own.jixin.merchkey}")
    public String merchkey;
    
    
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

   
    @Autowired
    private JxCloudService jxCloudService;
    

    /**
	 * @Description 认证请求
	 * @param outTradeNo   请求号
	 * @param cardNo       卡号
	 * @param name         名称
	 * @param cidNo        身份证号
	 * @param mobile       手机号
	 * @param customerName 认证人
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
    @Override
    public BankCardRes request(String outTradeNo, String cardNo, String name, String cidNo, String mobile, String customerName,String brand)
            throws Exception {
        logger.info("method =request JX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}",
        		outTradeNo, cardNo, name, cidNo, mobile, customerName);
        
        AESUtils aesUtils = new AESUtils(merchkey.getBytes(), merchkey.length());
		// 商户订单号 保证唯一
		Map<String, Object> reqMap = new HashMap<>();
		// 接口编码 不同业务各不相同 具体见接口文档
		reqMap.put("transcode", "106");
		// 版本号 固定0100
		reqMap.put("version", "0100");
		// 商户号
		reqMap.put("merchno", merchno);
		// 商户流水号
		reqMap.put("ordersn", outTradeNo);
		// 商户订单号
		reqMap.put("dsorderid", outTradeNo);
		// 姓名 AES加密
		reqMap.put("username", aesUtils.encrypt(name));
		// 证件号 AES加密
		reqMap.put("idcard", aesUtils.encrypt(cidNo));//certNo
		// 银行卡号 AES加密
		reqMap.put("bankcard", aesUtils.encrypt(cardNo));
		// 手机号 AES加密
		//reqMap.put("mobile", aesUtils.encrypt(phone));
		// 证件类型 枚举见文档
		reqMap.put("idtype", "01");
		// 真实场景 见接口文档枚举 必传
		reqMap.put("sceneCode", "99");
		// 真实终端商户名称 由于银联监管 必传
		reqMap.put("sCustomerName", customerName);
		// 加签 MD5
		String sign = SignUtil.getSign(reqMap, merchkey);
		reqMap.put("sign", sign);
		logger.info("method =request JX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 请求报文 = {}",
				outTradeNo, cardNo, name, cidNo, mobile, customerName, reqMap);
        
        
        ObjectMapper mapper = new ObjectMapper();
        String result = null;
        try {
    		byte[] request = mapper.writeValueAsBytes(reqMap);
    		// 发送
    		result = CommonUtil.post(url, proxyHost, proxyPort, request);
    		logger.info("method =request JX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 返回结果 = {}",
    				outTradeNo, cardNo, name, cidNo, mobile, customerName, result);
    		
    		if(StringUtils.isBlank(result)) {
    			logger.info("method =request JX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 无返回结果",
    					outTradeNo, cardNo, name, cidNo, mobile, customerName);
                throw new AuthRemoteException("无返回结果");
            
    		}
        } catch (Exception e) {
            logger.error("鉴权请求异常", e);
            throw new AuthRemoteException(e.getMessage());
        }
        
        
        /** 验签 */
        Gson gson = new Gson();
        try {
        	Map<String, Object> resMap = mapper.readValue(result, Map.class);
     		sign = SignUtil.getSign(resMap, merchkey);
     		if (resMap.get("sign").equals(sign)) {
     			logger.info("method =request JX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 验签成功",
     					outTradeNo, cardNo, name, cidNo, mobile, customerName);
     		} else {
     			logger.info("method =request JX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 验签失败",
     					outTradeNo, cardNo, name, cidNo, mobile, customerName);
     		}
		} catch (Exception e) {
			logger.info("method =request JX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 验签异常, e = {}",
        			outTradeNo, cardNo, name, cidNo, mobile, customerName, e.getMessage(), e);
        }

        return jxCloudService.convertJX(gson.fromJson(result, JXCloudResp.class));
    }
    	
}


