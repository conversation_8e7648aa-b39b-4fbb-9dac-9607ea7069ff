package com.pay.tp.core.remote.phonecheck;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.phonecheck.PhoneCheckResp;
import com.pay.tp.core.utils.JsonUtil;
import com.pay.tp.core.utils.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 号码检测API客户端
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Component
public class PhoneCheckClient {

    @Autowired
    private RestTemplate pcProxyRestTemplate;

    @Value("${phonecheck.api.baseUrl:}")
    private String baseUrl;

    @Value("${phonecheck.api.merchantId:}")
    private String merchantId;

    @Value("${phonecheck.api.appSecret:}")
    private String appSecret;

    @Value("${phonecheck.api.emptyNumberUrl:/api/phone/empty}")
    private String emptyNumberUrl;

    @Value("${phonecheck.api.realNameUrl:/api/phone/realname}")
    private String realNameUrl;

    @Value("${phonecheck.api.riskControlUrl:/api/phone/risk}")
    private String riskControlUrl;

    @Value("${phonecheck.api.timeout:30000}")
    private int timeout;

    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 运营商号段映射
    private static final Map<String, String> CARRIER_MAP = new HashMap<>();
    static {
        // 中国移动
        CARRIER_MAP.put("134", "CMCC");
        CARRIER_MAP.put("135", "CMCC");
        CARRIER_MAP.put("136", "CMCC");
        CARRIER_MAP.put("137", "CMCC");
        CARRIER_MAP.put("138", "CMCC");
        CARRIER_MAP.put("139", "CMCC");
        CARRIER_MAP.put("147", "CMCC");
        CARRIER_MAP.put("150", "CMCC");
        CARRIER_MAP.put("151", "CMCC");
        CARRIER_MAP.put("152", "CMCC");
        CARRIER_MAP.put("157", "CMCC");
        CARRIER_MAP.put("158", "CMCC");
        CARRIER_MAP.put("159", "CMCC");
        CARRIER_MAP.put("178", "CMCC");
        CARRIER_MAP.put("182", "CMCC");
        CARRIER_MAP.put("183", "CMCC");
        CARRIER_MAP.put("184", "CMCC");
        CARRIER_MAP.put("187", "CMCC");
        CARRIER_MAP.put("188", "CMCC");
        CARRIER_MAP.put("198", "CMCC");

        // 中国联通
        CARRIER_MAP.put("130", "CUCC");
        CARRIER_MAP.put("131", "CUCC");
        CARRIER_MAP.put("132", "CUCC");
        CARRIER_MAP.put("145", "CUCC");
        CARRIER_MAP.put("155", "CUCC");
        CARRIER_MAP.put("156", "CUCC");
        CARRIER_MAP.put("166", "CUCC");
        CARRIER_MAP.put("175", "CUCC");
        CARRIER_MAP.put("176", "CUCC");
        CARRIER_MAP.put("185", "CUCC");
        CARRIER_MAP.put("186", "CUCC");

        // 中国电信
        CARRIER_MAP.put("133", "CTCC");
        CARRIER_MAP.put("149", "CTCC");
        CARRIER_MAP.put("153", "CTCC");
        CARRIER_MAP.put("173", "CTCC");
        CARRIER_MAP.put("177", "CTCC");
        CARRIER_MAP.put("180", "CTCC");
        CARRIER_MAP.put("181", "CTCC");
        CARRIER_MAP.put("189", "CTCC");
        CARRIER_MAP.put("199", "CTCC");
    }

    /**
     * 空号检测
     * @param phoneNumbers 手机号列表
     * @return 检测结果
     */
    public List<PhoneCheckResp.PhoneCheckResult> checkEmptyNumber(List<String> phoneNumbers) {
        log.info("开始空号检测，手机号数量: {}", phoneNumbers.size());

        List<PhoneCheckResp.PhoneCheckResult> results = new ArrayList<>();

        // 如果配置了真实的API，调用真实API
        if (StringUtils.isNotBlank(baseUrl) && StringUtils.isNotBlank(merchantId)) {
            try {
                results = callRealApi(phoneNumbers, "EMPTY_NUMBER");
            } catch (Exception e) {
                log.error("调用真实空号检测API异常", e);
                // 降级到模拟检测
                results = mockEmptyNumberCheck(phoneNumbers);
            }
        } else {
            // 模拟检测
            results = mockEmptyNumberCheck(phoneNumbers);
        }

        log.info("空号检测完成，结果数量: {}", results.size());
        return results;
    }

    /**
     * 实名检测
     * @param phoneNumbers 手机号列表
     * @return 检测结果
     */
    public List<PhoneCheckResp.PhoneCheckResult> checkRealName(List<String> phoneNumbers) {
        log.info("开始实名检测，手机号数量: {}", phoneNumbers.size());

        List<PhoneCheckResp.PhoneCheckResult> results = new ArrayList<>();

        // 如果配置了真实的API，调用真实API
        if (StringUtils.isNotBlank(baseUrl) && StringUtils.isNotBlank(merchantId)) {
            try {
                results = callRealApi(phoneNumbers, "REAL_NAME");
            } catch (Exception e) {
                log.error("调用真实实名检测API异常", e);
                // 降级到模拟检测
                results = mockRealNameCheck(phoneNumbers);
            }
        } else {
            // 模拟检测
            results = mockRealNameCheck(phoneNumbers);
        }

        log.info("实名检测完成，结果数量: {}", results.size());
        return results;
    }

    /**
     * 风控检测
     * @param phoneNumbers 手机号列表
     * @return 检测结果
     */
    public List<PhoneCheckResp.PhoneCheckResult> checkRiskControl(List<String> phoneNumbers) {
        log.info("开始风控检测，手机号数量: {}", phoneNumbers.size());

        List<PhoneCheckResp.PhoneCheckResult> results = new ArrayList<>();

        // 如果配置了真实的API，调用真实API
        if (StringUtils.isNotBlank(baseUrl) && StringUtils.isNotBlank(merchantId)) {
            try {
                results = callRealApi(phoneNumbers, "RISK_CONTROL");
            } catch (Exception e) {
                log.error("调用真实风控检测API异常", e);
                // 降级到模拟检测
                results = mockRiskControlCheck(phoneNumbers);
            }
        } else {
            // 模拟检测
            results = mockRiskControlCheck(phoneNumbers);
        }

        log.info("风控检测完成，结果数量: {}", results.size());
        return results;
    }

    /**
     * 调用真实API
     * @param phoneNumbers 手机号列表
     * @param checkType 检测类型
     * @return 检测结果
     */
    private List<PhoneCheckResp.PhoneCheckResult> callRealApi(List<String> phoneNumbers, String checkType) {
        String url = baseUrl;
        switch (checkType) {
            case "EMPTY_NUMBER":
                url += emptyNumberUrl;
                break;
            case "REAL_NAME":
                url += realNameUrl;
                break;
            case "RISK_CONTROL":
                url += riskControlUrl;
                break;
            default:
                throw new IllegalArgumentException("不支持的检测类型: " + checkType);
        }

        // 构建请求参数
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("phoneNumbers", phoneNumbers);
        requestData.put("checkType", checkType);

        // 构建请求头
        HttpHeaders headers = buildHeaders(requestData);
        String requestBody = JsonUtil.toJson(requestData);
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);

        log.info("调用号码检测API，URL: {}, 请求体: {}", url, requestBody);

        // 发送请求
        String response = pcProxyRestTemplate.postForObject(url, httpEntity, String.class);
        log.info("号码检测API响应: {}", response);

        // 解析响应
        return parseApiResponse(response, phoneNumbers);
    }

    /**
     * 构建请求头
     * @param requestData 请求数据
     * @return HttpHeaders
     */
    private HttpHeaders buildHeaders(Map<String, Object> requestData) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 生成时间戳
        long timestamp = System.currentTimeMillis();

        // 计算签名
        String signStr = merchantId + JsonUtil.toJson(requestData) + timestamp + appSecret;
        String sign = Md5Util.encode(signStr);

        // 设置请求头
        headers.set("merchantId", merchantId);
        headers.set("requestTime", String.valueOf(timestamp));
        headers.set("sign", sign);

        return headers;
    }

    /**
     * 解析API响应
     * @param response 响应字符串
     * @param phoneNumbers 原始手机号列表
     * @return 检测结果
     */
    private List<PhoneCheckResp.PhoneCheckResult> parseApiResponse(String response, List<String> phoneNumbers) {
        List<PhoneCheckResp.PhoneCheckResult> results = new ArrayList<>();

        try {
            JSONObject jsonResponse = JSON.parseObject(response);
            // 这里根据实际API响应格式进行解析
            // 示例解析逻辑，需要根据实际API调整

            return results;
        } catch (Exception e) {
            log.error("解析API响应异常", e);
            // 返回失败结果
            for (String phoneNumber : phoneNumbers) {
                PhoneCheckResp.PhoneCheckResult result = new PhoneCheckResp.PhoneCheckResult();
                result.setPhoneNumber(phoneNumber);
                result.setStatus(PhoneCheckResp.PhoneStatus.FAIL);
                result.setStatusDesc("API解析异常");
                result.setErrorMsg(e.getMessage());
                results.add(result);
            }
            return results;
        }
    }

    /**
     * 模拟空号检测
     * @param phoneNumbers 手机号列表
     * @return 检测结果
     */
    private List<PhoneCheckResp.PhoneCheckResult> mockEmptyNumberCheck(List<String> phoneNumbers) {
        List<PhoneCheckResp.PhoneCheckResult> results = new ArrayList<>();

        for (String phoneNumber : phoneNumbers) {
            PhoneCheckResp.PhoneCheckResult result = new PhoneCheckResp.PhoneCheckResult();
            result.setPhoneNumber(phoneNumber);
            result.setCheckTime(new Date().toString());

            // 基本格式校验
            if (!PHONE_PATTERN.matcher(phoneNumber).matches()) {
                result.setStatus(PhoneCheckResp.PhoneStatus.FAIL);
                result.setStatusDesc("手机号格式错误");
                result.setErrorMsg("手机号格式不正确");
                results.add(result);
                continue;
            }

            // 模拟检测逻辑：根据手机号尾号判断
            String lastDigit = phoneNumber.substring(phoneNumber.length() - 1);
            if ("0".equals(lastDigit) || "4".equals(lastDigit)) {
                // 模拟空号
                result.setStatus(PhoneCheckResp.PhoneStatus.EMPTY);
                result.setStatusDesc("空号");
            } else {
                // 模拟正常号码
                result.setStatus(PhoneCheckResp.PhoneStatus.NORMAL);
                result.setStatusDesc("正常");
            }

            // 设置运营商和归属地信息
            setCarrierAndLocation(result, phoneNumber);

            results.add(result);
        }

        return results;
    }

    /**
     * 模拟实名检测
     * @param phoneNumbers 手机号列表
     * @return 检测结果
     */
    private List<PhoneCheckResp.PhoneCheckResult> mockRealNameCheck(List<String> phoneNumbers) {
        List<PhoneCheckResp.PhoneCheckResult> results = new ArrayList<>();

        for (String phoneNumber : phoneNumbers) {
            PhoneCheckResp.PhoneCheckResult result = new PhoneCheckResp.PhoneCheckResult();
            result.setPhoneNumber(phoneNumber);
            result.setCheckTime(new Date().toString());

            // 基本格式校验
            if (!PHONE_PATTERN.matcher(phoneNumber).matches()) {
                result.setStatus(PhoneCheckResp.PhoneStatus.FAIL);
                result.setStatusDesc("手机号格式错误");
                result.setErrorMsg("手机号格式不正确");
                results.add(result);
                continue;
            }

            result.setStatus(PhoneCheckResp.PhoneStatus.NORMAL);
            result.setStatusDesc("检测成功");

            // 模拟实名状态：根据手机号倒数第二位判断
            String secondLastDigit = phoneNumber.substring(phoneNumber.length() - 2, phoneNumber.length() - 1);
            if ("1".equals(secondLastDigit) || "3".equals(secondLastDigit) || "5".equals(secondLastDigit)) {
                result.setRealNameStatus(1); // 已实名
            } else if ("0".equals(secondLastDigit) || "2".equals(secondLastDigit)) {
                result.setRealNameStatus(0); // 未实名
            } else {
                result.setRealNameStatus(-1); // 无法确定
            }

            // 设置运营商和归属地信息
            setCarrierAndLocation(result, phoneNumber);

            results.add(result);
        }

        return results;
    }

    /**
     * 模拟风控检测
     * @param phoneNumbers 手机号列表
     * @return 检测结果
     */
    private List<PhoneCheckResp.PhoneCheckResult> mockRiskControlCheck(List<String> phoneNumbers) {
        List<PhoneCheckResp.PhoneCheckResult> results = new ArrayList<>();

        for (String phoneNumber : phoneNumbers) {
            PhoneCheckResp.PhoneCheckResult result = new PhoneCheckResp.PhoneCheckResult();
            result.setPhoneNumber(phoneNumber);
            result.setCheckTime(new Date().toString());

            // 基本格式校验
            if (!PHONE_PATTERN.matcher(phoneNumber).matches()) {
                result.setStatus(PhoneCheckResp.PhoneStatus.FAIL);
                result.setStatusDesc("手机号格式错误");
                result.setErrorMsg("手机号格式不正确");
                results.add(result);
                continue;
            }

            result.setStatus(PhoneCheckResp.PhoneStatus.NORMAL);
            result.setStatusDesc("检测成功");

            // 模拟风险等级：根据手机号中间数字判断
            String middleDigit = phoneNumber.substring(5, 6);
            if ("6".equals(middleDigit) || "8".equals(middleDigit)) {
                result.setRiskLevel(PhoneCheckResp.RiskLevel.HIGH);
            } else if ("4".equals(middleDigit) || "7".equals(middleDigit)) {
                result.setRiskLevel(PhoneCheckResp.RiskLevel.MEDIUM);
            } else {
                result.setRiskLevel(PhoneCheckResp.RiskLevel.LOW);
            }

            // 设置运营商和归属地信息
            setCarrierAndLocation(result, phoneNumber);

            results.add(result);
        }

        return results;
    }

    /**
     * 设置运营商和归属地信息
     * @param result 检测结果
     * @param phoneNumber 手机号
     */
    private void setCarrierAndLocation(PhoneCheckResp.PhoneCheckResult result, String phoneNumber) {
        // 根据号段判断运营商
        String prefix = phoneNumber.substring(0, 3);
        String carrier = CARRIER_MAP.get(prefix);
        result.setCarrier(carrier != null ? carrier : "UNKNOWN");

        // 设置号码类型
        result.setNumberType(1); // 手机号

        // 模拟归属地（根据手机号第4位数字）
        String fourthDigit = phoneNumber.substring(3, 4);
        switch (fourthDigit) {
            case "0":
            case "1":
                result.setProvince("北京");
                result.setCity("北京");
                break;
            case "2":
            case "3":
                result.setProvince("上海");
                result.setCity("上海");
                break;
            case "4":
            case "5":
                result.setProvince("广东");
                result.setCity("深圳");
                break;
            case "6":
            case "7":
                result.setProvince("浙江");
                result.setCity("杭州");
                break;
            default:
                result.setProvince("江苏");
                result.setCity("南京");
                break;
        }
    }
}
