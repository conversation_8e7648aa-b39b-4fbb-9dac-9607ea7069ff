package com.pay.tp.core.remote.auth.xinlian;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.remote.xinlian
 * @Description: TODO
 * @date Date : 2018年12月18日 14:51
 */
@Data
public class XinlianRes {
//	"cooperSerialNo":"22032915492995000320","remark":"","rspMsg":"交易成功","execType":"S","paySerialNo":"602203291549384169178500001579"
//	,"oriTransDate":"********","resv":"","bankName":"工商银行","validateStatus":"01","cardType":"1","rspCod":"000000"
	private long id;
    private String rspCod;//响应码
    private String rspMsg;//响应描述
    /**
	    卡类型：
	1-借记卡 
	2-信用卡 
	3-预付费卡 
	0-其他
	*/
    private String cardType;
    private String resv;//预留
    private String cooperSerialNo;//接入方交易请求流水号
    private String bankName;//开户行名称
    /** 
     * 认证结果： 
		01-认证一致  （收费）
		02-认证不一致（收费）
		03-认证不确定（不收费）
		04-认证失败  （不收费）
     */
    private String validateStatus;
    private String oriTransDate;//查询日期
    private String paySerialNo;
	private String remark;//补充说明
}
