package com.pay.tp.core.remote.sms;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.entity.sms.SmsRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调用LDYS的短信通道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LDYSSmsProxy implements SmsClient {


    @Autowired
    private LDYSSmsClient ldysSmsClient;

    @Override
    public Map<String, Object> request(String requestNo, String content, String phone) {

        LDYSSmsClient.SmsReq smsReq = new LDYSSmsClient.SmsReq();
        smsReq.setPhone(phone);
        smsReq.setContent(content);
        smsReq.setRequestNo(requestNo);
        ResultsBean<String> resultsBean = ldysSmsClient.sendSms(smsReq);
        HashMap<String, Object> map = new HashMap<>(2);
        if (resultsBean.success()) {
            map.put("result", 0);
        } else {
            map.put("result", -1);
            map.put("msg", resultsBean.getMessage());
        }
        return map;
    }


    @Override
    public Map<String, Object> parse(SmsMsg sms) {
        Map<String, Object> map = new HashMap<>(2);
        if ("0".equals(sms.getResult())) {
            map.put("code", 0);
        } else {
            map.put("code", -1);
            map.put("msg", sms.getMessage());
        }
        return map;
    }

    @Override
    public List<SmsMsg> parse(List<Map<String, String>> list) {

        //TODO 无回调
        return null;
    }
}
