package com.pay.tp.core.remote.position;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhangjian
 * @Package com.pay.position.core.client
 * @Description: TODO
 * @date Date : 2018年12月24日 13:42
 */
@Component
public class JuheClient {

    private final Logger logger = LoggerFactory.getLogger(JuheClient.class);
    @Value("${custom.juhe.url}")
    private String url;

    private String dateType = "json";
    @Value("${custom.juhe.connection.timeout:1000}")
    private int connectionTimeout = 1000;
    @Value("${custom.juhe.socket.timeout:1000}")
    private int socketTimeout = 1000;
    @Value("${custom.juhe.key}")
    private String key;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;


    public Map<String, Object> request(String requestNo, String mnc, String lac, String cell) throws IOException {
        logger.info("method = request, requestNo = {}, mnc = {}, lac ={}, cell = {}", requestNo, mnc, lac, cell);

        CloseableHttpClient client = null;
        try{
        	client = HttpClientBuilder.create().build();

            RequestConfig.Builder builder = RequestConfig.custom()
                .setConnectTimeout(connectionTimeout).setSocketTimeout(socketTimeout);

	        if(!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)){
	            builder.setProxy(new HttpHost(proxyHost, proxyPort)).build();
	        }

	        HttpPost post = new HttpPost(url);
	        post.setConfig(builder.build());
	
	        List<NameValuePair> list = new ArrayList<>();
	        list.add(new BasicNameValuePair("mnc", mnc+""));
	        list.add(new BasicNameValuePair("lac", Integer.parseInt(lac)+""));
	        list.add(new BasicNameValuePair("ci", Integer.parseInt(cell)+""));
	        list.add(new BasicNameValuePair("key", key));
	
	        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list,"utf-8");
	        post.setEntity(entity);
	
	        CloseableHttpResponse response = null;
            response = client.execute(post);
            int statusCode = response.getStatusLine().getStatusCode();

            if(HttpStatus.SC_OK == statusCode){
                String content = EntityUtils.toString(response.getEntity(), "utf-8");

                logger.info("method = request, requestNo = {}, content = {}", requestNo, content);

                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> map = objectMapper.readValue(content, Map.class);

                String resultCode = map.remove("error_code").toString();

                if("0".equals(resultCode)){
                    Map data = (Map) map.remove("result");
                    //Map data = (Map) ((List) result.remove("data")).get(0);
                    map.put("dMcc",data.get("mcc"));
                    map.put("dMnc",data.get("mnc"));
                    map.put("dLac",data.get("lac"));
                    map.put("dCell",data.get("ci"));
                    map.put("dLng",data.get("lon"));
                    map.put("dLat",data.get("lat"));
                    map.put("oLng",data.get("lon"));
                    map.put("oLat",data.get("lat"));
                    map.put("precision",data.get("radius"));
                    map.put("address",data.get("address"));
                }

                logger.info("method = request, requestNo = {}, map = {}", requestNo, map);
                return map;
            }
            throw new RuntimeException("三方接口无法正常响应");
        } finally{
            if(client != null){
                client.close();
            }
        }
    }
    
    
}
