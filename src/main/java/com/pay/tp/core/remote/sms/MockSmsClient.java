package com.pay.tp.core.remote.sms;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.pay.tp.core.entity.sms.SmsMsg;

/**
 * <AUTHOR>
 * @date 2022年07月27日 17:13
 */
@Component
public class MockSmsClient implements SmsClient{
    private final Logger logger = LoggerFactory.getLogger(MockSmsClient.class);
    private Map<String, String> codeMap = new HashMap<>();


    @Override
    public Map<String, Object> request(String requestNo, String content, String phone) throws Exception {
    	 HashMap<String, Object> map = new HashMap<>(2);
         map.put("result", 0);
         return map;
    }


	@Override
	public Map<String, Object> parse(SmsMsg sms) {
		Map<String, Object> map = new HashMap<>(2);
            map.put("code", 0);
        return map;
	}


	@Override
	public List<SmsMsg> parse(List<Map<String, String>> list) {
		// TODO Auto-generated method stub
		return null;
	}


}
