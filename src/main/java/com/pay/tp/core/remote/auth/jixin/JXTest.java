package com.pay.tp.core.remote.auth.jixin;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.codehaus.jackson.map.ObjectMapper;

public class JXTest {
	public static final String url = "http://121.8.160.34:9003/jxdata/api/auth/jm/execute.do";//接口地址 见接口文档
	public static final String merchno = "0000000000000003";// 商户号 联系商服提供
	public static final String merchkey = "yJTM3pnDudZmE5pR6xcmBv8yKvHGnydv";// 商户秘钥 联系商服提供

	public static void main(String[] args) throws Exception {
		AESUtils aesUtils = new AESUtils(merchkey.getBytes(), merchkey.length());
		// 商户订单号 保证唯一
		String orderId = UUID.randomUUID().toString().replaceAll("-", "");
		Map<String, Object> reqMap = new HashMap<>();
		// 接口编码 不同业务各不相同 具体见接口文档
		reqMap.put("transcode", "116");
		// 版本号 固定0100
		reqMap.put("version", "0100");
		// 商户号
		reqMap.put("merchno", merchno);
		// 商户流水号
		reqMap.put("ordersn", orderId);
		// 商户订单号
		reqMap.put("dsorderid", orderId);
		// 姓名 AES加密
		reqMap.put("username", aesUtils.encrypt("许秦稷"));
		// 证件号 AES加密
		reqMap.put("idcard", aesUtils.encrypt("232723199012061113"));
		// 银行卡号 AES加密
		reqMap.put("bankcard", aesUtils.encrypt("6212260200043832852"));
		// 手机号 AES加密
		//reqMap.put("mobile", aesUtils.encrypt("***********"));
		// 证件类型 枚举见文档
		reqMap.put("idtype", "01");
		// 真实场景 见接口文档枚举 必传
		reqMap.put("sceneCode", "01");
		// 真实终端商户名称 由于银联监管 必传
		reqMap.put("sCustomerName", "吉信");
		// 加签 MD5
		String sign = SignUtil.getSign(reqMap, merchkey);
		reqMap.put("sign", sign);
		System.out.println("请求参数：" + reqMap);

		ObjectMapper mapper = new ObjectMapper();

		byte[] request = mapper.writeValueAsBytes(reqMap);
		// 发送
		String result = CommonUtil.post(url, null, 0, request);
		System.out.println("返回参数：" + result);
		// 验签
		Map<String, Object> resMap = mapper.readValue(result, Map.class);
		sign = SignUtil.getSign(resMap, merchkey);
		if (resMap.get("sign").equals(sign)) {
			System.out.println("返回验签正确");
		} else {
			System.out.println("返回验签失败");
		}
	}
}
