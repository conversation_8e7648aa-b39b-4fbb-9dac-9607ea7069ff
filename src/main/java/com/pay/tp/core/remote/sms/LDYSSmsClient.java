package com.pay.tp.core.remote.sms;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.CommonConstants;
import lombok.Data;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @date 2021-08-05 11:14:32
 */
@FeignClient(value = CommonConstants.PAYCHANNEL_EUREKA_SERVER_INSTANCE_CORE)
public interface LDYSSmsClient {


    /**
     * @param smsReq
     * @Description 发送短信
     */
    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/otherCommon/sms",
            method = RequestMethod.POST, headers = {"cus-client-name=manage-core"},
            produces = {"application/json;charset=UTF-8"})
    ResultsBean<String> sendSms(@RequestBody SmsReq smsReq);

    @Data
    class SmsReq {


        /**
         * 请求流水号
         * 两位字母加 13位数字
         */
        private String requestNo;
        /**
         * 手机号码
         */
        private String phone;

        /**
         * 短信内容
         */
        private String content;
    }
}


