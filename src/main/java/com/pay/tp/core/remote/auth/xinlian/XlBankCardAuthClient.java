package com.pay.tp.core.remote.auth.xinlian;

import java.util.Map;

import com.pay.tp.core.entity.auth.BankcardAuth;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.remote.xinlian
 * @Description: TODO
 * @date Date : 2019年01月16日 14:48
 */
public interface XlBankCardAuthClient {

    public Map<String, Object> parse(BankcardAuth auth);

    public XinlianRes request(String cooperSerialNo, String cardNo, String name, String cidNo, String mobile, String customerName)
            throws Exception;
}
