package com.pay.tp.core.remote.position;

import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR> zhangjian
 * @Package com.pay.position.core.client
 * @Description: TODO
 * @date Date : 2018年12月24日 16:05
 */
@Component
public class LbsClient {

    private final Logger logger = LoggerFactory.getLogger(AliyunClient.class);

    private String url = "http://api.cellocation.com:82/cell/?coord=gcj02&output=json&mcc=460&";
    private int connectionTimeout = 1000;
    private int socketTimeout = 1000;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    public Map<String, String> request(String lac,String ci,String mnc) throws Exception {
        logger.info("LBS method = request, lac = {}, cell = {}, mnc = {}", lac,ci,mnc);

        CloseableHttpClient client = null;
        try{

            client = HttpClientBuilder.create().build();
            RequestConfig.Builder builder = RequestConfig.custom()
                    .setConnectTimeout(connectionTimeout).setSocketTimeout(socketTimeout);

            if(!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)){
                builder.setProxy(new HttpHost(proxyHost, proxyPort)).build();
            }
            
            HttpGet get = new HttpGet(url + "&mnc=" + mnc + "&lac=" + lac + "&ci=" + ci);
            get.setConfig(builder.build());

            HttpResponse response = client.execute(get);
            int statusCode = response.getStatusLine().getStatusCode();

            if(HttpStatus.SC_OK == statusCode){
                String content = EntityUtils.toString(response.getEntity());
                logger.info("LBS method = request, content = {}", content);
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String,String> map = objectMapper.readValue(content, Map.class);
                logger.info("LBS method = response,  map = {}", map);
                return map;

            }else{
                logger.error("LBS method = request, statusCode = {}", statusCode);
            }
        }finally {
            if(client != null){
                client.close();
            }
        }

        return new HashMap<>();
    }
    
    public static void main(String[] args) throws Exception {
    	LbsClient lbsClient = new LbsClient();
    	Map<String, String> request = lbsClient.request( "4301", "20986", "1");
    	System.out.println(request);
	}
    
    
}
