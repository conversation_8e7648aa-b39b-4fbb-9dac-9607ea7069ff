package com.pay.tp.core.remote.bean;

import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR> @date 2024/4/12
 * @apiNote
 */
@Data
@ToString(callSuper = true)
public class JlQuerySlResultReq {

    private String origin;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 交易流水号
     */
    private String transId;

    public JlQuerySlResultReq() {
    }

    public JlQuerySlResultReq(String origin, String customerNo, String transId) {
        this.origin = origin;
        this.customerNo = customerNo;
        this.transId = transId;
    }
}
