package com.pay.tp.core.remote.sms;

import java.nio.charset.Charset;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.entity.sms.SmsRes;

/**
 * <AUTHOR> zhangjian
 * @Package com.pay.sms.core.client
 * @Description: TODO
 * @date Date : 2018年12月24日 10:05
 */
@Component
public class XuanWuClient implements  SmsClient{

    private final Logger logger = LoggerFactory.getLogger(XuanWuClient.class);

    private String id = "xuanwu";

    @Value("${custom.xuanwu.username:}")
    private String username;
    @Value("${custom.xuanwu.password:}")
    private String password;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;
    @Value("${custom.xuanwu.cgi:}")
    private String cgi;

    private DateFormat df = new SimpleDateFormat("yyyyMMddhhmmss");

    private Map<String, String> codeMap = new HashMap<>();

    @PostConstruct
    private void init(){

        codeMap.put("0", "正常发送");
        codeMap.put("-2", "发送参数填定不正确");
        codeMap.put("-3", "用户载入延迟");
        codeMap.put("-6", "密码错误");
        codeMap.put("-7", "用户不存在");
        codeMap.put("-11", "发送号码数理大于最大发送数量");
        codeMap.put("-12", "余额不足");
        codeMap.put("-99", "内部处理错误");
        codeMap.put("其他", "未知错误");

    }


    @Override
    public Map<String,Object> request(String requestNo, String content, String phone) throws Exception {

        logger.info("method = request ,requestNo = {}, to = {}", requestNo, phone);

        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        HttpPost post = new HttpPost(cgi + "/sendsms");

        if(!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)){
            RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
            post.setConfig(config);
        }

        ObjectMapper objectMapper  = new ObjectMapper();
        Map<String, String> params = new HashMap<>();
        params.put("username", username);
        params.put("password", Md5Encrypt.md5(password));
        params.put("to", phone);
        params.put("text", content);
        params.put("subID", "");
        params.put("msgType", "4");
        params.put("encode", "1");
        params.put("version", "1.0");

        post.setEntity(new StringEntity(objectMapper.writeValueAsString(params), Charset.forName("UTF-8")));


        HttpResponse response = httpClient.execute(post);
        String entity = EntityUtils.toString(response.getEntity());

        logger.info("method = request entity = {}, requestNo = {}" , entity, requestNo);

        if(200 == response.getStatusLine().getStatusCode()){

            Map<String, Object> map = new HashMap<>(2);

            map.put("result", entity);
            map.put("msg", codeMap.get(entity));

            return map;
        }else{
            throw new RuntimeException("短信通道发送失败");
        }


    }


    @Override
    public Map<String, Object> parse(SmsMsg sms) {
        Map<String, Object> map = new HashMap<>(2);
        if("0".equals(sms.getResult())){
            map.put("code", 0);
        }else{
            map.put("code", -1);
            map.put("msg", sms.getMessage());
        }

        return map;
    }

    //TODO  暂时不需要回调
    @Override
    public List<SmsMsg> parse(List<Map<String, String>> list) {
        List<SmsMsg> smsMsgList = new ArrayList<>(list.size());
        return smsMsgList;
    }


}
