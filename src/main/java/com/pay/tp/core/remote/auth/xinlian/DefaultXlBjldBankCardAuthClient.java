package com.pay.tp.core.remote.auth.xinlian;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 创建时间：2018年12月19日 下午11:22:53
 * @ClassName
 * @Description
 */
//@Service
//@Profile({"proc"})
@Service("xlBjldAuthClient")
public class DefaultXlBjldBankCardAuthClient extends DefaultXlBankCardAuthClient{

    @Value("${own.xinlian.insIdBjld:}")
    private String insId;

    @Value("${own.xinlian.operIdBjld:}")
    private String operId;

	@Override
	public String getInsId() {
		return insId;
	}

	@Override
	public String getOperId() {
		return operId;
	}
}
