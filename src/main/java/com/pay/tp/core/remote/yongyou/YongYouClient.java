package com.pay.tp.core.remote.yongyou;

import com.pay.tp.core.beans.yongyou.YongYouCustomerReq;
import com.pay.tp.core.beans.yongyou.YongYouCustomerResp;
import com.pay.tp.core.utils.JsonUtil;
import com.pay.tp.core.utils.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/**
 * 用友API客户端，封装用友接口调用
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Component
public class YongYouClient {

    @Autowired
    private RestTemplate yyProxyRestTemplate;

    @Value("${yongyou.api.baseUrl:http://182.92.74.199:5580}")
    private String baseUrl;

    @Value("${yongyou.api.signKey:}")
    private String signKey;

    @Value("${yongyou.api.partnerId:}")
    private String partnerId;

    @Value("${yongyou.api.company:}")
    private String company;


    @Value("${yongyou.api.addCustomer:}")
    private String addCustomer;

    /**
     * 添加客户
     *
     * @param customerReq 客户信息请求
     * @return 添加结果
     */
    public YongYouCustomerResp addCustomer(YongYouCustomerReq customerReq) {
        try {
            // 构建请求URL
            String url = baseUrl + addCustomer;
            if (addCustomer == null || addCustomer.trim().isEmpty() || url.endsWith("null")) {
                url = baseUrl + "/open/customer/add";
                log.warn("addCustomer配置为空，使用默认路径: /open/customer/add");
            }

            // 验证客户信息
            if (customerReq == null || customerReq.getCustomers() == null || customerReq.getCustomers().isEmpty()) {
                throw new IllegalArgumentException("客户信息不能为空");
            }

            // 构建请求头
            HttpHeaders headers = buildHeaders();
            String requestBody = JsonUtil.toJson(customerReq.getCustomers());
            HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String responseStr;
            try {
                log.info("请求URL: {}", url, requestBody, headers);
                responseStr = yyProxyRestTemplate.postForObject(url, httpEntity, String.class);
                log.info("响应: {}", responseStr);
            } catch (RestClientException e) {
                log.error("HTTP请求失败: {}", e.getMessage());
                log.error("异常类型: {}", e.getClass().getSimpleName());
                if (e.getCause() != null) {
                    log.error("根本原因: {}", e.getCause().getMessage());
                }

                YongYouCustomerResp errorResponse = new YongYouCustomerResp();
                errorResponse.setSuccess(false);
                errorResponse.setMessage("HTTP请求失败: " + e.getMessage());
                return errorResponse;
            }

            log.info("用友添加客户响应: {}", responseStr);

            // 解析响应
            if (responseStr != null && !responseStr.trim().isEmpty()) {
                try {
                    YongYouCustomerResp response = JsonUtil.toObject(responseStr, YongYouCustomerResp.class);
                    log.info("响应解析成功: success={}, message={}", response.getSuccess(), response.getMessage());
                    return response;
                } catch (Exception parseException) {
                    log.error("响应解析失败", parseException);
                    YongYouCustomerResp errorResponse = new YongYouCustomerResp();
                    errorResponse.setSuccess(false);
                    errorResponse.setMessage("响应解析失败: " + parseException.getMessage());
                    return errorResponse;
                }
            } else {
                log.warn("响应为空");
                YongYouCustomerResp errorResponse = new YongYouCustomerResp();
                errorResponse.setSuccess(false);
                errorResponse.setMessage("响应为空");
                return errorResponse;
            }

        } catch (Exception e) {
            log.error("用友添加客户异常", e);
            YongYouCustomerResp errorResponse = new YongYouCustomerResp();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("调用异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 构建请求头
     *
     * @return HttpHeaders
     */
    private HttpHeaders buildHeaders() {
        HttpHeaders headers = new HttpHeaders();

        // 设置Content-Type，确保字符集正确
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        headers.set("User-Agent", "Java-HttpClient");

        // 生成时间戳（毫秒）
        long timestamp = System.currentTimeMillis();

        // 验证配置参数
        if (signKey == null || signKey.trim().isEmpty()) {
            log.error("signKey配置为空，无法生成签名");
            throw new IllegalStateException("signKey配置为空");
        }
        if (company == null || company.trim().isEmpty()) {
            log.error("company配置为空，无法生成签名");
            throw new IllegalStateException("company配置为空");
        }
        if (partnerId == null || partnerId.trim().isEmpty()) {
            log.error("partnerId配置为空，无法生成签名");
            throw new IllegalStateException("partnerId配置为空");
        }

        // 计算签名: md5(签名key+company+partnerId+timestamp).toUpper
        String signStr = signKey + company + partnerId + timestamp;
        String sign = Md5Util.encode(signStr);
        if (sign != null) {
            sign = sign.toUpperCase();
        }

        // 设置请求头 - 确保参数名称正确
        headers.set("timestamp", String.valueOf(timestamp));
        headers.set("partnerId", partnerId);
        headers.set("company", company);
        headers.set("sign", sign);

        log.info(" 签名计算: signKey({}) + company({}) + partnerId({}) + timestamp({}) = {}",
                signKey, company, partnerId, timestamp, signStr);

        return headers;
    }
}
