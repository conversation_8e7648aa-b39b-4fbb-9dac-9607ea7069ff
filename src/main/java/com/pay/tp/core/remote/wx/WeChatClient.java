package com.pay.tp.core.remote.wx;

import com.pay.frame.cache.util.RedisUtil;
import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.RedisKeyUtil;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.wx.WxMsgTemplateBean;
import com.pay.tp.core.exception.SmsDingDingException;
import com.pay.tp.core.utils.JsonUtil;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class WeChatClient {
    private final org.slf4j.Logger logger = LoggerFactory.getLogger(WeChatClient.class);

    @Value("${wx.push.accessTokenUrl:}")
    private String accessTokenUrl;
    @Value("${wx.push.templateUrl:}")
    private String templateUrl;
    @Value("${wx.push.openIdUrl:}")
    private String openIdUrl;

    @Value("${wx.push.jscode2sessionUrl:}")
    private String jscode2sessionUrl;

    @Value("${wx.push.plusAppid:}")
    private String plusAppid;
    @Value("${wx.push.plusSecret:}")
    private String plusSecret;
    @Value("${wx.push.ukAppid:}")
    private String ukAppid;
    @Value("${wx.push.ukSecret:}")
    private String ukSecret;

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;


    @Autowired
    private RestTemplate restTemplate;


    public String request(String code, String brand) throws Exception {
        logger.info("method code:{}", code);
        CloseableHttpClient httpClient = null;
        try {
            httpClient = HttpClientBuilder.create().build();

            Map<String, String> params = getWeChatAppidSecret(brand);

            params.put("code", code);
            params.put("grant_type", "authorization_code");
            String url = assembleUrl(openIdUrl, params);
            HttpGet get = new HttpGet(url);

            if (!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)) {
                RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
                get.setConfig(config);
            }

            HttpResponse response = httpClient.execute(get);
            int statusCode = response.getStatusLine().getStatusCode();

            if (HttpStatus.SC_OK == statusCode) {
                String entity = EntityUtils.toString(response.getEntity(), "utf-8");
                logger.info("method = request entity = {}", entity);
                return entity;
            }

            throw new RuntimeException("获取微信id失败");
        } finally {
            if (httpClient != null) {
                httpClient.close();
            }
        }

    }


    public String assembleUrl(String urlPar, Map<String, String> params) {
        logger.info("assembleUrl = {}", urlPar);
        StringBuilder str = new StringBuilder(urlPar);
        if (str.toString().endsWith("?")) {

        } else if (str.toString().contains("?")) {
            str.append("&");
        } else {
            str.append("?");
        }

        for (Map.Entry<String, String> entry : params.entrySet()) {
            try {
                if (entry.getValue() == null || entry.getValue().length() == 0) {
                    str.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                } else {
                    str.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), "utf-8")).append("&");
                }
            } catch (Exception e) {
                logger.info("assembleUrl:=" + e);
            }
        }
        return str.toString();
    }


    /**
     * 获取公众号的 access_token
     * @param brand
     * @return
     */
    public String getWeChatAccessToken(String brand) {
        Map<String, String> params = getWeChatAppidSecret(brand);
        return getAccessToken(params.get("appid"), params.get("secret"));
    }
    public Map<String, String> getWeChatAppidSecret(String brand) {
        Map<String, String> params = new HashMap<>();
        if (Brand.UK.name().equals(brand)) {
            params.put("appid", ukAppid);
            params.put("secret", ukSecret);
        }else if (Brand.PLUS.name().equals(brand)){
            params.put("appid", plusAppid);
            params.put("secret", plusSecret);
        }else {
            throw new ServerException("品牌类型错误");
        }
        return params;
    }




    public String getCacheAccessToken(String appid, String secret) {
        String key = RedisKeyUtil.pubKey("WX_ACCESS_TOKEN", appid);
        Object accessToken = RedisUtil.get(key);
        if (accessToken == null || StringUtils.isBlank(accessToken.toString())) {
            accessToken = getAccessToken(appid, secret);
            RedisUtil.set(key, accessToken, 25, TimeUnit.MINUTES);
        }
        return accessToken.toString();
    }

    private String getAccessToken(String appId, String appSecret) {
//        https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
        String getTokenUrl = String.format(accessTokenUrl + "?grant_type=client_credential&appid=%s&secret=%s", appId, appSecret);
        logger.info("wx 获取access_token url {}", accessTokenUrl);
        Map<String, Object> result = restTemplate.getForObject(getTokenUrl, Map.class);
        logger.info("wx 获取access_token {}, {}", getTokenUrl, result);

        String accessToken = result.getOrDefault("access_token", "").toString();
        if (StringUtils.isBlank(accessToken)) {
            logger.error("获取accessToken error {}, {}", appId, result);
            String errcode = result.getOrDefault("errcode", -1).toString();
            throw new SmsDingDingException(errcode, "请求 access_token 微信响应异常");
        }
//        Integer value = Integer.valueOf(result.getOrDefault("expires_in", "0").toString());
        return accessToken;

    }

    /**
     * 推送微信公众号
     *
     * @param map
     * @param cfgValue
     * @param wxTemplateCode
     */
    public Map sendWxMessage(WxMsgTemplateBean wxMsgTemplate, String accessToken) {
// https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN

        String url = String.format(templateUrl + "?access_token=%s", accessToken);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        HttpEntity<String> httpEntity = new HttpEntity(wxMsgTemplate, headers);

        logger.info("sendWxMessage send url: {}", templateUrl, JsonUtil.toJson(httpEntity));
        Map sendResult = restTemplate.postForObject(url, httpEntity, Map.class);
        logger.info("sendWxMessage send url: {}, req body: {}, result: {}",
                url, httpEntity, JsonUtil.toJson(sendResult));

        return sendResult;
    }



    public String getOpenId(String code, String appId, String secret) throws Exception {
        logger.info("method code:{}", code);
        CloseableHttpClient httpClient = null;
        try {
            httpClient = HttpClientBuilder.create().build();

            Map<String, String> params = new HashMap<>();
            params.put("appid", appId);
            params.put("secret", secret);
            params.put("code", code);
            params.put("grant_type", "authorization_code");
            String url = assembleUrl(openIdUrl, params);
            HttpGet get = new HttpGet(url);

            if (!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)) {
                RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
                get.setConfig(config);
            }

            HttpResponse response = httpClient.execute(get);
            int statusCode = response.getStatusLine().getStatusCode();

            if (HttpStatus.SC_OK == statusCode) {
                String entity = EntityUtils.toString(response.getEntity(), "utf-8");
                logger.info("method = request entity = {}", entity);
//                Map map = objectMapper.readValue(entity, Map.class);
                return entity;
            }

            throw new RuntimeException("获取微信id失败");
        } finally {
            if (httpClient != null) {
                httpClient.close();
            }
        }

    }

    /**
     * 小程序获取openID
     *
     * @param code      授权码
     * @param appId
     * @param appSecret
     * @return
     */
    public String getWxAppOpenId(String code, String appId, String appSecret) throws IOException {
        logger.info("method getWxAppOpenId code:{}, appId:{}, appSecret:{}", code, appId, appSecret);
        CloseableHttpClient httpClient = null;
        try {

            String url = "https://api.weixin.qq.com/sns/jscode2session";
            url += "?appid="+ appId;//自己的appid
            url += "&secret="+ appSecret;//自己的appSecret
            url += "&js_code=" + code;
            url += "&grant_type=authorization_code";
            url += "&connect_redirect=1";
            logger.info("jscode2sessionUrl {}", url);
            HttpGet get = new HttpGet(url);
            if (!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)) {
                RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
                get.setConfig(config);
            }
            httpClient = HttpClientBuilder.create().build();
            HttpResponse response = httpClient.execute(get);
            int statusCode = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK == statusCode) {
                String entity = EntityUtils.toString(response.getEntity(), "utf-8");
                logger.info("method getWxAppOpenId code:{}, appId:{}, appSecret:{}, entity:{}", code, appId, appSecret, entity);
                return entity;
            }
        } catch (ClientProtocolException e) {
            logger.error("method getWxAppOpenId error", e);
        } finally {
            if (httpClient != null) {
                httpClient.close();
            }
        }
        throw new RuntimeException("获取微信用户失败");
    }

}
