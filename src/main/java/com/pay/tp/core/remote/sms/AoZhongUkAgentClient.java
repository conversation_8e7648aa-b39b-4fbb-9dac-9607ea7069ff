package com.pay.tp.core.remote.sms;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.frame.common.base.util.DateUtil;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.utils.JsonUtil;
import com.pay.tp.core.utils.Md5Util;

/**
 * <AUTHOR>
 * @date 2022年07月27日 17:13
 */
@Component
public class AoZhongUkAgentClient implements SmsClient{
    private final Logger logger = LoggerFactory.getLogger(AoZhongUkAgentClient.class);

    @Value("${custom.aozhongUkAgent.send.url:}")
    private String sendUrl;
    @Value("${custom.aozhongUkAgent.uid:}")
    private String uid;
    @Value("${custom.aozhongUkAgent.password:}")
    private String password;

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;


    private Map<String, String> codeMap = new HashMap<>();
    @PostConstruct
    private void init(){
        codeMap.put("0", "发送成功");
        codeMap.put("1", "参数不足有误");
        codeMap.put("2", "用户不存在");
        codeMap.put("3", "账号密码或IP错误");
        codeMap.put("4", "该商户已被冻结");
        codeMap.put("5", "该账号不支持该接口");
        codeMap.put("6", "号码有误");
        codeMap.put("7", "发送内容有误");
        codeMap.put("8", "余额不足");
        codeMap.put("9", "操作异常");
        codeMap.put("10", "发送失败");
    }


    @Override
    public Map<String, Object> request(String requestNo, String content, String phone) throws Exception {
        logger.info("method = request ,requestNo = {}, to = {}", requestNo, phone);

        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        HttpPost post = new HttpPost(sendUrl);

        if(!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)){
            RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
            post.setConfig(config);
        }
        post.addHeader("Accept","application/json;");
        post.addHeader("Content-Type","application/json;charset=utf-8;");

        ObjectMapper objectMapper  = new ObjectMapper();
        Map<String, String> params = new HashMap<>();
        params.put("uid", uid);
        String md5_16 = Md5Util.encode(password).substring(8, 24);
        String time = DateUtil.format(new Date(), "yyyyMMddHHmmssSS");
        params.put("pwd", Md5Util.encode(md5_16 + time));
        params.put("mobile", phone);
        params.put("content", content);
        params.put("time", time);

        post.setEntity(new StringEntity(objectMapper.writeValueAsString(params), Charset.forName("UTF-8")));

        HttpResponse response = httpClient.execute(post);
        String entity = EntityUtils.toString(response.getEntity());
        logger.info("method = request entity = {}, requestNo = {}, params={}" , entity, requestNo, params);
        if(200 == response.getStatusLine().getStatusCode()){
            Map resp = objectMapper.readValue(entity, Map.class);

            Map<String, Object> map = new HashMap<>(2);
            map.put("result", resp.get("status"));
            if("10".equals(resp.get("status"))){
                map.put("msg", JsonUtil.toJson(resp.get("data_info")));
            }else {
                map.put("msg", resp.get("status_code"));
            }
            if(resp.get("data_info")!=null && StringUtils.isNotBlank(resp.get("data_info").toString())) {
            	Map<String, String> data_info = (Map<String, String>) resp.get("data_info");
            	map.put("msgId", data_info.get("msgid"));
            }
            return map;
        }else{
            throw new RuntimeException("短信通道发送失败");
        }
    }


    @Override
    public Map<String, Object> parse(SmsMsg sms) {
        Map<String, Object> map = new HashMap<>(2);
        if("0".equals(sms.getResult())){
            map.put("code", 0);
        }else{
            map.put("code", -1);
            map.put("msg", sms.getMessage());
        }

        return map;
    }

    // 	[{"msgid":"短信msgid","mobile":"手机号","status":"状态码","time":"xxxx-xx-xx xx:xx:xx"}]
    @Override
    public List<SmsMsg> parse(List<Map<String, String>> list) {
        List<SmsMsg> smsMsgList = new ArrayList<>(list.size());
        for (Map<String, String> map : list) {
            String msgid = map.get("msgid");
            String mobile = map.get("mobile");

            SmsMsg smsMsg = new SmsMsg();
            String status = "0";
            if (!"DELIVRD".equals(map.get("status"))) {
                status = "10";
                smsMsg.setResMessage(map.get("status"));
            }
            smsMsg.setResResult(status);
            smsMsg.setPhone(mobile);
            smsMsg.setMsgId(msgid);
            smsMsgList.add(smsMsg);
        }
        return smsMsgList;
    }


}
