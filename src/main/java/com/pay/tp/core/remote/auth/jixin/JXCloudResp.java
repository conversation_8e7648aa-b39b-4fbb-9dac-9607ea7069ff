package com.pay.tp.core.remote.auth.jixin;

/**
 * @Description: 掌讯鉴权返回
 * @see: AmiCloudResp 此处填写需要参考的类
 * @version Nov 20, 2019 1:39:19 PM 
 * <AUTHOR>
 */
public class JXCloudResp {
	private String transcode;		// 交易码
	private String ordersn;			// 商户请求流水号
	private String merchno;			// 商户号
	private String dsorderid;		// 商户订单号
	private String orderid;			// 平台交易流水号
//	private String returncode;		// 返回码
//	private String errtext;			// 返回信息
	private String sign;			// 加密校验值
	private String platformCode;	// 平台返回码
	private String platformDesc;	// 平台返回信息
	
	public String getTranscode() {
		return transcode;
	}
	public void setTranscode(String transcode) {
		this.transcode = transcode;
	}
	public String getOrdersn() {
		return ordersn;
	}
	public void setOrdersn(String ordersn) {
		this.ordersn = ordersn;
	}
	public String getMerchno() {
		return merchno;
	}
	public void setMerchno(String merchno) {
		this.merchno = merchno;
	}
	public String getDsorderid() {
		return dsorderid;
	}
	public void setDsorderid(String dsorderid) {
		this.dsorderid = dsorderid;
	}
	public String getOrderid() {
		return orderid;
	}
	public void setOrderid(String orderid) {
		this.orderid = orderid;
	}
//	public String getReturncode() {
//		return returncode;
//	}
//	public void setReturncode(String returncode) {
//		this.returncode = returncode;
//	}
//	public String getErrtext() {
//		return errtext;
//	}
//	public void setErrtext(String errtext) {
//		this.errtext = errtext;
//	}
	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}
	public String getPlatformCode() {
		return platformCode;
	}
	public void setPlatformCode(String platformCode) {
		this.platformCode = platformCode;
	}
	public String getPlatformDesc() {
		return platformDesc;
	}
	public void setPlatformDesc(String platformDesc) {
		this.platformDesc = platformDesc;
	}
}
