package com.pay.tp.core.remote.auth.xinlian;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import com.google.gson.Gson;
import com.pay.tp.core.exception.AuthRemoteException;
import com.pay.tp.core.remote.auth.BankCardAuthClient;
import com.pay.tp.core.beans.auth.BankCardRes;
import com.pay.tp.core.service.auth.XlCloudService;

/**
 * <AUTHOR>
 * @version 创建时间：2018年12月19日 下午11:22:53
 * @ClassName
 * @Description
 */
//@Service
//@Profile({"proc"})
//@Service("xlAuthClient")
public abstract class DefaultXlBankCardAuthClient implements BankCardAuthClient {
    private final Logger logger = LoggerFactory.getLogger(DefaultXlBankCardAuthClient.class);

    @Value("${custom.xinlian.bankcard.auth.url:}")
    private String url;

    public abstract String getInsId();
    public abstract String getOperId();

    private String charset = "UTF-8";

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    private String transCode = "99";
    private String scUsageScenarios = "用户注册时，对个人风控核验验证，过滤恶意注册及其他风险用户";	// 终端商户产品的使用场景
    private String scUsePurpose = "风控核验，过滤风险用户";	// 终端商户的使用目的
    private String protocolVerNm = "《用户协议》和《隐私保护政策》2022.2.0版";	// 终端商户与C端客户签订的电子协议版本号
    
    
    @Autowired
    private XlCloudService xlCloudService;

    /**
	 * @Description 认证请求
	 * @param outTradeNo   请求号
	 * @param cardNo       卡号
	 * @param name         名称
	 * @param cidNo        身份证号
	 * @param mobile       手机号
	 * @param customerName 认证人
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
    @Override
    public BankCardRes request(String outTradeNo, String cardNo, String name, String cidNo, String mobile, String customerName,String brand)
            throws Exception {
    	logger.info("method =request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}",
        		outTradeNo, cardNo, name, cidNo, mobile, customerName);
    	
        Gson gson = new Gson();
        RSAHelper cipher = new RSAHelper();// 初始化自己的私钥,对方的公钥以及密钥长度.
        cipher.initKey(2048);
        Map<String, String> param = new HashMap<>();
        param.put("cooperSerialNo", outTradeNo);	// 请求流水号
        param.put("cardNo", cardNo);	// 银行卡号
        param.put("name", name);	// 姓名
        param.put("cidNo", cidNo);	// 身份证
        param.put("mobile", mobile);	// 手机号
        if(com.pay.frame.common.base.util.StringUtils.isNotBlank(customerName)) {
        	param.put("scName", customerName);	//  终端商户名称
        }else {
        	param.put("scName", "个体户-" + name);	//  终端商户名称
        }
        param.put("transCode", transCode);		// 交易场景编码
        param.put("scUsageScenarios", scUsageScenarios);	//  终端商户产品的使用场景
        param.put("scUsePurpose", scUsePurpose);	//  终端商户的使用目的
        param.put("protocolVerNm", protocolVerNm);	//  终端商户与C端客户签订的电子协议版本号
        
        logger.info("method =request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {},  请求报文 = {}",
				outTradeNo, cardNo, name, cidNo, mobile,  param);

        String jsonStr = gson.toJson(param);
        // 签名
		byte[] signBytes = cipher.signRSA(jsonStr.getBytes(charset), false, charset);
		// 对明文加密
		byte[] cryptedBytes = cipher.encryptRSA(jsonStr.getBytes(charset), false, charset);

        String content = "";
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        if(!StringUtils.isEmpty(proxyHost)){
            RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
            httpPost.setConfig(config);
        }

        List<NameValuePair> formParams = new ArrayList<NameValuePair>();
        formParams.add(new BasicNameValuePair("insId", getInsId()));
        formParams.add(new BasicNameValuePair("operId", getOperId()));
        formParams.add(new BasicNameValuePair("sign", Base64.encodeBase64String(signBytes)));
        formParams.add(new BasicNameValuePair("encrypt", Base64.encodeBase64String(cryptedBytes)));
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(formParams, charset));
            HttpResponse httpResponse = httpClient.execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            logger.info("method = request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, formParams = {}, statusCode = {}, url = {}",
            		outTradeNo, cardNo, name, cidNo, mobile, formParams, statusCode, url);
            if (statusCode == HttpStatus.SC_OK) {
            	HttpEntity resEntity = httpResponse.getEntity();
                if (resEntity != null) {
                    content = EntityUtils.toString(resEntity, charset);
                    logger.info("method = request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, content = {}",
                    		outTradeNo, cardNo, name, cidNo, mobile, customerName, content);
                } else {
                	logger.info("method = request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 无返回结果",
                			outTradeNo, cardNo, name, cidNo, mobile, customerName);
                    throw new RuntimeException("无返回结果");
                }
                EntityUtils.consume(resEntity);
            }else {
            	throw new RuntimeException("请求失败：" + statusCode);
            }
        } catch (Exception e) {
            logger.error("请求异常", e);
            throw new AuthRemoteException(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
            }
        }
        Map<String, String> map = gson.fromJson(content, HashMap.class);
        logger.info("method = request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, map = {}",
        		outTradeNo, cardNo, name, cidNo, mobile, customerName, map);
        //签名
        signBytes = Base64.decodeBase64(map.get("sign"));
        //密文
        cryptedBytes = Base64.decodeBase64(map.get("encrypt"));

        //如果密文为空，则直接返回content
        if (cryptedBytes == null) {
        	logger.info("method = request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 返回密问为空，直接返回内容",
            		outTradeNo, cardNo, name, cidNo, mobile, customerName);
            //return gson.fromJson(content, XinlianRes.class);
            return xlCloudService.convertXl(gson.fromJson(content, XinlianRes.class));
        }
    	 // 对密文解密
        byte[] decryptedBytes = cipher.decryptRSA(cryptedBytes, false, charset);
        String body = new String(decryptedBytes, charset);
        logger.info("method = request XL {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, body = {}",
        		outTradeNo, cardNo, name, cidNo, mobile, customerName, body);
        
        boolean isValid = cipher.verifyRSA(decryptedBytes, signBytes, false, charset);
        if (isValid) {
        	return xlCloudService.convertXl(gson.fromJson(body, XinlianRes.class));
        }else {
        	throw new RuntimeException("验签失败");
        }
        	
    }
}
