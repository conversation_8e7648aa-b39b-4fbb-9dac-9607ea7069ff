package com.pay.tp.core.remote.sms;

import java.nio.charset.Charset;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.entity.sms.SmsRes;
import com.pay.tp.core.utils.Md5Util;

/**
 * <AUTHOR> zhangjian
 * @Package com.pay.sms.core.client
 * @Description: TODO
 * @date Date : 2018年12月24日 10:05
 */
//@Component
public class XiaoMaClient implements  SmsClient{

    private final Logger logger = LoggerFactory.getLogger(XiaoMaClient.class);

    private String id = "xiaoma";

    @Value("${custom.xiaoma.send.url:}")
    private String sendUrl;
    @Value("${custom.xiaoma.password:}")
    private String password;
    @Value("${custom.xiaoma.user:}")
    private String user;
    @Value("${custom.xiaoma.sign:}")
    private String sign;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    private DateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");



    @Override
    public Map<String,Object> request(String requestNo, String content, String phone) throws Exception {

        logger.info("method = request ,requestNo = {}, to = {}", requestNo, phone);


        CloseableHttpClient httpClient = HttpClientBuilder.create().build();




        HttpPost post = new HttpPost(sendUrl);

        if(!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)){
            RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
            post.setConfig(config);
        }
        post.addHeader("Accept","application/json;");
        post.addHeader("Content-Type","application/json;charset=utf-8;");

        ObjectMapper objectMapper  = new ObjectMapper();
        Map<String, String> params = new HashMap<>();
        params.put("clientid",user);
        params.put("password", Md5Util.encode(password).toLowerCase());
        params.put("mobile", phone);
        params.put("smstype", "0");
        params.put("content", sign + content);
        params.put("uid", requestNo);

        post.setEntity(new StringEntity(objectMapper.writeValueAsString(params), Charset.forName("UTF-8")));


        HttpResponse response = httpClient.execute(post);
        String entity = EntityUtils.toString(response.getEntity());

        logger.info("method = request entity = {}, requestNo = {}" , entity, requestNo);

        if(200 == response.getStatusLine().getStatusCode()){

            Map resp = objectMapper.readValue(entity, Map.class);

            Map<String, Object> map = new HashMap<>(2);

            List list = (List) resp.get("data");
            Map data = (Map) list.get(0);

            map.put("result", data.remove("code") + "");
            map.put("msg", data.remove("msg") + "");
            map.put("ext", resp);
            return map;
        }else{
            throw new RuntimeException("短信通道发送失败");
        }

    }


    @Override
    public Map<String, Object> parse(SmsMsg sms) {
        Map<String, Object> map = new HashMap<>(2);
        if("0".equals(sms.getResult())){
            map.put("code", 0);
        }else{
            map.put("code", -1);
            map.put("msg", sms.getMessage());
        }

        return map;
    }

    //TODO  暂时不需要回调
    @Override
    public List<SmsMsg> parse(List<Map<String, String>> list) {
        List<SmsMsg> smsMsgList = new ArrayList<>(list.size());
        return smsMsgList;
    }
}
