package com.pay.tp.core.remote.sms;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.entity.sms.SmsRes;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.client
 * @Description: TODO
 * @date Date : 2018年12月25日 14:23
 */
public interface SmsClient {

    public Map<String,Object> request(String requestNo, String content, String phone) throws Exception;

    public Map<String, Object> parse(SmsMsg sms);

    List<SmsMsg> parse(List<Map<String, String>> list);
}
