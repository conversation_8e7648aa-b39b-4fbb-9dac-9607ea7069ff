package com.pay.tp.core.remote.sms;

import java.util.List;
import java.util.Map;

import com.pay.tp.core.utils.DingTalkSignUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.pay.tp.core.exception.SmsDingDingException;
import com.pay.tp.core.utils.JsonUtil;

import lombok.Builder;
import lombok.Data;


/**
 * 钉钉通道请求，封装客户端
 *
 * <AUTHOR>
 */
@Component
public class DingDingMsgClient {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Value("${dingding.api.token}")
    private String getToken;

    @Value("${dingding.api.userId}")
    private String getUserId;

    @Value("${dingding.api.asyncSendV2}")
    private String asyncSendV2;

    @Value("${dingding.api.robotSend}")
    private String robotSend;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取钉钉通道 AccessToken
     *
     * @return
     */
    public String getAccessToken(String appkey, String appsecret) {
        String getTokenUrl = String.format(getToken + "?appkey=%s&appsecret=%s", appkey, appsecret);
        log.info("ding ding get access token url {}", getToken);
        Map<String, Object> result = restTemplate.getForObject(getTokenUrl, Map.class);
        log.info("ding ding get access token {}, {}", getTokenUrl, result);
        Integer errcode = Integer.valueOf(result.getOrDefault("errcode", -1).toString());
        if (errcode == 0) {
            return result.get("access_token").toString();
        } else {
            log.error("get access token error {}, {}", appkey, result);
            throw new SmsDingDingException(errcode.toString(), "请求 access token 钉钉通道响应异常");
        }
    }

    /**
     * 根据手机号查询钉钉用户 userId
     *
     * @param accessToken
     * @param mobile
     * @return
     */
    public String getUserIdByMobile(String accessToken, String mobile) {
        String getUserIdByMobile = String.format(getUserId + "?access_token=%s&mobile=%s", accessToken, mobile);
        log.info("ding ding get user id by mobile url {}", getUserId);
        Map<String, Object> result = restTemplate.getForObject(getUserIdByMobile, Map.class);
        log.info("ding ding get user id by mobile {}, user id : {}", getUserIdByMobile, result);
        Integer errcode = Integer.valueOf(result.getOrDefault("errcode", -1).toString());
        if (errcode == 0) {
            return result.get("userid").toString();
        } else {
            log.error("ding ding get user id by mobile error {}, {}", mobile, result);
            throw new SmsDingDingException(errcode.toString(),
                    String.format("使用手机号: %s, 请求 user id 钉钉通道响应异常", mobile));
        }
    }

    public Map<String, Object> asyncSendV2(String accessToken, String dingAgentId, String userIdList, String msg) {
        String asyncSendV2Url = String.format(asyncSendV2 + "?access_token=%s", accessToken);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        DingDingWorkMsg workMsg = DingDingWorkMsg.buildWorkMsg(dingAgentId, userIdList, msg);

        HttpEntity<String> httpEntity = new HttpEntity(workMsg, headers);

        log.info("ding ding async send v2 url: {}", asyncSendV2, JsonUtil.toJson(httpEntity));
        Map sendResult = restTemplate.postForObject(asyncSendV2Url, httpEntity, Map.class);
        log.info("ding ding async send v2 url: {}, req body: {}, result: {}",
                asyncSendV2Url, httpEntity, JsonUtil.toJson(sendResult));

        return sendResult;
    }

    /**
     * 对接钉钉接口请求参数封装 Bean
     * {"agent_id":xxxx,"msg":{"msgtype":"text","text":{"content":"hello wxw 555"}},"userid_list":"xxxxxxx"}
     */
    @Data
    @Builder
    static class DingDingWorkMsg {

        private String agent_id;
        private Msg msg;
        private String userid_list;

        public static DingDingWorkMsg buildWorkMsg(String agentId, String userIdList, String msg) {
            DingDingWorkMsg workMsg = DingDingWorkMsg.builder()
                    .agent_id(agentId)
                    .userid_list(userIdList)
                    .msg(DingDingWorkMsg.Msg.builder()
                            .msgtype("text")
                            .text(DingDingWorkMsg.Text.builder()
                                    .content(msg)
                                    .build())
                            .build())
                    .build();
            return workMsg;
        }

        @Data
        @Builder
        static class Msg {
            private String msgtype;
            private Text text;
        }

        @Data
        @Builder
        static class Text {
            private String content;
        }
    }

    public Map<String, Object> robotSendMsg(String token, String secret, List<String> atMobiles, List<String> atUserIds, String msg) {

        long timestamp = System.currentTimeMillis();
        String sing = DingTalkSignUtils.sing(timestamp, secret);

        String robotSendUrl = String.format(robotSend
                        + "?access_token=%s&timestamp=%s&sign=%s"
                , token
                , timestamp
                , sing);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        DingDingRobotMsg robotMsg = DingDingRobotMsg.buildRobotMsg("text", atMobiles, atUserIds, msg);

        HttpEntity<String> httpEntity = new HttpEntity(robotMsg, headers);

        log.info("ding ding robot send msg url: {}", robotSend, JsonUtil.toJson(httpEntity));

        Map sendResult = restTemplate.postForObject(robotSendUrl, httpEntity, Map.class);
        log.info("ding ding robot send msg url: {}, req body: {}, result: {}",
                robotSendUrl, httpEntity, JsonUtil.toJson(sendResult));

        return sendResult;
    }

    /**
     * 对接钉钉自定义机器人通知接口请求参数封装 Bean
     * {"at":{"atMobiles":["180xxxxxx"],"atUserIds":["user123"],"isAtAll":false},"text":{"content":"我就是我, @XXX 是不一样的烟火"},"msgtype":"text"}
     */
    @Data
    @Builder
    static class DingDingRobotMsg {

        private String msgtype;
        private Text text;
        private At at;

        public static DingDingRobotMsg buildRobotMsg(String msgtype, List<String> atMobiles, List<String> atUserIds, String msg) {
            DingDingRobotMsg robotMsg = DingDingRobotMsg.builder()
                    .msgtype(msgtype)
                    .at(At.builder()
                            .atMobiles(atMobiles)
                            .atUserIds(atUserIds)
                            .isAtAll(false)
                            .build())
                    .text(Text.builder()
                            .content(msg)
                            .build())
                    .build();
            return robotMsg;
        }

        @Data
        @Builder
        static class At {
            private List<String> atMobiles;
            private List<String> atUserIds;
            private Boolean isAtAll;
        }

        @Data
        @Builder
        static class Text {
            private String content;
        }
    }
}
