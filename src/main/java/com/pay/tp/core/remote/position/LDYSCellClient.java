package com.pay.tp.core.remote.position;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.CommonConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 联动优势基站信息
 */
@FeignClient(value = CommonConstants.PAYCHANNEL_EUREKA_SERVER_INSTANCE_CORE)
public interface LDYSCellClient {

    /**
     * 基站信息查询
     *
     * @param
     * @return
     */
    @PostMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/otherCommon/baseStationQuery")
    ResultsBean<LDYSCellClient.StationRsp> stationQuery(@RequestBody LDYSCellParam param);


    class LDYSCellParam {

        /**
         * 基站信息
         */
        private String bts;

        public String getBts() {
            return bts;
        }

        public void setBts(String bts) {
            this.bts = bts;
        }

        public String generateBts(String cell, String mcc, String mnc, String lac) {
            return String.join("|", cell, mcc, mnc, lac);
        }

    }

    @Getter
    @Setter
    @ToString
    class StationRsp {

        /**
         * 城市
         */
        private String city;

        /**
         * 省份
         */
        private String province;

        /**
         * 附近周边
         */
        private String poi;

        /**
         * 区域编码
         */
        private String adCode;

        /**
         * 街道
         */
        private String street;

        /**
         * 描述
         */
        private String desc;

        /**
         * 国家
         */
        private String country;

        /**
         * 类型
         */
        private String type;

        /**
         * 坐标点
         */
        private String location;

        /**
         * 道路信息
         */
        private String road;

        /**
         * 搜索半径
         */
        private String radius;

        /**
         * 城市编码
         */
        private String cityCode;
    }
}
