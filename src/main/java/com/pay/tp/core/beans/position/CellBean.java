package com.pay.tp.core.beans.position;


/**基站定位信息
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.beans
 * @Description: TODO
 * @date Date : 2019年01月11日 21:22
 */
public class CellBean {

    /** 区域号 */
    private String dLac;

    /** 基站号 */
    private String dCell;

    /** 经度 */
    private String dLng;

    /** 维度 */
    private String dLat;

    /** 纠偏后的经度 */
    private String oLng;

    /** 纠偏后的维度 */
    private String oLat;

    /** 基站信号覆盖范围（单位：米，半径） */
    private String precision;

    /** 地址 */
    private String address;
    /**
     * 地区编号
     */
    private String adCode;

    public String getdLac() {
        return dLac;
    }

    public void setdLac(String dLac) {
        this.dLac = dLac;
    }

    public String getdCell() {
        return dCell;
    }

    public void setdCell(String dCell) {
        this.dCell = dCell;
    }

    public String getdLng() {
        return dLng;
    }

    public void setdLng(String dLng) {
        this.dLng = dLng;
    }

    public String getdLat() {
        return dLat;
    }

    public void setdLat(String dLat) {
        this.dLat = dLat;
    }

    public String getoLng() {
        return oLng;
    }

    public void setoLng(String oLng) {
        this.oLng = oLng;
    }

    public String getoLat() {
        return oLat;
    }

    public void setoLat(String oLat) {
        this.oLat = oLat;
    }

    public String getPrecision() {
        return precision;
    }

    public void setPrecision(String precision) {
        this.precision = precision;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }

    @Override
    public String toString() {
        return "CellBean{" +
                "dLac='" + dLac + '\'' +
                ", dCell='" + dCell + '\'' +
                ", dLng='" + dLng + '\'' +
                ", dLat='" + dLat + '\'' +
                ", oLng='" + oLng + '\'' +
                ", oLat='" + oLat + '\'' +
                ", precision='" + precision + '\'' +
                ", address='" + address + '\'' +
                ", adCode='" + adCode + '\'' +
                '}';
    }
}
