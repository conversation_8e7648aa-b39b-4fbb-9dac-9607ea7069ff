package com.pay.tp.core.beans.yongyou;

import lombok.Data;

import java.util.Map;

/**
 * 用友添加客户响应Bean
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
public class YongYouCustomerResp {

    /**
     * 成功或失败（数据为空时返回false）
     */
    private Boolean success;

    /**
     * 成功或失败信息
     */
    private String message;

    /**
     * 新增结果
     */
    private CustomerResult data;

    /**
     * 错误码
     */
    private String code;

    @Data
    public static class CustomerResult {
        /**
         * 新增成功的客户的map集合
         * key：甲方客户Id 
         * value：我公司客户id
         */
        private Map<String, String> success;

        /**
         * 新增失败的客户的map集合
         * key：甲方客户Id 
         * value：失败原因
         */
        private Map<String, String> failed;
    }
}
