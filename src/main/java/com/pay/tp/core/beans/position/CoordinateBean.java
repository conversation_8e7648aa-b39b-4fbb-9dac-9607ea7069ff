package com.pay.tp.core.beans.position;


/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.beans
 * @Description: TODO
 * @date Date : 2018年12月25日 16:18
 */
public class CoordinateBean implements  java.io.Serializable{
	private static final long serialVersionUID = 1L;

	/** 结构化地址信息 */
    private String formattedAddress;
    /** 国家编号 */
    private String countryCode;
    /** 省份名称 */
    private String province;
    /** 省份编码 */
    private String provinceCode;
    /** 城市名称 */
    private String city;
    /** 城市编码 */
    private String cityCode;
    /** 6位地区码*/
    private String adCode;

    public String getFormattedAddress() {
        return formattedAddress;
    }

    public void setFormattedAddress(String formattedAddress) {
        this.formattedAddress = formattedAddress;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }

    @Override
    public String toString() {
        return "CoordinateBean{" +
                "formattedAddress='" + formattedAddress + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", province='" + province + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", city='" + city + '\'' +
                ", cityCode='" + cityCode + '\'' +
                ", adCode='" + adCode + '\'' +
                '}';
    }
    
}
