//package com.pay.tp.core.beans.fyt;
//
//import lombok.Data;
//import org.hibernate.validator.constraints.NotBlank;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @description:
// * @date 2023/10/12 14:47
// */
//@Data
//public class FytFindUserRespBean {
//
//    /**
//     * 用户手机号
//     */
//    private String phone;
//
//    /**
//     * 用户名称
//     */
//    private String name;
//
//    /**
//     * 用户会员权限 0非会员 1会员
//     */
//    private String auth;
//}
