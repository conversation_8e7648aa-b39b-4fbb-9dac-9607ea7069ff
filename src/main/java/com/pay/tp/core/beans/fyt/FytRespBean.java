//package com.pay.tp.core.beans.fyt;
//
//import lombok.Data;
//import org.hibernate.validator.constraints.NotBlank;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @description:
// * @date 2023/10/12 14:47
// */
//@Data
//public class FytRespBean {
//
//    /**
//     * 请求状态码
//     * 1-成功 0-失败
//     */
//    private String status;
//
//    /**
//     * msg
//     */
//    private String message;
//
//    /**
//     * 数据
//     */
//    private String data;
//}
