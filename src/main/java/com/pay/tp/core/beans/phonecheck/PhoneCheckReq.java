package com.pay.tp.core.beans.phonecheck;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * 号码检测请求Bean
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@Accessors(chain = true)
public class PhoneCheckReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 请求号（用于幂等性控制）
     */
    @NotBlank(message = "请求号不能为空")
    private String requestNo;

    /**
     * 手机号列表
     */
    @NotEmpty(message = "手机号列表不能为空")
    private List<String> phoneNumbers;

    /**
     * 检测类型
     * EMPTY_NUMBER: 空号检测
     * REAL_NAME: 实名检测
     * RISK_CONTROL: 风控检测
     */
    @NotBlank(message = "检测类型不能为空")
    @Pattern(regexp = "EMPTY_NUMBER|REAL_NAME|RISK_CONTROL", message = "检测类型必须是EMPTY_NUMBER、REAL_NAME或RISK_CONTROL")
    private String checkType;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 品牌标识
     */
    private String brand;

    /**
     * 扩展参数
     */
    private String extParams;

    /**
     * 检测类型枚举
     */
    public static class CheckType {
        /** 空号检测 */
        public static final String EMPTY_NUMBER = "EMPTY_NUMBER";
        /** 实名检测 */
        public static final String REAL_NAME = "REAL_NAME";
        /** 风控检测 */
        public static final String RISK_CONTROL = "RISK_CONTROL";
    }
}
