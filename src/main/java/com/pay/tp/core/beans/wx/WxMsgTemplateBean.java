package com.pay.tp.core.beans.wx;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 微信公众号推送
 */
@Data
public class WxMsgTemplateBean {

    private String touser;             //
    private String template_id;        //
    private String url;                //
    private String client_msg_id;      //
    private Object data;               //
    private Miniprogram miniprogram;   //


    @Data
    @Accessors(chain=true)
    public static class Miniprogram {

        private String appid;

        private String pagepath;

    }

}
