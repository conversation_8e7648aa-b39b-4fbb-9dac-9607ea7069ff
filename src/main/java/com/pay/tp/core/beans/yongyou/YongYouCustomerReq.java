package com.pay.tp.core.beans.yongyou;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import java.util.List;

/**
 * 用友添加客户请求Bean
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
public class YongYouCustomerReq {

    /**
     * 客户列表
     */
    @Valid
    private List<CustomerInfo> customers;

    @Data
    public static class CustomerInfo {
        /**
         * 客户id（必须）
         */
        @NotBlank(message = "客户id不能为空")
        private String customerId;

        /**
         * 客户姓名（必须）
         */
        @NotBlank(message = "客户姓名不能为空")
        private String name;

        /**
         * 客户手机号（可选，根据后台设置）
         */
        private String phone;

        /**
         * 公司
         */
        private String company;

        /**
         * 职位
         */
        private String position;

        /**
         * 跟进人的用户Id
         */
        private String userId;

        /**
         * 性别 男/女
         */
        private String gender;

        /**
         * 生日
         */
        private String birthday;

        /**
         * 座机
         */
        private String telephone;

        /**
         * qq
         */
        private String qq;

        /**
         * 微信号
         */
        private String wechat;

        /**
         * 地址
         */
        private String address;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 客户分配：0分配到客户列表（缺省），1分配到公海
         */
        private String from;

        /**
         * 公海id
         */
        private String seaId;

        /**
         * 备注
         */
        private String remark;

        /**
         * 客户来源（可自定义）
         */
        private String dataSource;

        /**
         * 企业性质
         */
        private String econKind;

        /**
         * 公司地址
         */
        private String comAdress;

        /**
         * 注册资本
         */
        private String registCapi;

        /**
         * 经营范围
         */
        private String scope;

        /**
         * 企业法人
         */
        private String operName;

        /**
         * 公司类型
         */
        private String comType;

        /**
         * 员工数量
         */
        private String employeesNum;

        /**
         * 行业
         */
        private String industry;

        /**
         * 主营产品
         */
        private String mainProduce;

        /**
         * 公司简介
         */
        private String comInfo;

        /**
         * 协作人的用户Id数组
         */
        private List<String> collaborators;

        /**
         * 自定义字段
         */
        private List<CustomFieldValue> customFieldValues;
    }

    @Data
    public static class CustomFieldValue {
        /**
         * 自定义字段id（必须）
         */
        private Integer id;

        /**
         * 自定义字段的值（数组），如果多选字段可传多个，文本类型的值长度不能超过400（必须）
         */
        private List<String> values;

        /**
         * 自定义字段类型，取自 查询自定义字段设置（/open/customer/customFields） 接口的type字段（必须）
         */
        private String fieldType;

        /**
         * 当fieldType=6 既其他联系方式类型时，传此参数
         */
        private List<MultiContact> multiContacts;
    }

    @Data
    public static class MultiContact {
        /**
         * 取自 查询自定义字段设置（/open/customer/customFields） 接口的options选项数据的id（必须）
         */
        private String optionId;

        /**
         * 具体其他联系方式号码（必须）
         */
        private String optionText;
    }
}
