package com.pay.tp.core.beans.position;


/**ip定位信息
 * <AUTHOR> z<PERSON><PERSON>an
 * @Package com.pay.position.core.beans
 * @Description: TODO
 * @date Date : 2019年01月12日 14:05
 */
public class IpBean {
    
	/**所在大陆*/
    private String land;
    /**国家*/
    private String country;
    /**城市*/
    private String city;
    /**省份*/
    private String prov;
    /**省份编号*/
    private String provCode;
    /***/
    private String dist;
    /**ISP*/
    private String isp;
    /**邮编*/
    private String zipCode;
    /**国家(英文)*/
    private String english;
    /**国家(编码)*/
    private String cc;
    /**经度*/
    private String longitude;
    /**纬度*/
    private String latitude;
    /**开始ip*/
    private String beginIp;
    /**结束ip*/
    private String endIp;
    /***/
    private String area;

    public String getLand() {
        return land;
    }

    public void setLand(String land) {
        this.land = land;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProv() {
        return prov;
    }

    public void setProv(String prov) {
        this.prov = prov;
    }

    public String getDist() {
        return dist;
    }

    public void setDist(String dist) {
        this.dist = dist;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getEnglish() {
        return english;
    }

    public void setEnglish(String english) {
        this.english = english;
    }

    public String getCc() {
        return cc;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getBeginIp() {
        return beginIp;
    }

    public void setBeginIp(String beginIp) {
        this.beginIp = beginIp;
    }

    public String getEndIp() {
        return endIp;
    }

    public void setEndIp(String endIp) {
        this.endIp = endIp;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getProvCode() {
        return provCode;
    }

    public void setProvCode(String provCode) {
        this.provCode = provCode;
    }

	@Override
	public String toString() {
		return "IpBean [land=" + land + ", country=" + country + ", city=" + city + ", prov=" + prov + ", provCode="
				+ provCode + ", dist=" + dist + ", isp=" + isp + ", zipCode=" + zipCode + ", english=" + english
				+ ", cc=" + cc + ", longitude=" + longitude + ", latitude=" + latitude + ", beginIp=" + beginIp
				+ ", endIp=" + endIp + ", area=" + area + "]";
	}
    
    
}
