package com.pay.tp.core.beans.phonecheck;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 号码检测响应Bean
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@Accessors(chain = true)
public class PhoneCheckResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 请求号
     */
    private String requestNo;

    /**
     * 响应码
     * 0: 成功
     * -1: 失败
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 检测结果列表
     */
    private List<PhoneCheckResult> results;

    /**
     * 总检测数量
     */
    private Integer totalCount;

    /**
     * 成功检测数量
     */
    private Integer successCount;

    /**
     * 失败检测数量
     */
    private Integer failCount;

    /**
     * 检测耗时（毫秒）
     */
    private Long costTime;

    /**
     * 号码检测结果
     */
    @Data
    @Accessors(chain = true)
    public static class PhoneCheckResult implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 手机号
         */
        private String phoneNumber;

        /**
         * 检测状态
         * 1: 正常
         * 0: 空号/异常
         * -1: 检测失败
         */
        private Integer status;

        /**
         * 状态描述
         */
        private String statusDesc;

        /**
         * 运营商
         * CMCC: 中国移动
         * CUCC: 中国联通
         * CTCC: 中国电信
         */
        private String carrier;

        /**
         * 归属地省份
         */
        private String province;

        /**
         * 归属地城市
         */
        private String city;

        /**
         * 号码类型
         * 1: 手机号
         * 2: 固话
         * 3: 虚拟号
         */
        private Integer numberType;

        /**
         * 风险等级（风控检测时使用）
         * LOW: 低风险
         * MEDIUM: 中风险
         * HIGH: 高风险
         */
        private String riskLevel;

        /**
         * 实名状态（实名检测时使用）
         * 1: 已实名
         * 0: 未实名
         * -1: 无法确定
         */
        private Integer realNameStatus;

        /**
         * 检测时间
         */
        private String checkTime;

        /**
         * 错误信息
         */
        private String errorMsg;
    }

    /**
     * 响应码常量
     */
    public static class ResponseCode {
        /** 成功 */
        public static final String SUCCESS = "0";
        /** 失败 */
        public static final String FAIL = "-1";
    }

    /**
     * 号码状态常量
     */
    public static class PhoneStatus {
        /** 正常 */
        public static final Integer NORMAL = 1;
        /** 空号/异常 */
        public static final Integer EMPTY = 0;
        /** 检测失败 */
        public static final Integer FAIL = -1;
    }

    /**
     * 运营商常量
     */
    public static class Carrier {
        /** 中国移动 */
        public static final String CMCC = "CMCC";
        /** 中国联通 */
        public static final String CUCC = "CUCC";
        /** 中国电信 */
        public static final String CTCC = "CTCC";
    }

    /**
     * 风险等级常量
     */
    public static class RiskLevel {
        /** 低风险 */
        public static final String LOW = "LOW";
        /** 中风险 */
        public static final String MEDIUM = "MEDIUM";
        /** 高风险 */
        public static final String HIGH = "HIGH";
    }
}
