package com.pay.tp.core.beans.tencent;

import java.io.Serializable;

import lombok.Data;
import lombok.ToString;

/**
 *
 */
@Data
@ToString
public class AccessTokenRes implements Serializable{


	private static final long serialVersionUID = 1L;
	private String code	;	//0：成功 非0：失败 详情请参见 SaaS 服务错误码
	private String msg	;	//请求结果描述
	private String transactionTime	;//	调用接口的时间
	private String access_token	;//	access_token 的值
	private String expire_time	;//	access_token 失效的绝对时间
	private int expire_in	;	//access_token 的最大生存时间

	
	public boolean success() {
		return "0".equals(code);

	}
}
