package com.pay.tp.core.beans.sms;

import java.util.Date;

import javax.validation.constraints.NotNull;

import org.springframework.beans.BeanUtils;

import com.pay.tp.core.entity.msgmanage.MsgChannelConfig;
import com.pay.tp.core.enums.Status;

import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 通道配置 DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SmsMsgChannelConfigDto {
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 配置状态
     * ENABLE
     * DISABLE
     */
    @NotNull
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 通道编号
     */
    private String channelCode;

    /**
     * 配置参数名
     */
    private String cfgParam;

    /**
     * 配置参数值
     */
    private String cfgValue;

    public SmsMsgChannelConfigDto convertFor(MsgChannelConfig channelConfig) {
        SmsMsgChannelConfigDto dto = new SmsMsgChannelConfigDto();
        BeanUtils.copyProperties(channelConfig, dto);
        dto.setStatus(channelConfig.getStatus().name());
        return dto;
    }

    public MsgChannelConfig convertToSmsMsgChannelConfig(SmsMsgChannelConfigDto channelConfigDto) {
        MsgChannelConfig channelConfig = new MsgChannelConfig();
        BeanUtils.copyProperties(channelConfigDto, channelConfig);
        channelConfig.setStatus(Status.valueOf(channelConfigDto.getStatus()));
        return channelConfig;
    }
}