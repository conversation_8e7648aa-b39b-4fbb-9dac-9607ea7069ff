package com.pay.tp.core.beans.auth;

import lombok.Data;

/**
 * @Description: 银行卡鉴权信息返回
 * @see: BankCardRes 此处填写需要参考的类
 * @version Nov 20, 2019 11:56:04 AM
 * <AUTHOR>
 */
@Data
public class BankCardRes {

	private long id;
	private String rspCod; // 返回编码
	private String rspMsg; // 返回结果
	private String bankName; // 银行名称
	private String validateStatus; // 验证状态
	private String oriTransDate; // 交易时间
	private String paySerialNo; // 支付序列号
	private String remark; // 描述
	  /**
    卡类型：
1-借记卡 
2-信用卡 
3-预付费卡 
0-其他
*/
    private String cardType;
    private String cooperSerialNo;//接入方交易请求流水号
    
	private String channelRspCod; // 通道返回码
}
