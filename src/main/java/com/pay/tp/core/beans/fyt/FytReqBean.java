//package com.pay.tp.core.beans.fyt;
//
//import lombok.Data;
//import lombok.experimental.Accessors;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @description:
// * @date 2023/10/12 14:47
// */
//@Data
//@Accessors(chain = true)
//public class FytReqBean {
//
//    /**
//     * 请求的加密数据
//     */
//    private String data;
//
//    /**
//     * 加密时16位向量
//     */
//    private String iv;
//    private String timestamp;
//    private String sign;
//}
