package com.pay.tp.core.beans;

import org.hibernate.validator.constraints.NotBlank;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper=true)
@ToString(callSuper=true)
public class BaiDuPersonVerifyBean extends BaiDuOcrBean{

    @NotBlank(message = "身份证号不能为空")
    private String identityNo;

    @NotBlank(message = "姓名不能为空")
    private String legalPerson;
    /**
     * 服务商编号
     */
    private String userNo;
    /**
     * 品牌
     */
    private String brand;

}
