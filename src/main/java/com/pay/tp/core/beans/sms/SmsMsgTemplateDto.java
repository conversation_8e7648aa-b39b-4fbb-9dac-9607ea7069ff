package com.pay.tp.core.beans.sms;

import java.util.Date;

import javax.validation.constraints.NotNull;

import org.springframework.beans.BeanUtils;

import com.pay.tp.core.entity.msgmanage.MsgTemplate;
import com.pay.tp.core.enums.Status;

import lombok.Data;

/**
 * 消息模板DTO
 *
 * <AUTHOR>
 */
@Data
public class SmsMsgTemplateDto {
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 消息模板
     */
    private String msgTemplate;

    /**
     * 状态
     */
    @NotNull
    private String status;

    /**
     * 消息模板名称
     */
    private String templateName;

    /**
     * 消息模板编号
     */
    private String templateCode;

    /**
     * 消息通道编号
     */
    private String channelCode;

    /**
     * 用户组号
     */
    private String userGroupNo;

    /**
     * 模板描述
     */
    private String desc;

    /**
     * 扩展消息
     */
    private String extInfo;

    public SmsMsgTemplateDto convertFor(MsgTemplate template) {
        SmsMsgTemplateDto dto = new SmsMsgTemplateDto();
        BeanUtils.copyProperties(template, dto);
        dto.setStatus(template.getStatus().name());
        return dto;
    }

    public MsgTemplate convertToSmsMsgTemplate(SmsMsgTemplateDto templateDto) {
        MsgTemplate template = new MsgTemplate();
        BeanUtils.copyProperties(templateDto, template);
        template.setStatus(Status.valueOf(templateDto.getStatus()));
        return template;
    }
}
