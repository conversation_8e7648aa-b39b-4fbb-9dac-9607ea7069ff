package com.pay.tp.core.service.msgmanage;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pay.tp.core.entity.msgmanage.MsgChannel;
import com.pay.tp.core.entity.msgmanage.MsgChannelConfig;
import com.pay.tp.core.exception.SmsOptimisticException;
import com.pay.tp.core.mapper.msgmanage.MsgChannelMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息通道服务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MsgChannelService {

    @Autowired
    private MsgChannelMapper msgChannelMapper;

    @Autowired
    private MsgChannelConfigService msgChannelConfigService;

    public MsgChannel findByCode(String channelCode) {
        MsgChannel channel = msgChannelMapper.findByCode(channelCode);
//        log.info("find channel by channel code: {}, channel: {}",
//                channelCode, channel);
        return channel;
    }

    public void updateByPrimaryKey(MsgChannel channel) {
        channel.setUpdateTime(new Date());
        int i = msgChannelMapper.updateByPrimaryKey(channel);
        if (i != 1) {
            throw new SmsOptimisticException("99", "更新失败,有其他变更先于此次变更");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(MsgChannel channel, List<MsgChannelConfig> configs) {
        channel.setCreateTime(new Date()).setOptimistic(0L);
        msgChannelMapper.insert(channel);
        msgChannelConfigService.batchInsert(configs);
    }

    /**
     * 指定类型查询所有已开启的通道
     *
     * @param type
     * @return
     */
    public List<MsgChannel> findByType(String type) {
        return msgChannelMapper.findByType(type);
    }

    /**
     * 多条件查询数据【定义给前端使用】
     *
     * @param params
     * @return
     */
    public List<MsgChannel> findByParams(Map<String, Object> params) {
        return msgChannelMapper.findByParams(params);
    }

    /**
     * 指定ID查询通道
     *
     * @param id
     * @return
     */
    public MsgChannel findById(Long id) {
        return msgChannelMapper.selectByPrimaryKey(id);
    }
}
