package com.pay.tp.core.service.yongyou;

import com.pay.tp.core.entity.yongyou.YongYouWorkPhoneBusiness;
import com.pay.tp.core.mapper.yongyou.YongYouWorkPhoneBusinessMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 用友工作手机业务服务类
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class YongYouWorkPhoneBusinessService {

    @Autowired
    private YongYouWorkPhoneBusinessMapper yongYouWorkPhoneBusinessMapper;

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 用友工作手机业务记录
     */
    public YongYouWorkPhoneBusiness findById(Long id) {
        return yongYouWorkPhoneBusinessMapper.selectById(id);
    }

    /**
     * 插入记录
     *
     * @param record 用友工作手机业务记录
     * @return 影响行数
     */
    @Transactional
    public int insert(YongYouWorkPhoneBusiness record) {
        return yongYouWorkPhoneBusinessMapper.insert(record);
    }

    /**
     * 更新记录
     *
     * @param record 用友工作手机业务记录
     * @return 影响行数
     */
    @Transactional
    public int update(YongYouWorkPhoneBusiness record) {
        return yongYouWorkPhoneBusinessMapper.updateByPrimaryKey(record);
    }

    /**
     * 查询需要推送的数据
     * 条件：YONGYOU_PUSH_STATUS = 'INIT' 或 'FAIL'，PUSH_RETURN_NUM < 3，VERIFY_STATUS = 'STATUS'
     *
     * @param limit 查询条数限制
     * @return 需要推送的记录列表
     */
    public List<YongYouWorkPhoneBusiness> findPendingPushRecords(int limit) {
        log.info("查询需要推送的用友数据，限制条数: {}", limit);
        List<YongYouWorkPhoneBusiness> records = yongYouWorkPhoneBusinessMapper.findPendingPushRecords(limit);
        log.info("查询到需要推送的记录数: {}", records.size());
        return records;
    }

    /**
     * 更新推送状态和推送次数
     *
     * @param id                主键ID
     * @param pushStatus        推送状态
     * @param pushReturnValue   推送返回值
     * @param pushReturnMessage 推送返回信息
     * @return 影响行数
     */
    @Transactional
    public int updatePushStatus(Long id, String pushStatus, String pushReturnValue, String pushReturnMessage) {
        log.info("更新推送状态，ID: {}, 状态: {}, 返回值: {}, 返回信息: {}",
                id, pushStatus, pushReturnValue, pushReturnMessage);
        return yongYouWorkPhoneBusinessMapper.updatePushStatus(id, pushStatus, pushReturnValue, pushReturnMessage);
    }

    /**
     * 增加推送次数
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Transactional
    public int incrementPushReturnNum(Long id) {
        log.info("增加推送次数，ID: {}", id);
        return yongYouWorkPhoneBusinessMapper.incrementPushReturnNum(id);
    }

    /**
     * 处理推送成功的记录
     *
     * @param id                主键ID
     * @param pushReturnValue   推送返回值
     * @param pushReturnMessage 推送返回信息
     */
    @Transactional
    public void handlePushSuccess(Long id, String pushReturnValue, String pushReturnMessage) {
        // 先增加推送次数
        incrementPushReturnNum(id);
        // 更新推送状态为成功
        updatePushStatus(id, YongYouWorkPhoneBusiness.PushStatus.SUCCESS, pushReturnValue, pushReturnMessage);
        log.info("处理推送成功，ID: {}", id);
    }

    /**
     * 处理推送失败的记录
     *
     * @param id                主键ID
     * @param pushReturnValue   推送返回值
     * @param pushReturnMessage 推送返回信息
     */
    @Transactional
    public void handlePushFail(Long id, String pushReturnValue, String pushReturnMessage) {
        // 先增加推送次数
        incrementPushReturnNum(id);
        // 更新推送状态为失败
        updatePushStatus(id, YongYouWorkPhoneBusiness.PushStatus.FAIL, pushReturnValue, pushReturnMessage);
        log.info("处理推送失败，ID: {}", id);
    }

    /**
     * 批量增加推送次数
     *
     * @param ids 主键ID列表
     * @return 影响行数
     */
    @Transactional
    public int batchIncrementPushReturnNum(List<YongYouWorkPhoneBusiness> pendingRecords) {
        if (pendingRecords == null || pendingRecords.isEmpty()) {
            return 0;
        }

        // 批量更新推送次数
        List<Long> ids = new ArrayList<>();
        for (YongYouWorkPhoneBusiness record : pendingRecords) {
            ids.add(record.getId());
        }

        return incrementPushReturnNumBatch(ids);
    }

    private int incrementPushReturnNumBatch(List<Long> batchIds) {
        return yongYouWorkPhoneBusinessMapper.batchIncrementPushReturnNum(batchIds);
    }

    /**
     * 批量更新推送状态
     *
     * @param statusUpdates 状态更新列表
     * @return 影响行数
     */
    @Transactional
    public int batchUpdatePushStatus(List<PushStatusUpdate> statusUpdates) {
        if (statusUpdates == null || statusUpdates.isEmpty()) {
            return 0;
        }

        log.info("批量更新推送状态，记录数: {}", statusUpdates.size());
        int totalCount = 0;

        for (PushStatusUpdate update : statusUpdates) {
            totalCount += updatePushStatus(update.getId(), update.getPushStatus(),
                    update.getPushReturnValue(), update.getPushReturnMessage());
        }

        log.info("批量更新推送状态完成，总影响行数: {}", totalCount);
        return totalCount;
    }

    public void updatePushStatusBatch(List<String> ids, String pushStatus, String returnValue, String returnMessage) {
        int i = yongYouWorkPhoneBusinessMapper.updatePushStatusBatch(ids, pushStatus, returnValue, returnMessage);
    }

    /**
     * 推送状态更新对象
     */
    public static class PushStatusUpdate {
        private Long id;
        private String pushStatus;
        private String pushReturnValue;
        private String pushReturnMessage;

        public PushStatusUpdate(Long id, String pushStatus, String pushReturnValue, String pushReturnMessage) {
            this.id = id;
            this.pushStatus = pushStatus;
            this.pushReturnValue = pushReturnValue;
            this.pushReturnMessage = pushReturnMessage;
        }

        // Getters
        public Long getId() {
            return id;
        }

        public String getPushStatus() {
            return pushStatus;
        }

        public String getPushReturnValue() {
            return pushReturnValue;
        }

        public String getPushReturnMessage() {
            return pushReturnMessage;
        }
    }
}
