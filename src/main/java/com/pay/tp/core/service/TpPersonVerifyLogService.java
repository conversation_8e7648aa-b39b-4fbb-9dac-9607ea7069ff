package com.pay.tp.core.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pay.tp.core.entity.TpPersonVerifyLog;
import com.pay.tp.core.mapper.TpPersonVerifyLogMapper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TpPersonVerifyLogService {
	@Autowired
	TpPersonVerifyLogMapper tpPersonVerifyLogMapper;
	@Transactional(rollbackFor = Exception.class)
	public void insert(TpPersonVerifyLog tpPersonVerifyLog) {
		tpPersonVerifyLog.setCreateTime(new Date());
		tpPersonVerifyLogMapper.insert(tpPersonVerifyLog);
	}

}
