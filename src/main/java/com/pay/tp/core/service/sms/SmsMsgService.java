package com.pay.tp.core.service.sms;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.util.HiddenInfoUtils;
import com.pay.tp.core.beans.sms.SmsParam;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.entity.sms.SmsRes;
import com.pay.tp.core.mapper.sms.SmsMsgMapper;
import com.pay.tp.core.mapper.sms.SmsResMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 短信信息记录
 */
@Service
public class SmsMsgService {

	@Autowired
	private SmsMsgMapper smsMsgMapper;

	
	/**
	 * 查询  ； 查不到添加
	 * @param param
	 * @return
	 */
	public SmsMsg query(SmsParam param) {
		SmsMsg o=smsMsgMapper.query(param);
		if (o!=null) {
			return o;
		}else {
			SmsMsg sms=new SmsMsg(param.getRequestNo(),"", param.getPhone(), param.getContent());
			sms.setOptismistic(0L);
			sms.setBrand(Optional.ofNullable(param.getBrand()).orElse(Brand.PLUS.name()));
			sms.setCreateTime(new Date());
			sms.setHiddenPhone(HiddenInfoUtils.hidePhoneNo(param.getPhone()));
			smsMsgMapper.insert(sms);
			return sms;
		}
	}

	/**
	 * 更新
	 * @param sms
	 */
	public void update(SmsMsg sms) {
		sms.setUpdateTime(new Date());
		smsMsgMapper.update(sms);
	}
	

	/**
	 * @ 根据id查询
	 * @param id
	 */
	public SmsMsg findById(Long id) {
		return smsMsgMapper.findById(id);
	}

	/**
	 * 分页查询
	 */
	public PageInfo<Map<String, Object>> findByPageAll(int pageNum, int pageSize, Map<String, Object> queryParams) {
		PageHelper.startPage(pageNum, pageSize, true);
		List<Map<String, Object>> list = smsMsgMapper.findByPageAll(queryParams);

		PageInfo<Map<String, Object>> page = new PageInfo<>(list);
		return page;
	}


	@Transactional
	public void updateResMsg(SmsMsg smsMsg) {
		smsMsg.setUpdateTime(new Date());
		smsMsgMapper.updateResMsg(smsMsg);
	}

	/**
	 * 根据回执id 和手机号查询
	 * @param phone
	 * @param msgId
	 * @return
	 */
	public SmsMsg findByMsgIdPhone(String phone, String msgId) {
		return smsMsgMapper.findByMsgIdPhone(phone, msgId);
	}


}
