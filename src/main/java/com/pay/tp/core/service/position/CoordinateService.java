package com.pay.tp.core.service.position;

import com.pay.tp.core.beans.position.CoordinateParam;
import com.pay.tp.core.entity.position.Coordinate;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.service
 * @Description: TODO
 * @date Date : 2018年12月25日 16:36
 */
public interface CoordinateService {
    
	Coordinate query(CoordinateParam param);

    void update(Coordinate o);
    
}
