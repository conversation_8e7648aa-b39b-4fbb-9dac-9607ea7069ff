package com.pay.tp.core.service.sms;

import com.pay.tp.core.remote.sms.*;
import com.pay.tp.core.remote.sms.aliyun.AliyunSmsClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.serivce
 * @Description: TODO
 * @date Date : 2018年12月25日 14:22
 */
@Component
public class SmsChannelContext {
    @Autowired
    private XuanWuClient xuanWuClient;
    @Autowired
    private AoZhongUkAgentClient aoZhongUkAgentClient;
    @Autowired
    private XuanWuUkAgentClient xuanWuUkAgentClient;
    //    @Autowired
//    private XiaoMaClient xiaoMaClient;
    @Autowired
    private LDYSSmsProxy ldysSmsProxy;
    @Autowired
    private AoZhongClient aoZhongClient;
    @Autowired
    private MockSmsClient mockSmsClient;
    @Autowired
    private AliyunSmsClient aliyunSmsClient;

    private Map<String, SmsClient> map = new HashMap<>();

    @PostConstruct
    public void init() {
        map.put("xuanwuUkAgent", xuanWuUkAgentClient);
        map.put("aozhongUkAgent", aoZhongUkAgentClient);
        map.put("xuanwu", xuanWuClient);
//        map.put("xiaoma", xiaoMaClient);
        map.put("ldyssms", ldysSmsProxy);
        map.put("aozhong", aoZhongClient);
        map.put("mock", mockSmsClient);
        map.put("aliyunUkAgent", aliyunSmsClient);
    }

    public SmsClient get(String code) {
        return map.get(code);
    }
}
