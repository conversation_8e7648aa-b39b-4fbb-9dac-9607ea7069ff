package com.pay.tp.core.service.position;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.beans.position.IpParam;
import com.pay.tp.core.entity.position.Ip;
import com.pay.tp.core.mapper.position.IpMapper;


/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.service
 * @Description: TODO
 * @date Date : 2018年12月24日 14:40
 */
@Service
public class IpServiceImpl implements  IpService{
    
	@Autowired
    private IpMapper ipMapper;

    @Override
    public Ip query(IpParam param) {
        Ip o=ipMapper.query(param);
        if (o!=null) {
            return o;
        }else {
            Ip ip = new Ip(param.getRequestNo(), "", param.getIp());
            ipMapper.insert(ip);
            return ip;
        }
    }


    @Override
    public void update(Ip ip) {
        ipMapper.update(ip);
    }

}
