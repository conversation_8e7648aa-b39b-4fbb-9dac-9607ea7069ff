package com.pay.tp.core.service.sms;

import java.util.List;

import com.pay.tp.core.enums.Status;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.entity.sms.SmsChannel;
import com.pay.tp.core.mapper.sms.SmsChannelMapper;

/**
 * 短信通道
 */
@Service
public class SmsChannelService {
	
	@Autowired
    private SmsChannelMapper smsChannelMapper;

	
	/***
	 * 
	 */
	public List<SmsChannel> findValid(String channelType) {
		return smsChannelMapper.findActive(channelType);
	}
	
	
    /**
     * 根据状态查询短信通道
     *
     * @param channelType
     * @param status
     */
    public List<SmsChannel> findByStatus(String channelType, String status){
        return smsChannelMapper.findByStatus(channelType, status);
    }

    /**
     * 根据id查询短信通道
     * @param id
     * @return
     */
    public SmsChannel findById(String id){
        return smsChannelMapper.findById(id);
    }

    /**
     * 根据id更新短信通道状态
     * @param status
     * @param id
     * @return
     */
    public int updateStatusById(@Param("status")String status,@Param("id")Long id){
        return smsChannelMapper.updateStatusById(status, id);
    }

    /**
     * 更新所有启用的为禁用状态
     * @return
     */
    public int updateDisableAll(String channelType){
        return smsChannelMapper.updateDisableAll(channelType);
    }

    /**
     * 查询所有
     * @return
     */
    public List<SmsChannel> findAll(){
        return smsChannelMapper.findAll();
    }
    
    
}
