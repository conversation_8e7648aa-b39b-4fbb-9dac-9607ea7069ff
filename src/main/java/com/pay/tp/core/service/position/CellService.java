package com.pay.tp.core.service.position;

import java.util.List;
import java.util.Map;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.position.CellParam;
import com.pay.tp.core.entity.position.Cell;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.service
 * @Description: TODO
 * @date Date : 2018年12月24日 14:36
 */
public interface CellService {
    
	Cell query(CellParam param);

    void update(Cell cell);
    
    void updateByCell(Cell cell);
    
    ResultsBean<List<Map<String, String>>> queryByLatLng(String latLng);

}
