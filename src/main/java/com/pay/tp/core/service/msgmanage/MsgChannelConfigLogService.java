package com.pay.tp.core.service.msgmanage;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.entity.msgmanage.MsgChannelConfig;
import com.pay.tp.core.entity.msgmanage.MsgChannelConfigLog;
import com.pay.tp.core.exception.SmsDingDingException;
import com.pay.tp.core.mapper.msgmanage.MsgChannelConfigLogMapper;
import com.pay.tp.core.utils.JsonUtil;

/**
 * 信息通道配置变更记录 服务层
 *
 * <AUTHOR>
 */
@Service
public class MsgChannelConfigLogService {

    @Autowired
    private MsgChannelConfigLogMapper msgChannelConfigLogMapper;

    public void insert(MsgChannelConfigLog msgChannelConfigLog) {
        msgChannelConfigLogMapper.insert(msgChannelConfigLog);
    }

    public void save(MsgChannelConfig before, MsgChannelConfig after) {
        if (before.getId().compareTo(after.getId()) != 0) {
            throw new SmsDingDingException("99", "save sms msg channel config log error, before and after id is diff !!!");
        }

        MsgChannelConfigLog configLog = new MsgChannelConfigLog();
        configLog.setCreateTime(new Date())
                .setOperator("SYSTEM")
                .setBefore(JsonUtil.toJson(before))
                .setAfter(JsonUtil.toJson(after))
                .setChannelConfigId(before.getId());
        msgChannelConfigLogMapper.insert(configLog);
    }
}
