package com.pay.tp.core.service.msgmanage;

import com.pay.tp.core.entity.msgmanage.MsgRecord;
import com.pay.tp.core.entity.msgmanage.MsgUser;
import com.pay.tp.core.mapper.msgmanage.MsgRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 消息记录服务层
 *
 * <AUTHOR>
 */
@Service
public class MsgRecordService {


    @Autowired
    private MsgRecordMapper msgRecordMapper;


    public MsgRecord build(String requestNo, MsgUser user, Long msgTemplateId, String msgContent, String jsonParam) {
        MsgRecord record = new MsgRecord();
        record.setChannelCode(user.getChannelCode())
                .setChannelUserNo(user.getChannelUserNo())
                .setContent(msgContent)
                .setRequestNo(requestNo)
                .setPhone(user.getPhoneNo())
                .setMsgTemplateId(msgTemplateId)
                .setMsgTemplateParam(jsonParam)
                .setUserName(user.getUserName());
        return record;
    }

    public MsgRecord build(String requestNo,String channelCode, Long msgTemplateId, String msgContent, String jsonParam) {
        MsgRecord record = new MsgRecord();
        record.setChannelCode(channelCode)
                .setContent(msgContent)
                .setRequestNo(requestNo)
                .setMsgTemplateId(msgTemplateId)
                .setMsgTemplateParam(jsonParam);
        return record;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<MsgRecord> recordList) {
        recordList.forEach(r -> {
            r.setCreateTime(new Date()).setOptimistic(0L);
            msgRecordMapper.insert(r);
        });
    }


    @Transactional(rollbackFor = Exception.class)
    public void insert(MsgRecord record) {
        record.setCreateTime(new Date()).setOptimistic(0L);
        msgRecordMapper.insert(record);
    }

    public List<MsgRecord> findByParams(Map<String, Object> params) {
        return msgRecordMapper.findByParams(params);
    }

    public MsgRecord findById(Long id) {
        return msgRecordMapper.selectByPrimaryKey(id);
    }

    public List<MsgRecord> findByRequestNo(String requestNo) {
        return msgRecordMapper.findByRequestNo(requestNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteDingMsg() {
        msgRecordMapper.deleteDingMsg();
    }
}