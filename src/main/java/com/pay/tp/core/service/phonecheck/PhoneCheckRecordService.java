package com.pay.tp.core.service.phonecheck;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.tp.core.entity.phonecheck.PhoneCheckRecord;
import com.pay.tp.core.mapper.phonecheck.PhoneCheckRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 号码检测记录服务类
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class PhoneCheckRecordService {

    @Autowired
    private PhoneCheckRecordMapper phoneCheckRecordMapper;

    /**
     * 根据主键查询
     * @param id 主键ID
     * @return 号码检测记录
     */
    public PhoneCheckRecord findById(Long id) {
        return phoneCheckRecordMapper.selectByPrimaryKey(id);
    }

    /**
     * 插入记录
     * @param record 号码检测记录
     * @return 影响行数
     */
    @Transactional
    public int insert(PhoneCheckRecord record) {
        if (record.getCreateTime() == null) {
            record.setCreateTime(new Date());
        }
        if (record.getUpdateTime() == null) {
            record.setUpdateTime(new Date());
        }
        return phoneCheckRecordMapper.insert(record);
    }

    /**
     * 更新记录
     * @param record 号码检测记录
     * @return 影响行数
     */
    @Transactional
    public int update(PhoneCheckRecord record) {
        record.setUpdateTime(new Date());
        return phoneCheckRecordMapper.updateByPrimaryKey(record);
    }

    /**
     * 根据请求号和手机号查询记录
     * @param requestNo 请求号
     * @param phoneNumber 手机号
     * @return 号码检测记录
     */
    public PhoneCheckRecord findByRequestNoAndPhone(String requestNo, String phoneNumber) {
        return phoneCheckRecordMapper.findByRequestNoAndPhone(requestNo, phoneNumber);
    }

    /**
     * 根据请求号查询所有记录
     * @param requestNo 请求号
     * @return 号码检测记录列表
     */
    public List<PhoneCheckRecord> findByRequestNo(String requestNo) {
        return phoneCheckRecordMapper.findByRequestNo(requestNo);
    }

    /**
     * 根据手机号查询最近的检测记录
     * @param phoneNumber 手机号
     * @param checkType 检测类型
     * @param limit 查询数量限制
     * @return 号码检测记录列表
     */
    public List<PhoneCheckRecord> findRecentByPhone(String phoneNumber, String checkType, int limit) {
        return phoneCheckRecordMapper.findRecentByPhone(phoneNumber, checkType, limit);
    }

    /**
     * 批量插入记录
     * @param records 号码检测记录列表
     * @return 影响行数
     */
    @Transactional
    public int batchInsert(List<PhoneCheckRecord> records) {
        if (records == null || records.isEmpty()) {
            return 0;
        }
        
        Date now = new Date();
        for (PhoneCheckRecord record : records) {
            if (record.getCreateTime() == null) {
                record.setCreateTime(now);
            }
            if (record.getUpdateTime() == null) {
                record.setUpdateTime(now);
            }
        }
        
        log.info("批量插入号码检测记录，数量: {}", records.size());
        return phoneCheckRecordMapper.batchInsert(records);
    }

    /**
     * 分页查询记录
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param params 查询参数
     * @return 分页结果
     */
    public PageInfo<Map<String, Object>> findByPageAll(int pageNum, int pageSize, Map<String, Object> params) {
        PageHelper.startPage(pageNum, pageSize, true);
        List<Map<String, Object>> list = phoneCheckRecordMapper.findByPageAll(params);
        return new PageInfo<>(list);
    }

    /**
     * 统计检测记录数量
     * @param params 查询参数
     * @return 记录数量
     */
    public int countByParams(Map<String, Object> params) {
        return phoneCheckRecordMapper.countByParams(params);
    }

    /**
     * 根据商户编号统计检测数量
     * @param merchantNo 商户编号
     * @param checkType 检测类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public Map<String, Object> countByMerchant(String merchantNo, String checkType, String startTime, String endTime) {
        return phoneCheckRecordMapper.countByMerchant(merchantNo, checkType, startTime, endTime);
    }

    /**
     * 删除过期记录
     * @param expireTime 过期时间
     * @return 影响行数
     */
    @Transactional
    public int deleteExpiredRecords(String expireTime) {
        log.info("删除过期号码检测记录，过期时间: {}", expireTime);
        int count = phoneCheckRecordMapper.deleteExpiredRecords(expireTime);
        log.info("删除过期号码检测记录完成，删除数量: {}", count);
        return count;
    }

    /**
     * 检查是否存在有效的检测记录
     * @param phoneNumber 手机号
     * @param checkType 检测类型
     * @param validHours 有效小时数
     * @return 是否存在有效记录
     */
    public boolean hasValidRecord(String phoneNumber, String checkType, int validHours) {
        List<PhoneCheckRecord> records = findRecentByPhone(phoneNumber, checkType, 1);
        if (records.isEmpty()) {
            return false;
        }
        
        PhoneCheckRecord record = records.get(0);
        if (record.getCreateTime() == null) {
            return false;
        }
        
        // 检查记录是否在有效期内
        long diffHours = (System.currentTimeMillis() - record.getCreateTime().getTime()) / (1000 * 60 * 60);
        return diffHours < validHours;
    }

    /**
     * 获取最近的有效检测记录
     * @param phoneNumber 手机号
     * @param checkType 检测类型
     * @param validHours 有效小时数
     * @return 有效的检测记录，如果没有则返回null
     */
    public PhoneCheckRecord getValidRecord(String phoneNumber, String checkType, int validHours) {
        List<PhoneCheckRecord> records = findRecentByPhone(phoneNumber, checkType, 1);
        if (records.isEmpty()) {
            return null;
        }
        
        PhoneCheckRecord record = records.get(0);
        if (record.getCreateTime() == null) {
            return null;
        }
        
        // 检查记录是否在有效期内
        long diffHours = (System.currentTimeMillis() - record.getCreateTime().getTime()) / (1000 * 60 * 60);
        if (diffHours < validHours) {
            return record;
        }
        
        return null;
    }
}
