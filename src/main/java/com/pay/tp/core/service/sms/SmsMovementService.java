package com.pay.tp.core.service.sms;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.tp.core.beans.sms.JiguangParam;
import com.pay.tp.core.entity.sms.SmsMovement;
import com.pay.tp.core.mapper.sms.SmsMovementMapper;


/**
 * <AUTHOR> zhangjian
 */
@Service
public class SmsMovementService {

    @Autowired
    private SmsMovementMapper smsMovementMapper;

    
    
    public SmsMovement query(JiguangParam param) throws JsonProcessingException {

        SmsMovement o = smsMovementMapper.query(param);
        if (o != null) {
            return o;
        } else {
            ObjectMapper objectMapper = new ObjectMapper();

            SmsMovement sms = new SmsMovement(param.getRequestNo(), "", param.getContent(),
                    param.getAlert(),
                    param.getExtras() == null ? null : objectMapper.writeValueAsString(param.getExtras()),
                    param.getTagsAnd() == null ? null : objectMapper.writeValueAsString(param.getTagsAnd()),
                    param.getTags() == null ? null : objectMapper.writeValueAsString(param.getTags()),
                    param.getAliases() == null ? null : objectMapper.writeValueAsString(param.getAliases()),
                    param.getPlatforms() == null ? null : objectMapper.writeValueAsString(param.getPlatforms()));
            sms.setOptismistic(0L);
            sms.setCreateTime(new Date());
            smsMovementMapper.insert(sms);
            return sms;
        }
    }


    
    public void update(SmsMovement sms) {
        smsMovementMapper.update(sms);
    }
    

}
