package com.pay.tp.core.service.auth;

import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.entity.auth.BankcardAuth;

/**
 * <AUTHOR>
 * @version 创建时间：2018年12月19日 下午11:23:22
 * @ClassName
 * @Description
 */
public interface BankCardAuthService {

	BankcardAuth query(BankCardAuthParam param);

	void update(BankcardAuth auth);

	/**
	 * 查询所有的鉴权通道列表（分页）
	 * @param pageNum 页码
	 */
	public PageInfo<BankcardAuth> findPageList(int pageNum, int pageSize, Map<String, String> params);

	/**
	 * 根据id查询
	 * @param id
	 */
	public BankcardAuth findById(Long id);

}
