package com.pay.tp.core.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.tp.core.entity.AppFileLog;
import com.pay.tp.core.mapper.AppFileLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2024/3/20
 * @apiNote
 */
@Service
public class AppFileLogService {

    @Autowired
    private AppFileLogMapper appFileLogMapper;



    @Transactional(rollbackFor = Exception.class)
    public void insert(Map<String, String> params, String fileName) {
        AppFileLog appFileLog = new AppFileLog();
        appFileLog.setAppCode(params.get("appCode"));
        appFileLog.setUrl(fileName);
        appFileLog.setUserNo(params.get("userNo"));
        appFileLog.setUserType(params.get("userType"));
        int i = appFileLogMapper.insert(appFileLog);
        if (i != 1) {
            throw new ServerException("保存日志记录异常");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFtpUrl(AppFileLog appFileLog) {
        int i = appFileLogMapper.updateFtpUrl(appFileLog);
        if (i != 1) {
            throw new ServerException("更新日志ftp路径异常");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(AppFileLog appFileLog) {
        int i = appFileLogMapper.updateStatus(appFileLog);
        if (i != 1) {
            throw new ServerException("更新日志状态异常");
        }
    }

    public PageInfo<AppFileLog> findPage(Map<String, String> param) {
        int currentPage = param.get("currentPage") == null ? 1 : Integer.parseInt(param.get("currentPage"));
        PageHelper.startPage(currentPage, PayConstants.PAGE_SIZE, true);
        List<AppFileLog> list = appFileLogMapper.findByPageAll(param);
        PageInfo<AppFileLog> page = new PageInfo<>(list);
        return page;
    }


    public AppFileLog findById(Long id){
        return appFileLogMapper.selectByPrimaryKey(id);
    }


    public List<AppFileLog> findBeforeTime(String startTime) {
        return appFileLogMapper.findBeforeTime(startTime);
    }

}
