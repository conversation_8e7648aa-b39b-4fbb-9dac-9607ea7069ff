package com.pay.tp.core.service.auth;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.pay.tp.core.beans.auth.TpAuthChannelParam;
import com.pay.tp.core.entity.auth.TpAuthChannel;
import com.pay.tp.core.mapper.auth.AuthChannelMapper;

@Service
public class AuthChannelService {

    private static final Logger logger = LoggerFactory.getLogger(AuthChannelService.class);

    @Autowired
    private AuthChannelMapper authChannelMapper;

    /**
     * 新增或修改鉴权通道
     * @param authChannelParam
     */
    public void addOrModChannel(TpAuthChannelParam authChannelParam)
    {
        /**
         * 查询通道是否存在，不存在，则新增；如存在，则修改
         */
        TpAuthChannel existChannel = findByUni(authChannelParam.getBusinessCode().name(),authChannelParam.getChannelNo());
        if(null != existChannel)
        {
            BeanUtils.copyProperties(authChannelParam,existChannel);
            authChannelMapper.updateById(existChannel);
            logger.info("修改鉴权通道完成:{}",existChannel);
        }
        else
        {
            TpAuthChannel newChannel = new TpAuthChannel();
            BeanUtils.copyProperties(authChannelParam,newChannel);
            authChannelMapper.insert(newChannel);
            logger.info("新增鉴权通道完成:{}",newChannel);
        }
    }

    /**
     * 根据业务编码及通道编号查询鉴权通道
     * @param businessCode 业务编码
     * @param channelNo  通道编号
     * @return
     */
    public TpAuthChannel findByUni( String businessCode,String channelNo)
    {
        return authChannelMapper.findByBusicodeAndChannelNo(businessCode, channelNo);
    }

    /**
     * 根据业务编码及状态查询鉴权通道
     * @param businessCode 业务编码
     * @param status 状态（ENABLE有效，DISABLE无效）
     * @return
     */
   public List<TpAuthChannel> findByBusiCodeAndStatus(String businessCode, String status)
    {
        return authChannelMapper.findByBusiCodeAndStatus(businessCode, status);
    }

    /**
     * 查询所有的鉴权通道列表（分页）
     * @param pageNum 页码
     * @param queryParams 查询条件
     * @return
     */
    public PageInfo<Map<String, Object>> findPageAuthChannelList(int pageNum, int pageSize, Map<String, String> queryParams)
    {
        List<Map<String,Object>> retList = authChannelMapper.findPageChannelList(pageNum, pageSize, queryParams);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(retList);
        return pageInfo;
    }

}