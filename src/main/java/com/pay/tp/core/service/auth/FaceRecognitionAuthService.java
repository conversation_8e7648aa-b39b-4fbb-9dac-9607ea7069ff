package com.pay.tp.core.service.auth;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.beans.auth.FaceRecognitionAuthParam;
import com.pay.tp.core.entity.auth.FaceRecognitionAuth;
import com.pay.tp.core.mapper.auth.FaceRecognitionAuthMapper;


@Service
public class FaceRecognitionAuthService {

	@Autowired
	FaceRecognitionAuthMapper faceRecognitionAuthMapper;

	public void update(FaceRecognitionAuth auth) {
		faceRecognitionAuthMapper.updateById(auth);
	}

	public FaceRecognitionAuth query(FaceRecognitionAuthParam param) {

		List<FaceRecognitionAuth> auths = faceRecognitionAuthMapper.queryByRequestNo(param);

		if (auths != null && !auths.isEmpty()) {
			for (FaceRecognitionAuth faceAuth : auths) {
				if("01".equals(faceAuth.getCode())) {
					return faceAuth;
				}
			}
			return auths.get(0);
		}

		FaceRecognitionAuth auth = null;
		List<FaceRecognitionAuth> faceAuths = faceRecognitionAuthMapper.query(param);
		if (faceAuths != null && !faceAuths.isEmpty()) {
			auth = faceAuths.get(0);
		}

		if (auth != null && "0".equals(auth.getRspCod()) && ("01".equals(auth.getCode()))) {
			return auth;
		} else {
			return null;
		}
	}
}
