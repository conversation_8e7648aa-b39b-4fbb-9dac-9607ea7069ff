package com.pay.tp.core.service.auth;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.frame.common.base.util.crypto.SM4Utils;
import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.entity.auth.BankcardAuth;
import com.pay.tp.core.mapper.auth.BankCardAuthMapper;

/**
 * 
 * <AUTHOR>
 * @version 创建时间：2018年12月19日 上午10:03:50
 * @ClassName
 * @Description
 */
@Service
public class BankCardAuthServicelmpl implements BankCardAuthService {

	@Autowired
	private BankCardAuthMapper bankCardMapper;
	 @Value("${crypto.sensitive.sm4}")
	    private String sm4Key;
	@Override
	public BankcardAuth query(BankCardAuthParam param) {
		BankcardAuth auth = null;
		try {
			String mobile=null;
			if(StringUtils.isNotBlank(param.getMobile())) {
				mobile = SM4Utils.encrypt(sm4Key,param.getMobile());
			}
			List<BankcardAuth> bankcardAuths = bankCardMapper.queryEncrypt(SM4Utils.encrypt(sm4Key,param.getCardNo())
					, SM4Utils.encrypt(sm4Key,param.getName()), SM4Utils.encrypt(sm4Key,param.getCidNo())
					, mobile);
			if (bankcardAuths != null && !bankcardAuths.isEmpty()) {
				auth = bankcardAuths.get(0);
			}
		} catch (Exception e) {
			List<BankcardAuth> bankcardAuths = bankCardMapper.query(param);
			if (bankcardAuths != null && !bankcardAuths.isEmpty()) {
				auth = bankcardAuths.get(0);
			}
		}
		return auth;
	}

	@Override
	public void update(BankcardAuth auth) {
		bankCardMapper.update(auth);
	}


	/**
	 * 查询所有的鉴权通道列表（分页）
	 * @param pageNum 页码
	 * @return
	 */
	public PageInfo<BankcardAuth> findPageList(int pageNum, int pageSize, Map<String, String> params) {
		List<BankcardAuth> retList = bankCardMapper.findByCondition(pageNum, pageSize, params);
		PageInfo<BankcardAuth> pageInfo = new PageInfo<>(retList);
		return pageInfo;
	}

	/**
	 * 根据id查询
	 * @param id
	 */
	public BankcardAuth findById(Long id) {
		return bankCardMapper.findById(id);
	}
	
	
}
