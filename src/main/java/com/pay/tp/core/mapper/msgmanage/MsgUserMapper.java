package com.pay.tp.core.mapper.msgmanage;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.msgmanage.MsgUser;

public interface MsgUserMapper {

    int insert(MsgUser record);

    MsgUser selectByPrimaryKey(Long id);

    int updateByPrimaryKey(MsgUser record);

    List<MsgUser> findByChannelCodeAndGroupNo(@Param("channelCode") String channelCode,
                                              @Param("groupNo") String groupNo);

    MsgUser findByUserNo(@Param("userNo") String userNo,
                         @Param("channelCode") String channelCode);

    List<MsgUser> findByParams(@Param("params") Map<String, Object> params);
}