package com.pay.tp.core.mapper.auth;

import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.entity.auth.BankcardAuth;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @version 创建时间：2018年12月19日 下午11:22:29
* @ClassName 
* @Description 
*/
public interface SdBankCardAuthMapper {

	List<BankcardAuth> query();
	void del(BankcardAuth auth);
	
}
