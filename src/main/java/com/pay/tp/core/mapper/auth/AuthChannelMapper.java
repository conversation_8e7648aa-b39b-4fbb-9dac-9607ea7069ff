package com.pay.tp.core.mapper.auth;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.auth.TpAuthChannel;

public interface AuthChannelMapper {

    int insert(TpAuthChannel record);

    TpAuthChannel findByBusicodeAndChannelNo(@Param("businessCode") String businessCode,@Param("channelNo") String channelNo);

    List<TpAuthChannel> findByBusiCodeAndStatus(@Param("businessCode") String businessCode,@Param("status") String status);

    List<Map<String, Object>> findPageChannelList(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize, @Param("queryParams") Map<String, String> queryParams);

    int updateById(TpAuthChannel record);
}