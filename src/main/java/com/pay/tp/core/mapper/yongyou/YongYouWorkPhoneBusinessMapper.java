package com.pay.tp.core.mapper.yongyou;

import com.pay.tp.core.entity.yongyou.YongYouWorkPhoneBusiness;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用友工作手机业务表Mapper接口
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface YongYouWorkPhoneBusinessMapper {

    /**
     * 根据主键查询
     * @param id 主键ID
     * @return 用友工作手机业务记录
     */
    YongYouWorkPhoneBusiness selectById(Long id);

    /**
     * 插入记录
     * @param record 用友工作手机业务记录
     * @return 影响行数
     */
    int insert(YongYouWorkPhoneBusiness record);

    /**
     * 更新记录
     * @param record 用友工作手机业务记录
     * @return 影响行数
     */
    int updateByPrimaryKey(YongYouWorkPhoneBusiness record);

    /**
     * 查询需要推送的数据
     * 条件：YONGYOU_PUSH_STATUS = 'INIT' 或 'FAIL'，PUSH_RETURN_NUM < 3，VERIFY_STATUS = 'STATUS'
     * @param limit 查询条数限制
     * @return 需要推送的记录列表
     */
    List<YongYouWorkPhoneBusiness> findPendingPushRecords(@Param("limit") int limit);

    /**
     * 更新推送状态和推送次数
     * @param id 主键ID
     * @param pushStatus 推送状态
     * @param pushReturnValue 推送返回值
     * @param pushReturnMessage 推送返回信息
     * @return 影响行数
     */
    int updatePushStatus(@Param("id") Long id,
                        @Param("pushStatus") String pushStatus,
                        @Param("pushReturnValue") String pushReturnValue,
                        @Param("pushReturnMessage") String pushReturnMessage);

    /**
     * 增加推送次数
     * @param id 主键ID
     * @return 影响行数
     */
    int incrementPushReturnNum(@Param("id") Long id);

    int batchIncrementPushReturnNum(@Param("ids") List<Long> ids);

    int updatePushStatusBatch(@Param("ids") List<String> customerNos, @Param("pushStatus") String pushStatus, @Param("returnValue") String returnValue, @Param("returnMessage") String returnMessage);

}
