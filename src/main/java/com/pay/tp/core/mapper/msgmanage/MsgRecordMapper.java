package com.pay.tp.core.mapper.msgmanage;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.msgmanage.MsgRecord;

public interface MsgRecordMapper {

    int insert(MsgRecord record);

    MsgRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKey(MsgRecord record);

    List<MsgRecord> findByParams(@Param("params") Map<String, Object> params);

    List<MsgRecord> findByRequestNo(@Param("requestNo") String requestNo);

    int deleteDingMsg();
}