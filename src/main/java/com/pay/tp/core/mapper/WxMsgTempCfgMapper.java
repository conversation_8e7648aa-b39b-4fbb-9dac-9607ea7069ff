package com.pay.tp.core.mapper;

import com.pay.tp.core.entity.WxMsgTempCfg;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WxMsgTempCfgMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxMsgTempCfg record);

    WxMsgTempCfg selectByPrimaryKey(Long id);

    int updateByPrimaryKey(WxMsgTempCfg record);

    List<WxMsgTempCfg> findByTempId(@Param("tempId") String tempId);

    List<WxMsgTempCfg> findByPageAll(Map<String, String> param);

}