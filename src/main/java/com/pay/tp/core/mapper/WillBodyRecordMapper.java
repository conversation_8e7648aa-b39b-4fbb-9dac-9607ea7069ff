package com.pay.tp.core.mapper;

import com.pay.tp.core.entity.WillBodyRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WillBodyRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WillBodyRecord record);

    WillBodyRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKey(WillBodyRecord record);

    WillBodyRecord findByOrderNo(@Param("orderNo") String orderNo, @Param("brand") String brand);

    List<WillBodyRecord> findValidateStatus(@Param("status") String status, @Param("startTime") String startTime,
                                            @Param("willType") String willType);

    List<WillBodyRecord> findByPageAll(@Param("param") Map<String, String> param);

    /**
     * 查询需要下载阿里
     * @param startTime
     * @return
     */
    List<WillBodyRecord> findAliFlag(@Param("startTime") String startTime);


    List<WillBodyRecord> findByCustNo(@Param("customerNo") String customerNo, @Param("brand") String brand);
}