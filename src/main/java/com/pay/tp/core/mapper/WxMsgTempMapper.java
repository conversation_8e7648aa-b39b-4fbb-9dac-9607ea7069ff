package com.pay.tp.core.mapper;

import com.pay.tp.core.entity.WxMsgTemp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WxMsgTempMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxMsgTemp record);

    WxMsgTemp selectByPrimaryKey(Long id);

    int updateByPrimaryKey(WxMsgTemp record);

    WxMsgTemp findByTempType(@Param("tempType") String tempType, @Param("brand") String brand);

    List<WxMsgTemp> findByPageAll(Map<String, Object> param);

}