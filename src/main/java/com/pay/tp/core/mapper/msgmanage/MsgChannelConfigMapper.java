package com.pay.tp.core.mapper.msgmanage;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.msgmanage.MsgChannelConfig;

public interface MsgChannelConfigMapper {

    int insert(MsgChannelConfig record);

    MsgChannelConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKey(MsgChannelConfig record);

    MsgChannelConfig findByChannelCodeAndCfgParam(@Param("channelCode") String channelCode, @Param("cfgParam") String cfgParam);

    List<MsgChannelConfig> findByChannelCode(@Param("channelCode") String channelCode);

    List<MsgChannelConfig> findByParams(@Param("params") Map<String, Object> params);

    MsgChannelConfig findUnique(MsgChannelConfig channelConfig);

}