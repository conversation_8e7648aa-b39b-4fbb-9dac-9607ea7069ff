package com.pay.tp.core.mapper.sms;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.sms.SmsRes;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.mapper
 * @Description: TODO
 * @date Date : 2018年12月24日 10:46
 */
public interface SmsResMapper {

    SmsRes query(@Param("requestNo") String requestNo, @Param("channelNo") String channelNo);

    void insert(SmsRes smsRes);

    void update(SmsRes smsRes);
}
