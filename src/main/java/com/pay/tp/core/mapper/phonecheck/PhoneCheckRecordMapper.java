package com.pay.tp.core.mapper.phonecheck;

import com.pay.tp.core.entity.phonecheck.PhoneCheckRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 号码检测记录Mapper接口
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface PhoneCheckRecordMapper {

    /**
     * 根据主键查询
     * @param id 主键ID
     * @return 号码检测记录
     */
    PhoneCheckRecord selectByPrimaryKey(Long id);

    /**
     * 插入记录
     * @param record 号码检测记录
     * @return 影响行数
     */
    int insert(PhoneCheckRecord record);

    /**
     * 更新记录
     * @param record 号码检测记录
     * @return 影响行数
     */
    int updateByPrimaryKey(PhoneCheckRecord record);

    /**
     * 根据请求号和手机号查询记录
     * @param requestNo 请求号
     * @param phoneNumber 手机号
     * @return 号码检测记录
     */
    PhoneCheckRecord findByRequestNoAndPhone(@Param("requestNo") String requestNo, 
                                           @Param("phoneNumber") String phoneNumber);

    /**
     * 根据请求号查询所有记录
     * @param requestNo 请求号
     * @return 号码检测记录列表
     */
    List<PhoneCheckRecord> findByRequestNo(@Param("requestNo") String requestNo);

    /**
     * 根据手机号查询最近的检测记录
     * @param phoneNumber 手机号
     * @param checkType 检测类型
     * @param limit 查询数量限制
     * @return 号码检测记录列表
     */
    List<PhoneCheckRecord> findRecentByPhone(@Param("phoneNumber") String phoneNumber,
                                           @Param("checkType") String checkType,
                                           @Param("limit") int limit);

    /**
     * 批量插入记录
     * @param records 号码检测记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<PhoneCheckRecord> records);

    /**
     * 分页查询记录
     * @param params 查询参数
     * @return 号码检测记录列表
     */
    List<Map<String, Object>> findByPageAll(Map<String, Object> params);

    /**
     * 统计检测记录数量
     * @param params 查询参数
     * @return 记录数量
     */
    int countByParams(Map<String, Object> params);

    /**
     * 根据商户编号统计检测数量
     * @param merchantNo 商户编号
     * @param checkType 检测类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Map<String, Object> countByMerchant(@Param("merchantNo") String merchantNo,
                                       @Param("checkType") String checkType,
                                       @Param("startTime") String startTime,
                                       @Param("endTime") String endTime);

    /**
     * 删除过期记录
     * @param expireTime 过期时间
     * @return 影响行数
     */
    int deleteExpiredRecords(@Param("expireTime") String expireTime);
}
