//package com.pay.tp.core.job;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Conditional;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import com.pay.frame.common.base.enums.cust.QrReportType;
//import com.pay.tp.core.biz.impl.DingDingBiz;
//import com.pay.tp.core.configuration.JobRegisterCondition;
//import com.pay.tp.core.entity.jh.JhChannelCfg;
//import com.pay.tp.core.remote.wx.WeChatClient;
//import com.pay.tp.core.service.jh.JhCHannelCfgService;
//
//import lombok.extern.slf4j.Slf4j;
//
///**
// *
// */
//@Slf4j
//@Component
//@Conditional({JobRegisterCondition.class})
//public class JhRefAccTokenTask {
//
//    @Autowired
//    private WeChatClient weChatClient;
//    @Autowired
//    private JhCHannelCfgService jhCHannelCfgService;
//    @Autowired
//    private DingDingBiz dingDingBiz;
//
//
//
//    @Scheduled(cron = "0 0/30 0/1 * * ?")
//    public void wxHealthCheck() {
//        try {
//        	log.info("JhRefAccTokenTask start");
//        	List<JhChannelCfg> list = jhCHannelCfgService.findByChannel(QrReportType.WX.getCode());
//        	log.info("{}",list);
//            if (!CollectionUtils.isEmpty(list)) {
//            	Set<String> set = new HashSet<>();
//                for (JhChannelCfg t : list) {
//                    try {
//                    	String key = t.getAppId()+t.getAppSecret();
//                    	log.info(key);
//                    	if(set.contains(key)) {
//                    		continue;
//                    	}
//                    	set.add(key);
//                    	String accessToken = weChatClient.getAccessToken(t.getAppId(), t.getAppSecret());
//                    	int s = jhCHannelCfgService.updateAccessToken(accessToken, t.getAppId(), t.getAppSecret());
//                    	if(s<1) {
//                    		throw new RuntimeException("更新ACC_TOKEN失败");
//                    	}
//                    } catch (Exception e) {
//                        log.error("error {}", t, e);
//                        try {
//							String param = "{\"msg\":\"刷新authToken异常, appId: %s channelNo: %s\"}";
//							String content = String.format(param, t.getAppId(),t.getChannelNo());
//							dingDingBiz.sendWorkMsg4TemplateCode(t.getAppId(), "JH_WX_ACC_TOKEN", content);
//						} catch (Throwable e2) {
//							log.error(e.getMessage()+"，发送失败",e2);
//						}
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("JhRefAccToken channel health check error ", e);
//        }
//    }
//
//
//}
