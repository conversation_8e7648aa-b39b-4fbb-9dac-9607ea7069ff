//package com.pay.tp.core.job;
//
//import com.pay.frame.common.base.util.DateUtil;
//import com.pay.frame.common.base.util.FmtDate;
//import com.pay.tp.core.beans.sms.SmsParam;
//import com.pay.tp.core.biz.SmsChannelBiz;
//import com.pay.tp.core.biz.impl.TencentBiz;
//import com.pay.tp.core.configuration.JobRegisterCondition;
//import com.pay.tp.core.entity.WillBodyRecord;
//import com.pay.tp.core.enums.WillType;
//import com.pay.tp.core.service.WillBodyRecordService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Conditional;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.text.MessageFormat;
//import java.util.Date;
//import java.util.List;
//
///**
// * @date 2022年07月19日 11:08
// */
//@Slf4j
//@Component
//@Conditional({JobRegisterCondition.class})
//public class TencentWillBodyTask {
//
//    @Autowired
//    private WillBodyRecordService willBodyRecordService;
//
//    @Autowired
//    private TencentBiz tencentBiz;
//
//    @Autowired
//    private SmsChannelBiz smsChannelBiz;
//
//
//    @Scheduled(cron = "0 0/10 0/1 * * ?")
//    public void appWillBody() {
//        String startTime = DateUtil.addDay(-3);
//        List<WillBodyRecord> list = willBodyRecordService.findValidateStatus("UNKNOWN", startTime, WillType.APP.name());
//        log.info("意愿核身核验结果：{}", list.size());
//        list.forEach(record -> {
//            try {
//                tencentBiz.willBodyResult(record);
//            } catch (Exception e) {
//                log.error("tencent willBodyResult error ", record, e);
//            }
//        });
//    }
//
//
//    /**
//     * 下载阿里云视频保存到自己的 ftp
//     */
//    @Scheduled(cron = "0 10 0 * * ?")
//    public void downALiPath() {
//        Date appointDate = FmtDate.getAppointHourMin(new Date(), 3, 50);
//        String content = "{0} 阿里云下载已经快4点了，停止下载";
//
//        String startTime = DateUtil.addDay(-3);
//        List<WillBodyRecord> list = willBodyRecordService.findAliFlag(startTime);
//        log.info("下载阿里云意愿视频：{}", list.size());
//        for (WillBodyRecord record : list) {
//
//            if(new Date().after(appointDate)){
//                SmsParam param = new SmsParam();
//                param.setRequestNo(record.getOrderNo());
//                param.setContent(MessageFormat.format(content, record.getOrderNo()));
//                param.setBrand(record.getBrand());
//                smsChannelBiz.send(param);
//                log.info("阿里云下载已经快4点了，停止下载：{}", record);
//
//                break;
//            }
//
//            try {
//                log.info("下载阿里云地址：{}", record);
//                tencentBiz.downALiPath(record);
//            } catch (Exception e) {
//                log.error("tencent downALiPath error ", record, e);
//            }
//        }
//    }
//
//
//    /**
//     * 下载小程序e证通的 视频
//     */
//    @Scheduled(cron = "0 0 4 * * ?")
//    public void getEIdvideo() {
//        String startTime = DateUtil.addDay(-3);
//        List<WillBodyRecord> list = willBodyRecordService.findValidateStatus("FINISH_VERIFY", startTime, WillType.EID.name());
//        log.info("eid意愿核身未下载视频：{}", list.size());
//        list.forEach(record -> {
//            try {
//                tencentBiz.getEIdvideo(record);
//            } catch (Exception e) {
//                log.error("tencent getEIdvideo error ", record, e);
//            }
//        });
//    }
//
//
//}
