//package com.pay.tp.core.job;
//
//import java.util.List;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Conditional;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import com.pay.tp.core.biz.impl.DingDingBiz;
//import com.pay.tp.core.configuration.JobRegisterCondition;
//import com.pay.tp.core.entity.msgmanage.MsgChannel;
//import com.pay.tp.core.service.msgmanage.MsgRecordService;
//
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 删除一个月以外的钉钉数据
// *
// */
//@Slf4j
//@Component
//@Conditional({JobRegisterCondition.class})
//public class SmsMsgDelDataTask {
//
//    @Autowired
//    private MsgRecordService msgRecordService;
//
//
//    @Scheduled(cron = "0 0 * * * ? ")
//    public void deleteRecordDingMsg() {
//        try {
//            msgRecordService.deleteDingMsg();
//        } catch (Exception e) {
//            log.error("ding ding record delete check error ", e);
//        }
//    }
//}
