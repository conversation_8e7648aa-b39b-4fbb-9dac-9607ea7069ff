package com.pay.tp.core.phoneCheeck;

import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: dm
 * @description:
 * @author: mxk
 * @create: 2023-09-18 15:25
 **/
public class Demo3 {

    private static String url = "http://XXXXXXX/dm-api/api/v2/verify";
    private static String merchantId = "M100000001";//能力平台注册的商户号
    private static String key = "477XXXXXXXXXXXXXXXXXXXXXXX114ED";//能力平台注册的商户秘钥


    public static void main(String[] args) throws Exception {

        verifForward(MD5Utils.encrypt("xxxxxxxxxxx"),"xx", MD5Utils.encrypt("xxxxx"), MD5Utils.encrypt("xxxxxxxxxxxxx"));
    }
    /***
     * 集成接口
     * @throws Exception
     */
    public  static void verifForward(String phone,String products,String name,String idnum) throws Exception {
        Map<String,Object> map =new HashMap<String, Object>();
        map.put("phone",phone);
        map.put("products",products);
        map.put("name",name);
        map.put("idnum",idnum);
        String jsonString = JSONObject.toJSONString(map);
        jsonString = AESUtils.encrypt(jsonString, MD5Utils.encryptToUpperCase(key).substring(8,24));

        sendPost(url + "/index/index", jsonString,String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url
     *            发送请求的 URL
     * @param param
     *            请求参数
     */
    public  static void sendPost(String url,String param,String requestTime) throws Exception {


        String sign = MD5Utils.encrypt(requestTime + key);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("merchantId", merchantId);
        headers.put("requestTime",requestTime);
        headers.put("sign", MD5Utils.encrypt(sign));

        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            HttpURLConnection conn = (HttpURLConnection)realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            // 添加消息头
            for (String key : headers.keySet()) {
                conn.addRequestProperty(key, headers.get(key));
            }
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            System.out.println("result:" + result);
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }
}
