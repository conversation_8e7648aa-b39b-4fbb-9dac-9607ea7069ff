package com.pay.tp.core.phoneCheeck;

import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.logging.Level;
import java.util.logging.Logger;

/** https://zhidao.baidu.com/question/1434129884173875059.html
 * @version V1.0
 * @desc AES 加密工具类
 */
public class AESUtils {

    private static final String KEY_ALGORITHM = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";//默认的加密算法

    /**
     * AES 加密操作
     *
     * @param content  待加密内容
     * @param password 加密密码
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content, String password) {
        try {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);// 创建密码器

            byte[] byteContent = content.getBytes("utf-8");

            SecretKeySpec key = new SecretKeySpec(password.getBytes("utf-8"), "AES");

            cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化为加密模式的密码器

            byte[] result = cipher.doFinal(byteContent);// 加密

            return Base64Utils.encodeToString(result);//通过Base64转码返回
        } catch (Exception ex) {
            Logger.getLogger(AESUtils.class.getName()).log(Level.SEVERE, null, ex);
        }

        return null;
    }

    /**
     * AES 解密操作
     *
     * @param content
     * @param password
     * @return
     */
    public static String decrypt(String content, String password) {

        try {
            //实例化
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);

            SecretKeySpec key = new SecretKeySpec(password.getBytes("utf-8"), "AES");

            //使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, key);

            //执行操作
            byte[] result = cipher.doFinal(Base64Utils.decodeFromString(content));
            String s = new String(result, "utf-8");
            return s;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logger.getLogger(AESUtils.class.getName()).log(Level.SEVERE, null, ex);
        }

        return null;
    }

    public static void main(String[] args) throws Exception {
        String password ="M100000001";
        password = MD5Utils.encryptToUpperCase(password).substring(8,24);
        String origin = "hello";
        String encrypt = AESUtils.encrypt(origin, password);
        String decrypt = AESUtils.decrypt(encrypt, password);
        System.out.println(origin);
        System.out.println(encrypt);
        System.out.println(decrypt);

    }

}
