package com.pay.tp.core.task;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.job.common.param.ReturnT;
import com.pay.tp.BaseTest;
import com.pay.tp.core.biz.impl.YongYouDataPushBiz;
import com.pay.tp.core.entity.yongyou.YongYouWorkPhoneBusiness;
import com.pay.tp.core.service.yongyou.YongYouWorkPhoneBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * 用友数据推送定时任务测试类
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
public class YongYouDataPushTaskTest extends BaseTest {

    @Autowired
    private YongYouDataPushTask yongYouDataPushTask;

    @Autowired
    private YongYouDataPushBiz yongYouDataPushBiz;

    @Autowired
    private YongYouWorkPhoneBusinessService yongYouWorkPhoneBusinessService;

    /**
     * 测试定时任务执行
     */
    @Test
    public void testExecute() {
        try {
            ReturnT<String> result = yongYouDataPushTask.execute("");
            log.info("定时任务执行结果: {}", result);
        } catch (Exception e) {
            log.error("定时任务执行异常", e);
        }
    }

    /**
     * 测试查询待推送数据
     */
    @Test
    public void testFindPendingPushRecords() {
        List<YongYouWorkPhoneBusiness> records = yongYouWorkPhoneBusinessService.findPendingPushRecords(10);
        log.info("查询到待推送记录数: {}", records.size());
        for (YongYouWorkPhoneBusiness record : records) {
            log.info("记录: ID={}, 商户编号={}, 手机号={}, 推送状态={}, 推送次数={}",
                    record.getId(), record.getCustomerNo(), record.getPhoneNo(),
                    record.getYongyouPushStatus(), record.getPushReturnNum());
        }
    }

    /**
     * 测试插入测试数据
     */
    @Test
    public void testInsertTestData() {
        YongYouWorkPhoneBusiness record = new YongYouWorkPhoneBusiness();
        record.setId(3235735L);
        record.setOptimistic(0L);
        record.setCustomerNo("806913807219281");
        record.setFullName("测试王金龙");
        record.setRealName("REXUs3ZB4n9KHUG69iOuDQ==");
        record.setPhoneNo("eTZTt0Qn6c7+jyHva2baoQ==");
        record.setOrganCode("河北省 石家庄市");
        record.setVerifyStatus(YongYouWorkPhoneBusiness.VerifyStatus.SUCCESS);
        record.setVerifyReturnNum(0);
        record.setVerifyReturnValue("SUCCESS");
        record.setVerifyReturnRemark("验证成功");
        record.setYongyouPushStatus(YongYouWorkPhoneBusiness.PushStatus.INIT);
        record.setPushReturnNum(0);
        record.setOperator("SYSTEM");
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setBrand("PLUS");

        int result = yongYouWorkPhoneBusinessService.insert(record);
        log.info("插入测试数据结果: {}, 记录ID: {}", result, record.getId());
    }

    /**
     * 测试更新推送状态
     */
    @Test
    public void testUpdatePushStatus() {
        // 先查询一条记录
        List<YongYouWorkPhoneBusiness> records = yongYouWorkPhoneBusinessService.findPendingPushRecords(1);
        if (!records.isEmpty()) {
            YongYouWorkPhoneBusiness record = records.get(0);

            // 测试更新为成功状态
            yongYouWorkPhoneBusinessService.handlePushSuccess(
                record.getId(),
                "SUCCESS",
                "测试推送成功"
            );

            log.info("更新推送状态为成功，记录ID: {}", record.getId());
        } else {
            log.info("没有找到可更新的记录");
        }
    }

    /**
     * 测试增加推送次数
     */
    @Test
    public void testIncrementPushReturnNum() {
        // 先查询一条记录
        List<YongYouWorkPhoneBusiness> records = yongYouWorkPhoneBusinessService.findPendingPushRecords(1);
        if (!records.isEmpty()) {
            YongYouWorkPhoneBusiness record = records.get(0);

            // 增加推送次数
            int result = yongYouWorkPhoneBusinessService.incrementPushReturnNum(record.getId());
            log.info("增加推送次数结果: {}, 记录ID: {}", result, record.getId());
        } else {
            log.info("没有找到可更新的记录");
        }
    }

    /**
     * 测试biz层批量处理
     */
    @Test
    public void testBatchProcessPush() {
        ResultsBean<String> result = yongYouDataPushBiz.batchProcessPush(10);
        log.info("批量处理结果: {}", result);
    }

    /**
     * 测试获取统计信息
     */
    @Test
    public void testGetStatistics() {
        ResultsBean<String> result = yongYouDataPushBiz.getPendingPushStatistics();
        log.info("统计信息: {}", result);
    }

    /**
     * 测试定时任务统计信息方法
     */
    @Test
    public void testTaskGetStatistics() {
        try {
            ReturnT<String> result = yongYouDataPushTask.getStatistics("");
            log.info("定时任务统计信息结果: {}", result);
        } catch (Exception e) {
            log.error("测试统计信息异常", e);
        }
    }
}
