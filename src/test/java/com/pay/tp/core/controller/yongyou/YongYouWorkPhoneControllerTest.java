package com.pay.tp.core.controller.yongyou;

import com.pay.tp.core.beans.yongyou.YongYouCustomerReq;
import com.pay.tp.core.utils.JsonUtil;
import com.pay.tp.core.utils.Md5Util;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;

/**
 * 用友控制器测试类
 * <AUTHOR>
 * @date 2025-01-04
 */
public class YongYouWorkPhoneControllerTest {

    @Test
    public void testCreateCustomerRequest() {
        // 创建测试数据
        YongYouCustomerReq request = new YongYouCustomerReq();
        
        // 创建客户信息
        YongYouCustomerReq.CustomerInfo customer1 = new YongYouCustomerReq.CustomerInfo();
        customer1.setCustomerId("90002");
        customer1.setName("小潘妈妈");
        customer1.setPhone("13960190002");
        customer1.setUserId("90031");
        customer1.setCollaborators(Arrays.asList("90032", "90033"));
        
        // 创建自定义字段
        YongYouCustomerReq.CustomFieldValue field1 = new YongYouCustomerReq.CustomFieldValue();
        field1.setId(709);
        field1.setValues(Collections.singletonList("清华大学附属中学"));
        
        YongYouCustomerReq.CustomFieldValue field2 = new YongYouCustomerReq.CustomFieldValue();
        field2.setId(707);
        field2.setValues(Collections.singletonList("一年级"));
        
        YongYouCustomerReq.CustomFieldValue field3 = new YongYouCustomerReq.CustomFieldValue();
        field3.setId(708);
        field3.setValues(Arrays.asList("英语", "数学"));
        
        // 创建多联系方式字段
        YongYouCustomerReq.CustomFieldValue field4 = new YongYouCustomerReq.CustomFieldValue();
        field4.setId(735);
        field4.setValues(Collections.emptyList());
        field4.setFieldType("6");
        
        YongYouCustomerReq.MultiContact contact1 = new YongYouCustomerReq.MultiContact();
        contact1.setOptionId("11765");
        contact1.setOptionText("13466665544");
        
        YongYouCustomerReq.MultiContact contact2 = new YongYouCustomerReq.MultiContact();
        contact2.setOptionId("11916");
        contact2.setOptionText("13466665542");
        
        field4.setMultiContacts(Arrays.asList(contact1, contact2));
        
        customer1.setCustomFieldValues(Arrays.asList(field1, field2, field3, field4));
        
        // 创建第二个客户
        YongYouCustomerReq.CustomerInfo customer2 = new YongYouCustomerReq.CustomerInfo();
        customer2.setCompany("测试3");
        customer2.setCustomerId("90003");
        customer2.setPhone("13960190003");
        customer2.setUserId("90031");
        
        request.setCustomers(Arrays.asList(customer1, customer2));
        
        // 输出JSON
        String json = JsonUtil.toJson(request);
        System.out.println("用友添加客户请求JSON:");
        System.out.println(json);
    }

    @Test
    public void testSignGeneration() {
        // 测试签名生成
        String signKey = "sign-key-baidu";
        String company = "baidu";
        String partnerId = "p123456";
        long timestamp = 1530081688201L;

        String signStr = signKey + company + partnerId + timestamp;
        String sign = Md5Util.encode(signStr);
        if (sign != null) {
            sign = sign.toUpperCase();
        }

        System.out.println("签名字符串: " + signStr);
        System.out.println("生成的签名: " + sign);
        System.out.println("期望的签名: A5869E8A67393190E81197CBA43A8782");

        // 验证签名是否正确
        assert "A5869E8A67393190E81197CBA43A8782".equals(sign) : "签名生成不正确";
    }

    /**
     * 测试与Postman相同的请求数据
     */
    @Test
    public void testPostmanSameRequest() {
        // 创建与Postman完全相同的请求数据
        YongYouCustomerReq request = new YongYouCustomerReq();

        YongYouCustomerReq.CustomerInfo customer = new YongYouCustomerReq.CustomerInfo();
        customer.setCustomerId("806913807219281");
        customer.setName("王金龙");
        customer.setPhone("13693033890");
        customer.setCompany("测试王金龙");

        request.setCustomers(Arrays.asList(customer));

        // 输出JSON，验证与Postman的请求体是否一致
        String json = JsonUtil.toJson(customer); // 注意：这里直接序列化单个客户对象
        System.out.println("与Postman相同的请求JSON:");
        System.out.println(json);

        // 验证JSON格式
        assert json.contains("\"customerId\":\"806913807219281\"");
        assert json.contains("\"name\":\"王金龙\"");
        assert json.contains("\"phone\":\"13693033890\"");
        assert json.contains("\"company\":\"测试王金龙\"");
    }

    /**
     * 测试签名生成（使用实际配置）
     */
    @Test
    public void testActualSignGeneration() {
        // 使用实际配置的参数
        String signKey = "64B0AD5FD36745FB94FAF6";
        String company = "6ogfq2";
        String partnerId = "pCDE746E7C9664ED991E22F802B6FD866";
        long timestamp = System.currentTimeMillis();

        String signStr = signKey + company + partnerId + timestamp;
        String sign = Md5Util.encode(signStr);
        if (sign != null) {
            sign = sign.toUpperCase();
        }

        System.out.println("实际配置签名测试:");
        System.out.println("时间戳: " + timestamp);
        System.out.println("签名字符串: " + signStr);
        System.out.println("生成的签名: " + sign);

        // 验证签名不为空
        assert sign != null && !sign.isEmpty() : "签名不能为空";
        assert sign.length() == 32 : "MD5签名长度应该是32位";
    }
}
