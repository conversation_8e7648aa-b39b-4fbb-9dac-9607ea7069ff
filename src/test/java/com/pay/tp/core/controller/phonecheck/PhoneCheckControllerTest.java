package com.pay.tp.core.controller.phonecheck;

import com.alibaba.fastjson.JSON;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.phonecheck.PhoneCheckReq;
import com.pay.tp.core.beans.phonecheck.PhoneCheckResp;
import com.pay.tp.core.biz.impl.PhoneCheckBiz;
import com.pay.tp.core.entity.phonecheck.PhoneCheckRecord;
import com.pay.tp.core.service.phonecheck.PhoneCheckRecordService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 号码检测控制器测试类
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class PhoneCheckControllerTest {

    @Autowired
    private PhoneCheckController phoneCheckController;

    @Autowired
    private PhoneCheckBiz phoneCheckBiz;

    @Autowired
    private PhoneCheckRecordService phoneCheckRecordService;

    /**
     * 测试空号检测
     */
    @Test
    public void testCheckEmptyNumber() {
        PhoneCheckReq request = new PhoneCheckReq();
        request.setRequestNo("TEST_EMPTY_" + System.currentTimeMillis());
        request.setPhoneNumbers(Arrays.asList(
                "13800138000", "13800138001", "13800138002",
                "13800138003", "13800138004", "13800138005"
        ));
        request.setMerchantNo("TEST_MERCHANT");
        request.setBrand("PLUS");

        log.info("测试空号检测请求: {}", JSON.toJSONString(request));

        ResultsBean<PhoneCheckResp> result = phoneCheckController.checkEmptyNumber(request);
        log.info("空号检测结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
        assert result.getObject().getResults() != null;
        assert result.getObject().getResults().size() == 6;
    }

    /**
     * 测试实名检测
     */
    @Test
    public void testCheckRealName() {
        PhoneCheckReq request = new PhoneCheckReq();
        request.setRequestNo("TEST_REALNAME_" + System.currentTimeMillis());
        request.setPhoneNumbers(Arrays.asList(
                "13800138010", "13800138011", "13800138012"
        ));
        request.setMerchantNo("TEST_MERCHANT");
        request.setBrand("PLUS");

        log.info("测试实名检测请求: {}", JSON.toJSONString(request));

        ResultsBean<PhoneCheckResp> result = phoneCheckController.checkRealName(request);
        log.info("实名检测结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
        assert result.getObject().getResults() != null;
        assert result.getObject().getResults().size() == 3;
    }

    /**
     * 测试风控检测
     */
    @Test
    public void testCheckRiskControl() {
        PhoneCheckReq request = new PhoneCheckReq();
        request.setRequestNo("TEST_RISK_" + System.currentTimeMillis());
        request.setPhoneNumbers(Arrays.asList(
                "13800138020", "13800138021", "13800138022"
        ));
        request.setMerchantNo("TEST_MERCHANT");
        request.setBrand("PLUS");

        log.info("测试风控检测请求: {}", JSON.toJSONString(request));

        ResultsBean<PhoneCheckResp> result = phoneCheckController.checkRiskControl(request);
        log.info("风控检测结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
        assert result.getObject().getResults() != null;
        assert result.getObject().getResults().size() == 3;
    }

    /**
     * 测试批量检测
     */
    @Test
    public void testBatchCheck() {
        PhoneCheckReq request = new PhoneCheckReq();
        request.setRequestNo("TEST_BATCH_" + System.currentTimeMillis());
        request.setCheckType(PhoneCheckReq.CheckType.EMPTY_NUMBER);

        // 构造大批量手机号
        List<String> phoneNumbers = Arrays.asList(
                "13800138030", "13800138031", "13800138032", "13800138033", "13800138034",
                "13800138035", "13800138036", "13800138037", "13800138038", "13800138039"
        );
        request.setPhoneNumbers(phoneNumbers);
        request.setMerchantNo("TEST_MERCHANT");
        request.setBrand("PLUS");

        log.info("测试批量检测请求: {}", JSON.toJSONString(request));

        ResultsBean<PhoneCheckResp> result = phoneCheckController.batchCheck(request);
        log.info("批量检测结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
        assert result.getObject().getResults() != null;
        assert result.getObject().getResults().size() == 10;
    }

    /**
     * 测试根据请求号查询结果
     */
    @Test
    public void testGetResultByRequestNo() {
        // 先执行一次检测
        PhoneCheckReq request = new PhoneCheckReq();
        String requestNo = "TEST_QUERY_" + System.currentTimeMillis();
        request.setRequestNo(requestNo);
        request.setPhoneNumbers(Arrays.asList("13800138040", "13800138041"));
        request.setMerchantNo("TEST_MERCHANT");
        request.setBrand("PLUS");

        phoneCheckController.checkEmptyNumber(request);

        // 查询结果
        ResultsBean<List<PhoneCheckRecord>> result = phoneCheckController.getResultByRequestNo(requestNo);
        log.info("查询结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
        assert result.getObject().size() == 2;
    }

    /**
     * 测试根据手机号查询历史
     */
    @Test
    public void testGetHistoryByPhone() {
        String phoneNumber = "13800138050";

        // 先执行一次检测
        PhoneCheckReq request = new PhoneCheckReq();
        request.setRequestNo("TEST_HISTORY_" + System.currentTimeMillis());
        request.setPhoneNumbers(Arrays.asList(phoneNumber));
        request.setMerchantNo("TEST_MERCHANT");
        request.setBrand("PLUS");

        phoneCheckController.checkEmptyNumber(request);

        // 查询历史
        ResultsBean<List<PhoneCheckRecord>> result = phoneCheckController.getHistoryByPhone(
                phoneNumber, PhoneCheckReq.CheckType.EMPTY_NUMBER, 5);
        log.info("查询历史结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
        assert result.getObject().size() >= 1;
    }

    /**
     * 测试统计功能
     */
    @Test
    public void testGetStatistics() {
        String merchantNo = "TEST_MERCHANT";

        ResultsBean<Map<String, Object>> result = phoneCheckController.getStatistics(
                merchantNo, PhoneCheckReq.CheckType.EMPTY_NUMBER, null, null);
        log.info("统计结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
    }

    /**
     * 测试生成请求号
     */
    @Test
    public void testGenerateRequestNo() {
        ResultsBean<String> result = phoneCheckController.generateRequestNo();
        log.info("生成请求号结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert result.getObject() != null;
        assert result.getObject().startsWith("PC");
    }

    /**
     * 测试健康检查
     */
    @Test
    public void testHealth() {
        ResultsBean<String> result = phoneCheckController.health();
        log.info("健康检查结果: {}", JSON.toJSONString(result));

        assert result.success();
        assert "号码检测服务运行正常".equals(result.getObject());
    }

    /**
     * 测试缓存功能
     */
    @Test
    public void testCache() {
        String phoneNumber = "13800138060";

        PhoneCheckReq request = new PhoneCheckReq();
        request.setRequestNo("TEST_CACHE_1_" + System.currentTimeMillis());
        request.setPhoneNumbers(Arrays.asList(phoneNumber));
        request.setMerchantNo("TEST_MERCHANT");
        request.setBrand("PLUS");

        // 第一次检测
        long startTime1 = System.currentTimeMillis();
        ResultsBean<PhoneCheckResp> result1 = phoneCheckController.checkEmptyNumber(request);
        long costTime1 = System.currentTimeMillis() - startTime1;
        log.info("第一次检测耗时: {}ms", costTime1);

        // 第二次检测（应该使用缓存）
        request.setRequestNo("TEST_CACHE_2_" + System.currentTimeMillis());
        long startTime2 = System.currentTimeMillis();
        ResultsBean<PhoneCheckResp> result2 = phoneCheckController.checkEmptyNumber(request);
        long costTime2 = System.currentTimeMillis() - startTime2;
        log.info("第二次检测耗时: {}ms", costTime2);

        assert result1.success();
        assert result2.success();
        // 第二次应该更快（使用缓存）
        assert costTime2 < costTime1;
    }

    /**
     * 测试异常处理
     */
    @Test
    public void testErrorHandling() {
        // 测试空请求
        PhoneCheckReq emptyRequest = new PhoneCheckReq();
        ResultsBean<PhoneCheckResp> result1 = phoneCheckController.checkEmptyNumber(emptyRequest);
        log.info("空请求结果: {}", JSON.toJSONString(result1));
        assert !result1.success();

        // 测试无效手机号
        PhoneCheckReq invalidRequest = new PhoneCheckReq();
        invalidRequest.setRequestNo("TEST_INVALID_" + System.currentTimeMillis());
        invalidRequest.setPhoneNumbers(Arrays.asList("123", "abc", ""));
        invalidRequest.setMerchantNo("TEST_MERCHANT");
        invalidRequest.setBrand("PLUS");

        ResultsBean<PhoneCheckResp> result2 = phoneCheckController.checkEmptyNumber(invalidRequest);
        log.info("无效手机号结果: {}", JSON.toJSONString(result2));
        // 应该成功，但结果中包含错误信息
        assert result2.success();
        assert result2.getObject().getResults().stream()
                .anyMatch(r -> r.getStatus().equals(PhoneCheckResp.PhoneStatus.FAIL));
    }

    /**
     * 测试并发处理
     */
    @Test
    public void testConcurrency() throws InterruptedException {
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                PhoneCheckReq request = new PhoneCheckReq();
                request.setRequestNo("TEST_CONCURRENT_" + threadIndex + "_" + System.currentTimeMillis());
                request.setPhoneNumbers(Arrays.asList(
                        "1380013807" + threadIndex,
                        "1380013808" + threadIndex
                ));
                request.setMerchantNo("TEST_MERCHANT");
                request.setBrand("PLUS");

                ResultsBean<PhoneCheckResp> result = phoneCheckController.checkEmptyNumber(request);
                log.info("线程{}检测结果: {}", threadIndex, result.success());
                assert result.success();
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        log.info("并发测试完成");
    }
}
