package com.pay.tp.wx;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.util.DateUtil;
import com.pay.tp.BaseTest;
import com.pay.tp.core.beans.wx.SendWxMsgReq;
import com.pay.tp.core.controller.wx.WeChatController;
import com.pay.tp.core.remote.wx.WeChatClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;


public class WxControllerTest extends BaseTest {

    @Autowired
    private WeChatController weChatController;

    @Autowired
    private WeChatClient weChatClient;

    
    @Test
    public void weChatAuth() throws Exception {
    	String accessToken = weChatClient.getCacheAccessToken("wx66d951fa88c6a44b", "a523e2eab9e5153096a8068a5e53893c");
    	System.out.println(accessToken);
//    	accessToken = weChatClient.jhAccessToken("wx66d951fa88c6a44b", "918db573951ddea469ef5a03fc082e00");
    	System.out.println(accessToken);
//    	accessToken = weChatClient.jhAccessToken("wx8878c7fd97c0f667", "d5cf115bd9005fbc06e60794be5484af");
//    	System.out.println(accessToken);
//        ResultsBean<String> resultsBean = weChatController.weChatAuth("041JYM0002ACNM1jPE200ClcJQ1JYM0Y");
//        System.out.println(resultsBean);
    }


    @Test
    public void sendWxMessage() throws Exception {
        SendWxMsgReq sendWxMsgReq = new SendWxMsgReq();

        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("custName", "测试商户");
        msgMap.put("transTime", DateUtil.formatTime());
        msgMap.put("transWay", "刷卡");
        msgMap.put("amount", "12121");
        sendWxMsgReq.setTemplateType("TRANS_SUCCESS");


        // 业务通知
//        Map<String, String> msgMap = new HashMap<>();
//        msgMap.put("time", DateUtil.formatTime());
//        msgMap.put("status", "待收货");
//        msgMap.put("content", "在线商城");
//        sendWxMsgReq.setTemplateType("BUSINESS_ACCEPT");


        // 收益到账通知
//        Map<String, String> msgMap = new HashMap<>();
//        msgMap.put("amount", "123");
//        msgMap.put("time", DateUtil.formatTime());
//        msgMap.put("type", "日结分润");
//        sendWxMsgReq.setTemplateType("INCOME_ACC");

        sendWxMsgReq.setBrand(Brand.UK.name());
        sendWxMsgReq.setOpenid("oIMUx6b-iVMipA2OZkxJfM7CGeaU");
        sendWxMsgReq.setPhone("***********");
        sendWxMsgReq.setUserNo("**********");
        sendWxMsgReq.setRequestNo("12345678yt11fdxz");
        sendWxMsgReq.setMsgMap(msgMap);


        ResultsBean<String> resultsBean = weChatController.sendWxMessage(sendWxMsgReq);
        System.out.println(resultsBean);
    }


//    @Test
//    public void wxHealthCheck() throws Exception {
//        smsMsgWxCheckHealthTask.wxHealthCheck();
//        System.out.println("");
//    }

}
