package com.pay.tp;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import com.pay.frame.common.base.enums.Brand;
import com.pay.tp.core.beans.tencent.EidWillBodyResultReq;
import com.pay.tp.core.biz.impl.TencentBiz;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.service.WillBodyRecordService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.controller.tencent.TencentController;


public class TencentControllerTest extends BaseTest {

    @Autowired
    private TencentController tencentController;
	@Autowired
    private WillBodyRecordService willBodyRecordService;
	@Autowired
	private TencentBiz tencentBiz;
//	@Autowired
//	private TencentWillBodyTask tencentWillBodyTask;
//
//
//    @Test
//    public void test() throws Exception {
//		tencentWillBodyTask.appWillBody();
////    	Map<String, String> map=new HashMap<String, String>();
////    	map.put("type", "SIGN");
////    	map.put("userId", "test001");
////		ResultsBean<Map<String, String>> findTicket = tencentController.findTicket(map);
////
////    	System.out.println(findTicket);
////    	System.out.println(findTicket);
//    }


    @Test
    public void test2() throws Exception {
//    	Map<String, String> map=new HashMap<String, String>();
//    	map.put("type", "SIGN");
//    	map.put("userId", "test001");
		WillBodyRecord record = new WillBodyRecord();
//		record.setTicket("xzsGwgWfCC2QzZqm0PeaO9OCjCmfKY8gEv65RVqTyBprSfk55255XLfVqUU2O7dr");
		record.setOrderNo("032c8be384d04409a30b349de73bd696");
		tencentBiz.willBodyResult(record);

    	System.out.println("========");
    }

    @Test
    public void test3() throws Exception {
		EidWillBodyResultReq param = new EidWillBodyResultReq();
		param.setBrand(Brand.PLUS.name());
//		param.setEidToken("34DC1AF7-4FF1-4E03-B950-4971C44C2D38");
		param.setEidToken("FE041AB2-4F21-40CF-A08E-0467CDDDDE4A");
		param.setSource("xcx");
		tencentBiz.saveEidResult(param);

    	System.out.println("========");
    }

	@Test
    public void test4() throws Exception {
		WillBodyRecord record = new WillBodyRecord();
		record.setBrand(Brand.PLUS.name());
//		param.setEidToken("34DC1AF7-4FF1-4E03-B950-4971C44C2D38");
		record.setOrderNo("0F549FE8-B023-4AD1-AB1F-A0C2D4003521");
		record.setSource("xcx");
		WillBodyRecord record1 = willBodyRecordService.findByOrderNo(record.getOrderNo(), record.getBrand());

		tencentBiz.getEIdvideo(record1);
    	System.out.println("========");
    }


}
