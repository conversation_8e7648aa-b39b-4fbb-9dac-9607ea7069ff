package com.pay.tp;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.mockito.internal.matchers.Find;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.util.DateUtil;
import com.pay.frame.common.base.util.RandomUtils;
import com.pay.tp.core.entity.auth.BankcardAuth;
import com.pay.tp.core.mapper.auth.BankCardAuthMapper;
import com.pay.tp.core.mapper.auth.SdBankCardAuthMapper;

public class BankCardAuthTest extends BaseTest{
	
	@Autowired
	private BankCardAuthMapper bankCardAuthMapper;
	@Autowired
	private SdBankCardAuthMapper sdBankCardAuthMapper;
	
	@Test
	public void xl() throws Exception {
		int pageNum=10000;
		Date createTime = DateUtil.parseDate("2020-01-01");
		PageInfo<BankcardAuth> info = findByAgentNo(1, pageNum);
		List<BankcardAuth> list = info.getList();
		int pages = info.getPages();
		System.out.println(pages);
		long currentTimeMillis = System.currentTimeMillis();
		save(list, createTime);
		System.out.println(System.currentTimeMillis() - currentTimeMillis);
		System.out.println(System.currentTimeMillis() - currentTimeMillis);
//		for (int i = 2; i <= pages; i++) {
//			currentTimeMillis = System.currentTimeMillis();
//			System.out.println(i);
//			 info = findByAgentNo(i, pageNum);
//			 list = info.getList();
//			 save(list, createTime);
//			 System.out.println(i);
//			 System.out.println(i + "===" +(System.currentTimeMillis() - currentTimeMillis));
//		}
	}
	@Transactional(propagation = Propagation.REQUIRES_NEW) 
	public void save(List<BankcardAuth> list,Date createTime) {
		for (BankcardAuth bankcardAuth : list) {
			bankcardAuth.setCreateTime(createTime);
			bankcardAuth.setRequestNo(RandomUtils.getUUID());
			bankCardAuthMapper.insertFromSdData(bankcardAuth);
//			sdBankCardAuthMapper.del(bankcardAuth);
		}
	}
	
	
	public PageInfo<BankcardAuth> findByAgentNo(int pageNum, int pageSize) {
		// 设置页属性
		PageHelper.startPage(pageNum, pageSize, true);
		// 查询数据
		List<BankcardAuth> list = sdBankCardAuthMapper.query();
		// 用PageInfo对结果进行包装
		PageInfo<BankcardAuth> page = new PageInfo<BankcardAuth>(list);
		return page;
	}
}
