//package com.pay.tp;
//
//import java.util.Date;
//import java.util.List;
//
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Propagation;
//import org.springframework.transaction.annotation.Transactional;
//
//import com.github.pagehelper.PageHelper;
//import com.github.pagehelper.PageInfo;
//import com.pay.frame.common.base.bean.ResultsBean;
//import com.pay.frame.common.base.util.DateUtil;
//import com.pay.frame.common.base.util.RandomUtils;
//import com.pay.tp.core.beans.fyt.FytFindUserReqBean;
//import com.pay.tp.core.beans.fyt.FytFindUserRespBean;
//import com.pay.tp.core.beans.fyt.FytRegisterReqBean;
//import com.pay.tp.core.controller.fyt.FytController;
//import com.pay.tp.core.entity.auth.BankcardAuth;
//import com.pay.tp.core.mapper.auth.BankCardAuthMapper;
//import com.pay.tp.core.mapper.auth.SdBankCardAuthMapper;
//
//public class FytTest extends BaseTest{
//	
//	@Autowired
//	private FytController FytController;
//	
//	@Test
//	public void xl() throws Exception {
//		String phone ="***********";
//		FytRegisterReqBean req1=new FytRegisterReqBean();req1.setPhone(phone);
//		ResultsBean<String> register = FytController.register(req1);
//		
//		System.out.println(register);
//		FytFindUserReqBean req=new FytFindUserReqBean();req.setPhone(phone);
//		ResultsBean<FytFindUserRespBean> findUser = FytController.findUser(req);
//		System.out.println(findUser);
////		for (int i = 2; i <= pages; i++) {
////			currentTimeMillis = System.currentTimeMillis();
////			System.out.println(i);
////			 info = findByAgentNo(i, pageNum);
////			 list = info.getList();
////			 save(list, createTime);
////			 System.out.println(i);
////			 System.out.println(i + "===" +(System.currentTimeMillis() - currentTimeMillis));
////		}
//	}
//}
