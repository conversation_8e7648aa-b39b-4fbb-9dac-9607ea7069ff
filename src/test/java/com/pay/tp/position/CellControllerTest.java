package com.pay.tp.position;

import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.BaseTest;
import com.pay.tp.core.beans.position.CellBean;
import com.pay.tp.core.beans.position.CellParam;
import com.pay.tp.core.controller.position.CellController;
import com.pay.tp.core.entity.position.Cell;

public class CellControllerTest extends BaseTest {

	@Autowired
	private CellController cellController;
	
	
	@Test
	public void test() throws Exception {
		CellParam param = new CellParam();
		param.setCell("150214146");
		param.setLac("36590");
		param.setMnc("11");
		param.setRequestNo("2521");
		CellBean cell = cellController.cell(param);
		System.out.println(cell);
//		Cell cell = new Cell();
//		cell.setdLat("39");
//		cell.setdLng("116");
//		cell.setoLat("39");
//		cell.setoLng("116");
//		cell.setAddress("北京市");
//		cell.setLac("04433");
//		cell.setCell("00337");
//		cellController.updateByCell(cell);
		
		
//		String latLng = "39.906786|116.51472113715";
//		ResultsBean<List<Map<String,String>>> resultsBean = cellController.queryByLatLng(latLng);
//		System.out.println(resultsBean);
	}
	
	
}
