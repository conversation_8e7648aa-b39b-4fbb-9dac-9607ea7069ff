package com.pay.tp.position;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.pay.tp.BaseTest;
import com.pay.tp.core.beans.position.IpBean;
import com.pay.tp.core.beans.position.IpParam;
import com.pay.tp.core.controller.position.IpController;

public class IpControllerTest extends BaseTest {

	@Autowired
	private IpController ipController;
	
	
	@Test
	public void test() throws Exception {
		IpParam param = new IpParam();
		param.setIp("**************");
		param.setRequestNo("5");
		IpBean exec = ipController.exec(param);
		System.out.println(exec);
	}
	
	
}
