package com.pay.tp.aliyun;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.Credentials;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.STSAssumeRoleSessionCredentialsProvider;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;

import java.io.File;

public class OosTest {
    //	生产：
//	用户登录名称 <EMAIL>
//	AccessKey ID LTAI5tAWz23p5bQ32Yi26eHN
//	AccessKey Secret ******************************
//	Endpoint：
//	oss-cn-beijing.aliyuncs.com
//	角色ARN:
//	acs:ram::****************:role/ldysramoss
//	OSS资源：
//	"Resource": [
//	    "acs:oss:*:****************:r3py9jtp87caldys/*"
//	bucketName:r3py9jtp87caldys
//	文件路径  :  oss.unnipay.com/124.mp4
    String region = "cn-beijing";
    // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
    private static String endpoint = "https://oss-cn-beijing.aliyuncs.com";
    // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
    private static String accessKeyId = "LTAI5tAWz23p5bQ32Yi26eHN";
    private static String accessKeySecret = "******************************";
    private static String roleArn = "acs:ram::****************:role/ldysramtestoss";
    // 填写Bucket名称，例如examplebucket。
    private static String bucketName = "r3py9jtp87caldys";
    // 填写不包含Bucket名称在内的Object完整路径，例如testfolder/exampleobject.txt。


    public static void main(String[] args) throws Exception {
        down();
    }

//{
//"AccessKeyID" : "LTAI5tEy6uEHDm2EidT6uk4N",
//"AccessKeySecret" : "******************************",
//"RoleArn" : "acs:ram::****************:role/ldysramtestoss",
//"TokenExpireTime" : "900",
//"PolicyFile": "policy/bucket_write_policy.txt"
//}

    private static void aa() throws ServerException, ClientException {
        // TODO Auto-generated method stub
        // yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
//		String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
//		String accessKeyId = "LTAI5tEy6uEHDm2EidT6uk4N";
//		String accessKeySecret = "******************************";
        // 从STS服务获取的安全令牌（SecurityToken）。
        // 授权STSAssumeRole访问的Region。以华东1（杭州）为例，其它Region请根据实际情况填写。
        String region = "cn-beijing";
        // 填写RAM用户的访问密钥（AccessKeyId和AccessKeySecret）。
        // 填写角色的ARN信息，即需要扮演的角色ID。格式为acs:ram::$accountID:role/$roleName。
        // $accountID为阿里云账号ID。您可以通过登录阿里云控制台，将鼠标悬停在右上角头像的位置，直接查看和复制账号ID，或者单击基本资料查看账号ID。
        // $roleName为RAM角色名称。您可以通过登录RAM控制台，单击左侧导航栏的RAM角色管理，在RAM角色名称列表下进行查看。
//		String roleArn = "acs:ram::****************:role/ldysramtestoss";
//		String roleSessionName = "alice-001";
        String roleSessionName = "sl";
        // 创建STSAssumeRoleSessionCredentialsProvider实例。
		STSAssumeRoleSessionCredentialsProvider provider = CredentialsProviderFactory.newSTSAssumeRoleSessionCredentialsProvider(
		region, accessKeyId, accessKeySecret, roleArn);
		provider.withRoleSessionName(roleSessionName);
		provider.withExpiredDuration(3600L);
		Credentials credentials = provider.getCredentials();
//        System.out.println(JSON.toJSON(credentials));
//        System.out.println(credentials.getSecretAccessKey());

//        System.out.println("---------");
//        DefaultProfile profile1 = DefaultProfile.getProfile(region);
//        HttpClientConfig httpClientConfig1 = HttpClientConfig.getDefault();
//        httpClientConfig1.setHttpsProxy(null);
//        profile1.setHttpClientConfig(httpClientConfig1);
//        com.aliyuncs.auth.BasicCredentials basicCredentials = new com.aliyuncs.auth.BasicCredentials(accessKeyId,
//                accessKeySecret);
//        STSAssumeRoleSessionCredentialsProvider provider1 = new STSAssumeRoleSessionCredentialsProvider(basicCredentials, roleArn, profile1);
//        provider1.withRoleSessionName(roleSessionName);
//        provider1.withExpiredDuration(3600L);
//        System.out.println(JSON.toJSON(provider1.getCredentials()));
//
//        System.out.println("---------");

        // 创建ClientConfiguration实例。
//		ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        IClientProfile profile = DefaultProfile.getProfile(region, accessKeyId, accessKeySecret);
        HttpClientConfig httpClientConfig = HttpClientConfig.getDefault();
        httpClientConfig.setHttpsProxy("https://10.8.9.136:3128");
        httpClientConfig.setProtocolType(ProtocolType.HTTPS);
        profile.setHttpClientConfig(httpClientConfig);


        DefaultAcsClient client = new DefaultAcsClient(profile);
//        IHttpClient httpClient =  ApacheHttpClient.getInstance();
//		client.setHttpClient(httpClient );
        final AssumeRoleRequest request = new AssumeRoleRequest();
        // 适用于Java SDK 3.12.0及以上版本。
//        request.setSysMethod(MethodType.POST);
        request.setRoleArn(roleArn);
        request.setRoleSessionName(roleSessionName);
        request.setPolicy(null); // 如果policy为空，则用户将获得该角色下所有权限。
//        request.setDurationSeconds(3600L); // 设置临时访问凭证的有效时间为3600秒。
        final AssumeRoleResponse response = client.getAcsResponse(request);
        System.out.println(JSON.toJSON(response.getCredentials()));
//        System.out.println("Expiration: " + response.getCredentials().getExpiration());
//        System.out.println("Access Key Id: " + response.getCredentials().getAccessKeyId());
//        System.out.println("Access Key Secret: " + response.getCredentials().getAccessKeySecret());
//        System.out.println("Security Token: " + response.getCredentials().getSecurityToken());
//        System.out.println("RequestId: " + response.getRequestId());


    }

    public static void down() throws Exception {

        String objectName = "0b3a01f76e8f47e1b26836e9aaeaf918.mp4";
        String pathName = "D:\\localpath\\124.mp4";
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
            // 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
            ossClient.getObject(new GetObjectRequest(bucketName, objectName), new File(pathName));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
    public static void del(String[] args) throws Exception {

        String objectName = "oss.unnipay.com/124.mp4";
        String pathName = "D:\\localpath\\124.mp4";
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
            // 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
            ossClient.getObject(new GetObjectRequest(bucketName, objectName), new File(pathName));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


}
