package com.pay.tp.aliyun;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.pay.frame.common.base.util.SftpClientUtils;
import com.pay.tp.BaseTest;
import com.pay.tp.core.biz.impl.ALiYunBiz;
import com.pay.tp.core.task.TpTempTask;

/**
 * @date 2022年08月19日 10:24
 */
public class AliYunTest extends BaseTest {

    @Autowired
    private ALiYunBiz aLiYunBiz;
    @Autowired
    private TpTempTask tpTempTask;

    @Value("${spring.application.name}")
    private String appName;


    @Test
    public void testAddOrMod1() throws Exception {
//    	aLiYunBiz.findToken(new HashMap<String, String>());
    	System.out.println("d");
    	tpTempTask.downFile(null);
    	System.out.println("d");
    }
    @Test
    public void testAddOrMod() throws Exception {
    	String objectName = "a575b54ac2434252b7150689b4292dff.mp4";
    	String pathName = "D:\\dddd.mp4";
//    	aLiYunBiz.downFile(objectName,pathName);
//        aLiYunBiz.downFile("0b3a01f76e8f47e1b26836e9aaeaf918.mp4", FilePathConstants.getLoaclTempPath(appName));
    }

    @Test
    public void testDelete() throws Exception {
    	String objectName = "a575b54ac2434252b7150689b4292dff.mp4";
    	String pathName = "/upload/customer/multi_media/20240321/sl/8069133481_24032111304671427604.xlog";
//    	aLiYunBiz.deleteFile(objectName);


        SftpClientUtils.delFile(pathName);
//        aLiYunBiz.downFile("0b3a01f76e8f47e1b26836e9aaeaf918.mp4", FilePathConstants.getLoaclTempPath(appName));
    }

//    @Autowired
//    private TencentWillBodyTask tencentWillBodyTask;
//
//    @Test
//    public void testDownloadAliVideo(){
//        tencentWillBodyTask.downALiPath();
//    }



    @Test
    public void getSignedUrl() throws Exception {
        //45f448ed23c9462aa97d7a7fe4dae859.mp4
        //14e6c86b7ea8476fb854cbb5c1314ee4.mp4
        //43dcceb31bd5415584db7cf01285f1f3.mp4
        //b1570a0ba00a446e90970cc3790aaa86.mp4
        //14c09e1615f54538b4729db636537141.mp4
        //ed9ac13ad9a549138b4acf3775f687eb.mp4
        //1ad33066628e446b81be774c66621963.mp4
        //c7ff65bdc1f94d709cdc064621eb5f43.mp4
        //4436f7a095ca4588bcbc144d0b9ae5ce.mp4
        //582dbad88eee44f5bdd01141f1be44a8.mp4
        String objectName = "b58717e43703469fb85aad367f40271c.mp4";
        aLiYunBiz.getSignedUrl(objectName, false);
    }

}
