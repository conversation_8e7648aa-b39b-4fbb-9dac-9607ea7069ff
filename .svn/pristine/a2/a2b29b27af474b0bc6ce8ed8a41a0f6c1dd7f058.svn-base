package com.pay.tp.core.service.msgmanage;

import com.pay.tp.core.entity.msgmanage.MsgTemplate;
import com.pay.tp.core.enums.Status;
import com.pay.tp.core.exception.SmsDingDingException;
import com.pay.tp.core.exception.SmsOptimisticException;
import com.pay.tp.core.mapper.msgmanage.MsgTemplateMapper;
import com.pay.tp.core.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 消息模板服务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MsgTemplateService {

    @Autowired
    private MsgTemplateMapper msgTemplateMapper;


    public MsgTemplate selectByPrimaryKey(Long id) {
        return msgTemplateMapper.selectByPrimaryKey(id);
    }

    public String buildMsgContent(MsgTemplate msgTemplate, String jsonParam) {
        if (msgTemplate == null) {
            log.error("build msg content error, msgTemplate is not exists.");
            throw new SmsDingDingException("99", "无可用消息模板：");
        }

        if (!Status.ENABLE.equals(msgTemplate.getStatus())) {
            log.error("build msg content error, template is disable {}", msgTemplate);
            throw new SmsDingDingException("99", "消息模板已关闭：" + msgTemplate.getTemplateCode());
        }

        String template = msgTemplate.getMsgTemplate();
        if (StringUtils.isEmpty(jsonParam)) {
            log.warn("build msg content waring template id: {}, param is empty.", msgTemplate);
            return template;
        }

        Map<String, String> templateParam = JsonUtil.toObject(jsonParam, Map.class);
        if (CollectionUtils.isEmpty(templateParam)) {
            log.warn("build msg content waring template id: {}, {}, param is empty.", msgTemplate, jsonParam);
            return template;
        }

        String content = templateParam.entrySet().stream()
                .reduce(template,
                        (x, y) -> x.replace("${" + y.getKey() + "}", y.getValue()),
                        (x, y) -> null);
        log.debug("build msg content {}, {} => {}", template, templateParam, content);
        return content;
    }

    public void updateByPrimaryKey(MsgTemplate template) {
        template.setUpdateTime(new Date());
        int i = msgTemplateMapper.updateByPrimaryKey(template);
        if (i != 1) {
            throw new SmsOptimisticException("99", "更新消息模板失败,已有变更先于此次变更");
        }
    }

    public void insert(MsgTemplate template) {
        template.setCreateTime(new Date()).setOptimistic(0L);
        msgTemplateMapper.insert(template);
    }

    public MsgTemplate findByTemplateCode(String templateCode) {
        return msgTemplateMapper.findByTemplateCode(templateCode);
    }

    public List<MsgTemplate> findByParams(Map<String, Object> params) {
        return msgTemplateMapper.findByParams(params);
    }

    public MsgTemplate findById(Long id) {
        return msgTemplateMapper.selectByPrimaryKey(id);
    }
}