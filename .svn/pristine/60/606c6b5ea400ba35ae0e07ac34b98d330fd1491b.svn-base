package com.pay.tp.baidu;

import com.pay.frame.common.base.util.Base64Utils;
import com.pay.tp.BaseTest;
import com.pay.tp.core.beans.BaiDuOcrBean;
import com.pay.tp.core.beans.BaiDuPersonVerifyBean;
import com.pay.tp.core.biz.impl.BaiDuBiz;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class BaiDuTest  extends BaseTest {

    @Autowired
    private BaiDuBiz baiDuBiz;


    @Test
    public void aa() throws Exception{
        BaiDuOcrBean baiDuOcrBean = new BaiDuOcrBean();
        baiDuOcrBean.setImage("");
BaiDuPersonVerifyBean bean=new BaiDuPersonVerifyBean();
bean.setBrand("PLUS");
bean.setIdentityNo("142226198203197418");
bean.setImage(Base64Utils.encodeFile("/Users/<USER>/Desktop/WechatIMG3012.jpeg", null));
bean.setLegalPerson("黄俊明");
bean.setUserNo("test01");
        Map<String, Object> detect = baiDuBiz.personVerify(bean);
        System.out.println(detect);
    }


}
