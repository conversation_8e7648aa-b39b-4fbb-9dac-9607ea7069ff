package com.pay.tp.core.entity.sms;

import com.pay.tp.core.entity.BaseEntity;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.entity
 * @Description: TODO
 * @date Date : 2018年12月24日 10:41
 */
public class SmsMsg extends BaseEntity{

	private String brand;
    private String phone;

    private String content;

    private String result;

    private String message;
    private String hiddenPhone;
    private String msgId;

    private String channelCode;

    private String resResult;
    private String resMessage;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getHiddenPhone() {
        return hiddenPhone;
    }

    public void setHiddenPhone(String hiddenPhone) {
        this.hiddenPhone = hiddenPhone;
    }

    public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}
    public String getResResult() {
		return resResult;
	}

	public void setResResult(String resResult) {
		this.resResult = resResult;
	}
    public String getResMessage() {
		return resMessage;
	}

	public void setResMessage(String resMessage) {
		this.resMessage = resMessage;
	}

    public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}



	@Override
    public String toString() {
        return "Sms{" +
                "phone='" + phone + '\'' +
                ", content='" + content + '\'' +
                ", result='" + result + '\'' +
                ", message='" + message + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", msgId='" + msgId + '\'' +
                ", resResult='" + resResult + '\'' +
                ", resMessage='" + resMessage + '\'' +
                "} " + super.toString();
    }

    public SmsMsg() {
    }

    public SmsMsg(String requestNo, String sys, String phone, String content) {
        super(requestNo, sys);
        this.phone = phone;
        this.content = content;
    }
}
