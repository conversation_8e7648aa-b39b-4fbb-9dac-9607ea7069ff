package com.pay.tp.core.service.position;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.position.CellParam;
import com.pay.tp.core.entity.position.Cell;
import com.pay.tp.core.mapper.position.CellMapper;
import com.pay.tp.core.remote.position.LbsClient;


/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.service
 * @Description: TODO
 * @date Date : 2018年12月24日 14:40
 */
@Service
public class CellServiceImpl implements  CellService{
    
	@Autowired
    private CellMapper cellMapper;

    @Autowired
    private LbsClient lbsClient;
    
    private final Logger logger = LoggerFactory.getLogger(CellServiceImpl.class);

    @Override
    public Cell query(CellParam param) {
        Cell o=cellMapper.query(param);
        
        if (o == null) {
            List<Cell> cells = cellMapper.queryByParam(param);
            if (cells != null && !cells.isEmpty()) {
                for (Cell cell : cells) {
                    if (!StringUtils.isEmpty(cell.getAddress())) {
                        o = cell;
                        break;
                    }
                }
                if (o == null) {
                    o = cells.get(0);
                }
            }
        }
        if (o!=null) {
            return o;
        }else {
            Cell cell=new Cell(param.getRequestNo(),"", param.getMnc(), param.getLac(), param.getCell());
            cellMapper.insert(cell);
            return cell;
        }
    }
    

    @Override
    public void update(Cell cell) {
        cellMapper.update(cell);
    }


	@Override
	@Transactional
	public void updateByCell(Cell cell) {
		cellMapper.updateByCell(cell);
	}
	
	
	@Override
	public ResultsBean<List<Map<String, String>>> queryByLatLng(String latLng) {
		try {
			String[] split = latLng.split("\\|");
			if(split.length != 2) {
				return ResultsBean.FAIL("格式不正确");
			}
			Cell cell = new Cell();
			cell.setdLat(split[0]);
			cell.setdLng(split[1]);
			List<Cell> list = cellMapper.queryByLatLng(cell);
			if (list.size() == 0) {
				return ResultsBean.FAIL("未查询到原纪录");
			}
			String lac = list.get(0).getLac();
			String ci = list.get(0).getCell();
			List<Map<String, String>> resList = new ArrayList<Map<String, String>>();
			Map<String, String> request = lbsClient.request(lac,ci , "1");
			if ("0".equals(String.valueOf(request.get("errcode")))) {
				request.put("lac", lac);
				request.put("cell", ci);
				request.put("lat", request.get("lat").substring(0,request.get("lat").length()-4));
				request.put("lon", request.get("lon").substring(0,request.get("lon").length()-4));
				resList.add(request);
			}
			Map<String, String> request1 = lbsClient.request(lac, ci, "0");
			if ("0".equals(String.valueOf(request1.get("errcode")))) {
				request1.put("lac", lac);
				request1.put("cell", ci);
				request1.put("lat", request1.get("lat").substring(0,request1.get("lat").length()-4));
				request1.put("lon", request1.get("lon").substring(0,request1.get("lon").length()-4));
				resList.add(request1);
			}
			return ResultsBean.SUCCESS(resList);
		} catch (Exception e) {
			logger.error("queryByLatLng exception={}",e.getMessage(),e);
			return ResultsBean.FAIL("系统异常");
			
		}
	}
	
}
