package com.pay.tp.core.configuration;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.pay.frame.common.base.util.StringUtils;
import lombok.Getter;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * rest template config
 *
 * <AUTHOR>
 */
@Configuration
@Getter
@ToString
public class AliOss {

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;
    @Value("${ali.yun.endpoint}")
    private String endpoint;
    @Value("${ali.yun.region}")
    private String region;
    @Value("${ali.yun.accessKeyId}")
    private String accessKeyId;
    @Value("${ali.yun.accessKeySecret}")
    private String accessKeySecret;
    @Value("${ali.yun.roleArn}")
    private String roleArn;
    @Value("${ali.yun.bucketName}")
    private String bucketName;
    @Value("${ali.yun.roleSessionName}")
    private String roleSessionName;
    @Value("${ali.yun.accessUrl:https://d2df7jcl45mdldys.oss-cn-beijing.aliyuncs.com/}")
    private String accessUrl;

    @Bean
    public OSS ossClient() {
        ClientBuilderConfiguration cfg = new ClientBuilderConfiguration();
        cfg.setProxyHost(proxyHost);
        cfg.setProxyPort(proxyPort);
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, cfg);

//       DefaultCredentialProvider credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(accessKeyId, accessKeySecret);
//       OSS ossClient1 = OSSClientBuilder.create()
//               .endpoint(endpoint)
//               .clientConfiguration(cfg)
//               .credentialsProvider(credentialsProvider)
//               .region(region)
//               .build();
    }

    @Bean
    public DefaultAcsClient defaultAcsClient() {
        HttpClientConfig httpClientConfig = HttpClientConfig.getDefault();
        if (StringUtils.isNotBlank(proxyHost)) {
            httpClientConfig.setHttpProxy("http://" + proxyHost + ":" + proxyPort);
            httpClientConfig.setHttpsProxy("http://" + proxyHost + ":" + proxyPort);
        }
        IClientProfile profile = DefaultProfile.getProfile(region, accessKeyId, accessKeySecret);
//      httpClientConfig.setHttpProxy("http://***********:3128");
////      httpClientConfig.setHttpsProxy("http://***********:3128");
//      httpClientConfig.setProtocolType(ProtocolType.HTTP);
        profile.setHttpClientConfig(httpClientConfig);
        return new DefaultAcsClient(profile);
    }
}
