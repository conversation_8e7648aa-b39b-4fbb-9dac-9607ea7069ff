package com.pay.tp.core.remote;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.CommonConstants;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-08-05 11:14:32
 */
@FeignClient(value = CommonConstants.PAYCHANNEL_EUREKA_SERVER_INSTANCE_CORE)
public interface PayChannelClient {

    /**
     * @Description 三（四）要素认证
     */
    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/otherCommon/authVer",
            method = RequestMethod.POST, headers = {"cus-client-name=tp-core"},
            produces = {"application/json;charset=UTF-8"})
    ResultsBean<String> authVer(@RequestBody AuthVerifyReq req);

    @Data
    class AuthVerifyReq {
    	private String brand;
    	 /**
         * 身份证号
         */
        private String identityNo;

        /**
         * 身份证名称
         */
        private String identityName;

        /**
         * 银行卡号
         */
        private String bankAccountNo;

        /**
         * 预留手机号
         */
        private String prePhone;
    }
}


