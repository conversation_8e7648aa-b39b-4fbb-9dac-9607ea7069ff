package com.pay.tp.core.configuration;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.pay.frame.common.base.util.SftpClientUtils;

/**
 * @Description: SFTP 配置
 * 
 * @version 2021年08月01日 下午
 * <AUTHOR>
@Configuration
public class SFTPConfig {
	
	@Value("${file.ftp.ip}")
    private String ip;
    @Value("${file.ftp.port}")
    private int port;
    @Value("${file.ftp.user}")
    private String username;
    @Value("${file.ftp.password}")
    private String password;
	
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;
    
    
    @PostConstruct
	public void init() {
    	SftpClientUtils.ip = ip;
    	SftpClientUtils.port = port;
    	SftpClientUtils.username = username;
    	SftpClientUtils.password = password;
//    	ApacheHttpClient.proxyHost = proxyHost;
//    	ApacheHttpClient.proxyPort = proxyPort;
	}
	
}
