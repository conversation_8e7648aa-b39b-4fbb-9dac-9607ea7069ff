package com.pay.tp.core.controller.auth;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.tp.core.beans.auth.TpAuthChannelParam;
import com.pay.tp.core.entity.auth.TpAuthChannel;
import com.pay.tp.core.service.auth.AuthChannelService;


@RestController
@RequestMapping("authChannel")
public class AuthChannelController {

	private final Logger logger = LoggerFactory.getLogger(AuthChannelController.class);

	@Autowired
	private AuthChannelService authChannelService;

	/**
	 * @Description: 分页查询
	 * @param queryParams
	 * @return
	 */
	@RequestMapping(value = "/findAuthChannelPage", method = RequestMethod.GET)
	public ResultsBean<PageInfo<Map<String, Object>>> findAuthChannelPage(
			@RequestBody Map<String, String> queryParams) {
		int currentPage = queryParams.get("currentPage") == null ? 1: Integer.parseInt(queryParams.get("currentPage").toString());
		PageInfo<Map<String, Object>> page = authChannelService.findPageAuthChannelList(currentPage,
				PayConstants.PAGE_SIZE, queryParams);
		return ResultsBean.SUCCESS(page);
	}

	/**
	 * 鉴权通道新增修改接口
	 */
	@RequestMapping(value = "/addOrUpdateChannel", method = RequestMethod.POST)
	public ResultsBean<String> addOrUpdateChannel(@RequestBody TpAuthChannelParam tpAuthChannelParam) {
//		logger.info("鉴权通道配置-新增或更新 参数:{}", tpAuthChannelParam);
		try {
			authChannelService.addOrModChannel(tpAuthChannelParam);
//			logger.info("鉴权通道配置-新增或更新成功");
			return ResultsBean.SUCCESS();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ResultsBean.EXCEPTION("保存失败请重试");
		}
	}

	@RequestMapping(value = "/findByUni", method = RequestMethod.GET)
	public ResultsBean<TpAuthChannel> findByUni(@RequestParam("businessCode") String businessCode,
			@RequestParam("channelNo") String channelNo) {

		TpAuthChannel channel = authChannelService.findByUni(businessCode, channelNo);
		return ResultsBean.SUCCESS(channel);
	}
}
