package com.pay.tp.core.controller.sms;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.tp.core.entity.sms.SmsChannel;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.service.sms.SmsChannelService;
import com.pay.tp.core.service.sms.SmsMsgService;

/**
 * 短信查询
 */
@RestController
@RequestMapping("/smsQuery")
public class SmsQueryController {

	@Autowired
	private SmsMsgService smsMsgService;
	
	@Autowired
	private SmsChannelService smsChannelService;
	
	
	
	/**
	 * @Description: 分页查询
	 * @param queryParams
	 * @return
	 */
	@RequestMapping(value = "/findBySmsPageAll", method = RequestMethod.GET)
	public ResultsBean<PageInfo<Map<String, Object>>> findBySmsPageAll(@RequestBody Map<String, Object> queryParams) {
		// 当前页
		int currentPage = queryParams.get("currentPage") == null ? 1: Integer.parseInt(queryParams.get("currentPage").toString());
		int pageSize = queryParams.get("pageSize") == null ? PayConstants.PAGE_SIZE: Integer.parseInt(queryParams.get("pageSize").toString());
		PageInfo<Map<String, Object>> page = smsMsgService.findByPageAll(currentPage, pageSize,queryParams);

		return ResultsBean.SUCCESS(page);
	}
	
	/**
	 * @Description 根据id查询
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "findSmsById/{id}", method = RequestMethod.GET)
	public ResultsBean<SmsMsg> findSmsById(@PathVariable("id") Long id){
		SmsMsg smsMsg = smsMsgService.findById(id);
		return ResultsBean.SUCCESS(smsMsg);
	}
	
	

	/**
	 * 查询所有短信通道
	 * @return
	 */
	@RequestMapping(value = "/findChannelAll", method = RequestMethod.GET)
	public ResultsBean<List<SmsChannel>> findAll() {
		return ResultsBean.SUCCESS(smsChannelService.findAll());
	}
	
	
}
