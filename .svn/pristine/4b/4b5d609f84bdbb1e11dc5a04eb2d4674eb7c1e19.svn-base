package com.pay.tp.core.controller.xcx;

import com.alibaba.fastjson.JSON;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.remote.wx.WeChatClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/xcxCust")
public class XcxCustController {

    @Autowired
    private WeChatClient weChatClient;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${wx.push.jscode2sessionUrl:}")
    private String jscode2sessionUrl;
    @Value("${wx.push.getuserphonenumberUrl:}")
    private String getuserphonenumberUrl;

    @Value("${wx.push.xcxCustAppid:}")
    private String plusAppid;
    @Value("${wx.push.xcxCustSecret:}")
    private String plusSecret;

    @Value("${wx.push.ukXcxCustAppid:}")
    private String ukAppid;
    @Value("${wx.push.ukXcxCustSecret:}")
    private String ukSecret;


//    Object
//    返回的 JSON 数据包
//    属性	类型	说明
//    openid	string	用户唯一标识
//    session_key	string	会话密钥
//    unionid	string	用户在开放平台的唯一标识符，若当前小程序已绑定到微信开放平台帐号下会返回，详见 UnionID 机制说明。
//    errcode	number	错误码
//    errmsg	string	错误信息
//    errcode 的合法值
//    值	说明	最低版本
//    -1	系统繁忙，此时请开发者稍候再试	
//    0	请求成功	
//    40029	code 无效	
//    45011	频率限制，每个用户每分钟100次	
//    40226	高风险等级用户，小程序登录拦截 。风险等级详见用户安全解方案
    /**
     * 文档说明地址
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
     * @param code
     * @return
     */
    @PostMapping("/code2session")
    public ResultsBean<Map<String, Object>> code2session(@RequestParam("code") String code, @RequestParam("brand") String brand) {
        Map<String, String> appMap = getAppIdSecret(brand);

      	String jscode2session = String.format(jscode2sessionUrl + "?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                appMap.get("appId"), appMap.get("secret"), code);

  		String result = restTemplate.getForObject(jscode2session, String.class);
//      	失败：	{"errcode":40163,"errmsg":"code been used, rid: 62a9adb2-01c41c65-34f78117"}
//      	成功：	{"session_key":"FrmAH3AksnzBwUW9\/F+4kA==","openid":"o1Cea4qHt4i-yarNH3IeqSTpihuM",unionid:"ozQ6b1Nj61Ew58d3JKYnKyvhU2XM"}
  		log.info("login params {}  result {} ", code,result);
  		Map<String, Object> map = JSON.parseObject(result,Map.class);
  		if(map==null||map.get("session_key")==null) {
  			return ResultsBean.FAIL("获取登录信息失败"+map.get("errcode"));
  		}
  		return ResultsBean.SUCCESS(map);
    }


    /**
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/phonenumber/phonenumber.getPhoneNumber.html
     * @param code
     * @return
     */
    @PostMapping("/getuserphonenumber")
    public ResultsBean<Object> getuserphonenumber(@RequestParam("code") String code, @RequestParam("brand") String brand) {
        Map<String, String> appMap = getAppIdSecret(brand);

    	String accessToken = weChatClient.getCacheAccessToken(appMap.get("appId"), appMap.get("secret"));
    	if(StringUtils.isBlank(accessToken.toString())) {
    		return ResultsBean.FAIL("获取手机号失败，请重试");
    	}
    	String getTokenUrl = String.format(getuserphonenumberUrl + "?access_token=%s", accessToken);
    	Map<String,String> map = new HashMap<String, String>();
    	map.put("code", code);
//    	{errcode=0, errmsg=ok, phone_info={phoneNumber=15635579392, purePhoneNumber=15635579392, countryCode=86, watermark={timestamp=1655285296, appid=wx8c30f097ced989a5}}}
      	Map<String, Object> result = restTemplate.postForObject(getTokenUrl, map, Map.class);
      	log.info("login params {}  result {} ", code,result);
      	if(result.get("phone_info")==null) {
      		return ResultsBean.FAIL("获取手机号失败"+result.get("errcode"));
      	}
      	Object phone_info = result.get("phone_info");
//      	log.info("login params {}  result {} ", code,phone_info);
      	return ResultsBean.SUCCESS(phone_info);
    }


    private Map<String, String> getAppIdSecret(String brand) {
        Map<String, String> map = new HashMap<String, String>();
        String appId="", secret="";
        if(Brand.PLUS.name().equals(brand)){
            appId = plusAppid;
            secret = plusSecret;

        }else if(Brand.UK.name().equals(brand)){
            appId = ukAppid;
            secret = ukSecret;
        }else{
            throw new ServerException("品牌类型错误");
        }

        map.put("appId", appId);
        map.put("secret", secret);
        return map;
    }


}
