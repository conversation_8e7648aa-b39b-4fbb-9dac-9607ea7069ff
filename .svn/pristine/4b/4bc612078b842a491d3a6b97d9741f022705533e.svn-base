package com.pay.tp;

import java.util.Map;
import java.util.UUID;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.beans.auth.CompanyCheckParam;
import com.pay.tp.core.configuration.Application;
import com.pay.tp.core.controller.auth.BankCardAuthController;
import com.pay.tp.core.controller.auth.CompanyCheckController;

/**
* <AUTHOR>
* @version 创建时间：2018年12月19日 下午11:23:31
* @ClassName 
* @Description 
*/
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = Application.class)
public class BaseTest {

	@Autowired
	private CompanyCheckController companyCheckController;

//	@Autowired
//	private XlBankCardAuthClient defaultZXBankCardAuthClient;
	
	@Autowired
	private BankCardAuthController bankCardAuthController;

//	@Test
//	public void testRequest() throws Exception {
//		//companyCheckController.request("12345","","张健","****************95","***********");
//
//		defaultZXBankCardAuthClient.request("05c51d934f0782f595","**************","张四林","140102197006256252",null, null);
//	}

//	@Test
//	public void testRequest1() throws Exception {
//
//		CompanyCheckParam param = new CompanyCheckParam();
//
//		param.setRegNo("****************");
//		param.setCidNo("***************");
//		param.setEntName("卡友支付服务有限公司");
//		param.setFrName("上官步燕");
//		param.setRequestNo(System.currentTimeMillis()+"");
//
//		System.out.println(companyCheckController.auth(param));
//	}
	
	
//	@Test
	public void testBankAuth() throws Exception {
		BankCardAuthParam params = new BankCardAuthParam();
		String orderId = UUID.randomUUID().toString().replaceAll("-", "");
		params.setRequestNo(orderId); 	// 请求号
		params.setCardNo("6212260200043832852"); 		// 卡号
		params.setCidNo("232723199002061112"); 		// 身份证号
		params.setCustomerName("许秦稷"); 	// TODO 鉴权人
		params.setMobile("***********"); 		// 预留手机号
		params.setName("许秦稷1111"); 			// 账户名称
		
		
		Map<String, Object> map = bankCardAuthController.auth(params);
		System.out.println(map);
	}
	
}
