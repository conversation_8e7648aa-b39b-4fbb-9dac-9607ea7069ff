package com.pay.tp.core.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * APP_FILE_LOG
 * <AUTHOR>
@Data
public class AppFileLog implements Serializable {
    /**
     * 编号
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * APP_CODE
     */
    private String appCode;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 阿里云件名
     */
    private String url;

    /**
     * ftp文件名
     */
    private String ftpUrl;

    private String status;

    private static final long serialVersionUID = 1L;

}