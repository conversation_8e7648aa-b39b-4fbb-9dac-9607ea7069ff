package com.pay.tp.core.task;

import com.pay.frame.common.base.util.DateUtil;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.biz.impl.TencentBiz;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.enums.WillType;
import com.pay.tp.core.service.WillBodyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@JobHandler(value = "downEidVideo")
public class DownEidVideoTask extends IJobHandler {

    @Autowired
    private WillBodyRecordService willBodyRecordService;

    @Autowired
    private TencentBiz tencentBiz;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("downEidVideo start..." + params);

            String startTime = DateUtil.addDay(-3);
            List<WillBodyRecord> list = willBodyRecordService.findValidateStatus("FINISH_VERIFY", startTime, WillType.EID.name());
            log.info("eid意愿核身未下载视频：{}", list.size());
            list.forEach(record -> {
                try {
                    tencentBiz.getEIdvideo(record);
                } catch (Exception e) {
                    log.error("tencent getEIdvideo error ", record, e);
                }
            });
            log.info("downEidVideo end..." + params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("downEidVideo error", e);
            return ReturnT.FAIL;
        }
    }
}
