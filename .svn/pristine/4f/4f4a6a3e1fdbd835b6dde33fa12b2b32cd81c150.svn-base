package com.pay.tp.core.task;

import com.pay.frame.common.base.util.DateUtil;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;
import com.pay.tp.core.biz.impl.AppFileLogBiz;
import com.pay.tp.core.biz.impl.TencentBiz;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.enums.WillType;
import com.pay.tp.core.service.WillBodyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@JobHandler(value = "appWillBody")
public class AppWillBodyTask extends IJobHandler {

    @Autowired
    private WillBodyRecordService willBodyRecordService;

    @Autowired
    private TencentBiz tencentBiz;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        try {
        	log.info("appWillBody start..." + params);

            String startTime = DateUtil.addDay(-3);
            List<WillBodyRecord> list = willBodyRecordService.findValidateStatus("UNKNOWN", startTime, WillType.APP.name());
            log.info("意愿核身核验结果：{}", list.size());
            list.forEach(record -> {
                try {
                    tencentBiz.willBodyResult(record);
                } catch (Exception e) {
                    log.error("tencent willBodyResult error ", record, e);
                }
            });

            log.info("appWillBody end..." + params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("appWillBody error", e);
            return ReturnT.FAIL;
        }
    }
}
