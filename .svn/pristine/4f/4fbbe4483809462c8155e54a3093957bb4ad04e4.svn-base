package com.pay.tp.core.beans.position;

import org.hibernate.validator.constraints.NotBlank;


/**ip定位请求参数
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.beans
 * @Description: TODO
 * @date Date : 2018年12月24日 14:29
 */
public class IpParam {
    
	@NotBlank
    /**ip地址*/
    private String ip;
    @NotBlank
    /**请求号*/
    private String requestNo;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    @Override
    public String toString() {
        return "IpParam{" +
                "ip='" + ip + '\'' +
                ", requestNo='" + requestNo + '\'' +
                '}';
    }
    
}
