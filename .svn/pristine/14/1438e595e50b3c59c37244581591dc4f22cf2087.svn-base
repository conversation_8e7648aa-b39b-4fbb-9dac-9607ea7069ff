package com.pay.tp.core.beans.sms;

import java.util.Date;

import org.springframework.beans.BeanUtils;

import com.pay.tp.core.entity.msgmanage.MsgUser;
import com.pay.tp.core.enums.Status;

import lombok.Data;

/**
 * 消息通讯 用户DTO
 *
 * <AUTHOR>
 */
@Data
public class SmsMsgUserDto {
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 通道编号
     */
    private String channelCode;

    /**
     * 通道用户编号
     */
    private String channelUserNo;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 所属用户组编号
     */
    private String groupNo;

    /**
     * 用户组名
     */
    private String groupName;

    /**
     * 说明描述
     */
    private String desc;

    public SmsMsgUserDto convertFor(MsgUser user) {
        SmsMsgUserDto dto = new SmsMsgUserDto();
        BeanUtils.copyProperties(user, dto);
        dto.setStatus(user.getStatus().name());
        return dto;
    }

    public MsgUser convertToSmsMsgUser(SmsMsgUserDto userDto) {
        MsgUser user = new MsgUser();
        BeanUtils.copyProperties(userDto, user);
        user.setStatus(Status.valueOf(userDto.getStatus()));
        return user;
    }
}
