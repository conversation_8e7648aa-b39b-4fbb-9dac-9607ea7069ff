<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.WxMsgTempMapper">
  <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.WxMsgTemp">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="OPERATOR" jdbcType="VARCHAR" property="operator" />
    <result column="TEMP_TYPE" jdbcType="VARCHAR" property="tempType" />
    <result column="TEMP_ID" jdbcType="VARCHAR" property="tempId" />
    <result column="TEMP_NAME" jdbcType="VARCHAR" property="tempName" />
    <result column="TEMP_TITLE" jdbcType="VARCHAR" property="tempTitle" />
    <result column="URL_TYPE" jdbcType="VARCHAR" property="urlType" />
    <result column="TEMP_URL" jdbcType="VARCHAR" property="tempUrl" />
    <result column="APPID" jdbcType="VARCHAR" property="appid" />
    <result column="TEMP_REMARK" jdbcType="VARCHAR" property="tempRemark" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, "OPERATOR", TEMP_TYPE, TEMP_ID, TEMP_NAME, 
    URL_TYPE, TEMP_URL, APPID, TEMP_REMARK, "STATUS", BRAND,TEMP_TITLE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UBADMA.WX_MSG_TEMP
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="findByTempType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WX_MSG_TEMP
    where TEMP_TYPE = #{tempType} and STATUS = 'TRUE'
    and BRAND = #{brand}
  </select>

  <select id="findByPageAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WX_MSG_TEMP
    <where>
      <if test="brand != null and brand != '' ">
        and BRAND = #{brand}
      </if>
      <if test="operator != null and operator != '' ">
        and OPERATOR = #{operator}
      </if>
      <if test="status != null and status != '' ">
        and STATUS = #{status}
      </if>
      <if test="tempType != null and tempType != '' ">
        and TEMP_TYPE = #{tempType}
      </if>
      <if test="tempId != null and tempId != '' ">
        and TEMP_ID = #{tempId}
      </if>
      <if test="tempName != null and tempName != '' ">
        and TEMP_NAME LIKE CONCAT(CONCAT('%',#{tempName,jdbcType=VARCHAR}),'%')
      </if>
      <if test="urlType != null and urlType != '' ">
        and URL_TYPE = #{urlType}
      </if>
      <if test="createTimeStart != null and createTimeStart != '' " >
        <![CDATA[
			  and CREATE_TIME >= to_date(#{createTimeStart,jdbcType=TIMESTAMP} || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
			]]>
      </if>
      <if test="createTimeEnd != null and createTimeEnd != '' " >
        <![CDATA[
			  and CREATE_TIME <= to_date(#{createTimeEnd,jdbcType=TIMESTAMP} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
			]]>
      </if>
    </where>
    order by id desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from UBADMA.WX_MSG_TEMP
    where ID = #{id,jdbcType=DECIMAL}
  </delete>

  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.pay.tp.core.entity.WxMsgTemp" useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
      SELECT UBADMA.SEQ_WX_MSG_TEMP_ID.nextval FROM dual
    </selectKey>
    insert into UBADMA.WX_MSG_TEMP (OPTIMISTIC, CREATE_TIME, UPDATE_TIME,
      "OPERATOR", TEMP_TYPE, TEMP_ID, 
      TEMP_NAME, URL_TYPE, TEMP_URL, 
      APPID, TEMP_REMARK, "STATUS", 
      BRAND,TEMP_TITLE)
    values (#{id,jdbcType=DECIMAL}, 0, SYSDATE, #{updateTime,jdbcType=TIMESTAMP},
      #{operator,jdbcType=VARCHAR}, #{tempType,jdbcType=VARCHAR}, #{tempId,jdbcType=VARCHAR}, 
      #{tempName,jdbcType=VARCHAR}, #{urlType,jdbcType=VARCHAR}, #{tempUrl,jdbcType=VARCHAR}, 
      #{appid,jdbcType=VARCHAR}, #{tempRemark,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR},#{tempTitle,jdbcType=VARCHAR})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.WxMsgTemp">
    update UBADMA.WX_MSG_TEMP
    set OPTIMISTIC = OPTIMISTIC + 1,
      UPDATE_TIME = SYSDATE,
      "OPERATOR" = #{operator,jdbcType=VARCHAR},
      TEMP_TYPE = #{tempType,jdbcType=VARCHAR},
      TEMP_ID = #{tempId,jdbcType=VARCHAR},
      TEMP_NAME = #{tempName,jdbcType=VARCHAR},
      TEMP_TITLE = #{tempTitle,jdbcType=VARCHAR},
      URL_TYPE = #{urlType,jdbcType=VARCHAR},
      TEMP_URL = #{tempUrl,jdbcType=VARCHAR},
      APPID = #{appid,jdbcType=VARCHAR},
      TEMP_REMARK = #{tempRemark,jdbcType=VARCHAR},
      "STATUS" = #{status,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>