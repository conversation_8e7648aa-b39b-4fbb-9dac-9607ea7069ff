package com.pay.tp.core.mapper.msgmanage;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.msgmanage.MsgTemplate;

public interface MsgTemplateMapper {

    int insert(MsgTemplate record);

    MsgTemplate selectByPrimaryKey(Long id);

    int updateByPrimaryKey(MsgTemplate record);

    MsgTemplate findByTemplateCode(@Param("templateCode") String templateCode);

    List<MsgTemplate> findByParams(@Param("params") Map<String, Object> params);
}