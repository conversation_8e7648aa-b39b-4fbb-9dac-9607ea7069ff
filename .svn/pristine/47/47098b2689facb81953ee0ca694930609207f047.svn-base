package com.pay.tp.core.mapper;

import com.pay.tp.core.entity.AppFileLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AppFileLogMapper {

    int insert(AppFileLog record);

    AppFileLog selectByPrimaryKey(Long id);

    int updateFtpUrl(AppFileLog record);

    int updateStatus(AppFileLog record);

    List<AppFileLog> findByPageAll(Map<String, String> param);

    List<AppFileLog> findBeforeTime(@Param("startTime") String startTime);

}