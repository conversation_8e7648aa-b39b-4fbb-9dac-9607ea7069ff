package com.pay.tp;

import com.pay.frame.common.base.util.RandomUtils;
import com.pay.tp.core.beans.sms.SmsParam;
import com.pay.tp.core.biz.SmsChannelBiz;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class SmsTest extends BaseTest {

    @Autowired
    private SmsChannelBiz SmsChannelBiz;

    @Test
    public void send() throws Exception {
        SmsParam param = new SmsParam();
        param.setBrand("UK");
        param.setContent("登录验证码：134148，非本人操作请忽略");
        param.setPhone("15117923505");
        param.setRequestNo(RandomUtils.getFlowNo());
        Map<String, Object> send = SmsChannelBiz.send(param);
        System.out.println(send);
    }

    @Test
    public void send2() throws Exception {
        SmsParam param = new SmsParam();
        param.setBrand("UK");
        param.setContent("商户已开通，账号：80000912632，密码：Aa123456");
        param.setPhone("18611170241");
        param.setRequestNo(RandomUtils.getFlowNo());
        Map<String, Object> send = SmsChannelBiz.send(param);
        System.out.println(send);
    }

    @Test
    public void send3() throws Exception {
        SmsParam param = new SmsParam();
        param.setBrand("UK");
        param.setContent("终端解绑80000912632，SN：12322568000000000683，请告知服务商");
        param.setPhone("13681563801");
        param.setRequestNo(RandomUtils.getFlowNo());
        Map<String, Object> send = SmsChannelBiz.send(param);
        System.out.println(send);
    }


    @Test
    public void send4() throws Exception {
        SmsParam param = new SmsParam();
        param.setBrand("UK");
        param.setContent("您的商户交易受限。请登录联动优客，进入待办事项查看处理。");
        param.setPhone("15117923505");
        param.setRequestNo(RandomUtils.getFlowNo());
        Map<String, Object> send = SmsChannelBiz.send(param);
        System.out.println(send);
    }
}
