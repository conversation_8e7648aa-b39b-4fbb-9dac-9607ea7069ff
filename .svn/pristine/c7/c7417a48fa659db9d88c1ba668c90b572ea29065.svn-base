package com.pay.tp.core.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Properties;

/**
 * 定时调度平台配置
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "spring.job.executor")
public class JobExecutorProperties {

    private String dispatcherAddresses;
    private String appName;
    private String ip;
    private int port;
    private String accessToken;
    private String logPath;
    private int logRetentionDays;

    public String getDispatcherAddresses() {
        return dispatcherAddresses;
    }

    public void setDispatcherAddresses(String dispatcherAddresses) {
        this.dispatcherAddresses = dispatcherAddresses;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public int getLogRetentionDays() {
        return logRetentionDays;
    }

    public void setLogRetentionDays(int logRetentionDays) {
        this.logRetentionDays = logRetentionDays;
    }

    public Properties toProperties() {
        Properties properties = new Properties();
        notNullAdd(properties, "dispatcherAddresses", this.dispatcherAddresses);
        notNullAdd(properties, "appName", this.appName);
        notNullAdd(properties, "ip", this.ip);
        notNullAdd(properties, "port", this.port);
        notNullAdd(properties, "accessToken", this.accessToken);
        notNullAdd(properties, "logPath", this.logPath);
        notNullAdd(properties, "logRetentionDays", this.logRetentionDays);
        return properties;
    }

    private void notNullAdd(Properties properties, String key, Object value) {
        if (value != null) {
            properties.setProperty(key, value.toString());
        }
    }
}
