package com.pay.tp.core.mapper.msgmanage;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.msgmanage.MsgChannel;

public interface MsgChannelMapper {

    int insert(MsgChannel record);

    MsgChannel selectByPrimaryKey(Long id);

    int updateByPrimaryKey(MsgChannel record);

    MsgChannel findByCode(String code);

    List<MsgChannel> findByType(String type);

    List<MsgChannel> findByParams(@Param("params") Map<String, Object> params);
}