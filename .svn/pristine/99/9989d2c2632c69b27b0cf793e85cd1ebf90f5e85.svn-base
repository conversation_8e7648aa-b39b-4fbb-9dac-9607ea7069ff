package com.pay.tp.core.remote.auth.jixin;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;


public final class AESUtils {
    // AES config
    public final static Integer AES_SIZE_128 = 128;
    public final static String ALGORITHM_AES = "AES";
    public static final String CIPHER_ALGORITHM_CBC = "AES/CBC/PKCS5Padding";
    public static final String BC_PROVIDER = "BC";
    public static final String DEFAULT_ROOT_IV = "0000000000000000";
    public static final String DEFAULT_CHARSET = "UTF-8";

    static {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    private final byte[] passwordBytes;
    private final Integer keySize;

    public AESUtils(byte[] passwordBytes) {
        this(passwordBytes, AES_SIZE_128);
    }

    public AESUtils(byte[] passwordBytes, Integer keySize) {
        this.passwordBytes = passwordBytes;
        this.keySize = keySize;
    }

    protected Cipher getEncryptCipher() throws Exception {
        return getCipher(Cipher.ENCRYPT_MODE);
    }

    protected Cipher getDecryptCipher() throws Exception {
        return getCipher(Cipher.DECRYPT_MODE);
    }

    // cipher不是线程安全的，如果需要性能上的考虑，
    //使用cache的方案来实现，暂时应该不需要
    protected Cipher getCipher(Integer mode) throws Exception {
        try {
            SecretKeySpec key = new SecretKeySpec(passwordBytes, ALGORITHM_AES);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC, BC_PROVIDER);// 创建密码器
            cipher.init(mode, key, new IvParameterSpec(DEFAULT_ROOT_IV.getBytes()));// 初始化
            return cipher;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 加密
     *
     * @param content 需要加密的内容
     * @return
     */
    public String encrypt(String content) throws Exception {
        try {
            if (content != null) {
                byte[] byteContent = content.getBytes(DEFAULT_CHARSET);
                return Base64.encodeBase64String(encrypt(byteContent));
            } else {
                return null;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public byte[] encrypt(byte[] content) throws Exception {
        try {
            return getEncryptCipher().doFinal(content);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 解密
     *
     * @param content
     * @return
     * @throws
     */
    public String decrypt(String content) throws Exception {
        try {
            if (content != null) {
                byte[] byteContent = content.getBytes(DEFAULT_CHARSET);
                return new String(decrypt(Base64.decodeBase64(byteContent)));
            } else {
                return null;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public byte[] decrypt(byte[] content) throws Exception {
        try {
            return getDecryptCipher().doFinal(content);
        } catch (Exception e) {
            throw new Exception("failed to Decrypt the content [" + content + "]", e);
        }
    }

    public static void main(String[] args) throws Exception {
        String merchkey = "rlvkAu05uNTw9mDzjgN5YGxX14Oqrrc7";
        AESUtils aesUtils = new AESUtils(merchkey.getBytes(), merchkey.length());
        System.out.println(aesUtils.decrypt("IwqjlQVPt88GqEnqiEaJPg=="));
    }
}
