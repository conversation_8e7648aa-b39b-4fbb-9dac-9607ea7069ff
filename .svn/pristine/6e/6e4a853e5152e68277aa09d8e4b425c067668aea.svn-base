package com.pay.tp.core.mapper.auth;

import java.util.List;

import com.pay.tp.core.beans.auth.CompanyCheckParam;
import com.pay.tp.core.entity.auth.CompanyCheck;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.mapper
 * @Description: TODO
 * @date Date : 2018年12月22日 17:17
 */
public interface CompanyCheckMapper {
    List<CompanyCheck> query(CompanyCheckParam param);

    void insert(CompanyCheck companyCheck);

    void update(CompanyCheck companyCheck);

    CompanyCheck queryByRequestNo(CompanyCheckParam param);
}
