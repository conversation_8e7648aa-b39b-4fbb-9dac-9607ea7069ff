package com.pay.tp.core.entity.msgmanage;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MSG_RECORD
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MsgRecord implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 系统
     */
    private String sys;

    /**
     * 请求编号
     */
    private String requestNo;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 返回码
     */
    private String resultCode;

    /**
     * 返回信息
     */
    private String result;

    /**
     * 通道编号
     */
    private String channelCode;

    /**
     * 接收消息的用户名
     */
    private String userName;

    /**
     * 通道用户编号
     */
    private String channelUserNo;

    /**
     * 消息模板ID
     */
    private Long msgTemplateId;

    /**
     * 消息模板参数
     */
    private String msgTemplateParam;
}