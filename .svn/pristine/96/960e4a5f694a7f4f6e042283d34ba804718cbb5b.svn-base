package com.pay.tp.core.service.auth;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.beans.auth.CompanyCheckParam;
import com.pay.tp.core.entity.auth.CompanyCheck;
import com.pay.tp.core.mapper.auth.CompanyCheckMapper;

/**
 * 
* <AUTHOR>
* @version 创建时间：2018年12月19日 上午10:03:50
* @ClassName 
* @Description
 */
@Service
public class CompanyCheckServicelmpl implements CompanyCheckService {

	@Autowired
	private CompanyCheckMapper companyCheckMapper;
	
	@Override
	public CompanyCheck query(CompanyCheckParam param) {

		CompanyCheck o=companyCheckMapper.queryByRequestNo(param);
		if( o != null){
			return o;
		}

		List<CompanyCheck> companyChecks = companyCheckMapper.query(param);
		if(companyChecks != null && !companyChecks.isEmpty()){
			o = companyChecks.get(0);
		}

		if (o != null  && "000000".equals(o.getRspCod())) {
			return o;
		}else {
			CompanyCheck companyCheck=new CompanyCheck(param.getRequestNo(),"", param.getEntName(),param.getRegNo(), param.getFrName(), param.getCidNo());
			companyCheckMapper.insert(companyCheck);
			return companyCheck;
		}
	}


	@Override
	public void update(CompanyCheck companyCheck) {
		companyCheckMapper.update(companyCheck);
	}

	
}
