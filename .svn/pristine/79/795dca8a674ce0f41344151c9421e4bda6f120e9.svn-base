package com.pay.tp.core.beans.sms;

import org.hibernate.validator.constraints.NotBlank;

/**短信请求参数
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.beans
 * @Description: TODO
 * @date Date : 2018年12月24日 10:47
 */
public class SmsParam {
    /**
     * UK_AGENT 走独立通道， 其余来源走相同通道
     */
    private String ownerRole;

	private String brand;
    /**
     * 请求号
     */
    @NotBlank
    private String requestNo;
    /**
     * 号码
     */
    @NotBlank
    private String phone;
    /**
     * 内容
     */
    @NotBlank
    private String content;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}


    public String getOwnerRole() {
		return ownerRole;
	}

	public void setOwnerRole(String ownerRole) {
		this.ownerRole = ownerRole;
	}

	@Override
    public String toString() {
        return "SmsParam{" +
                "requestNo='" + requestNo + '\'' +
                ", phone='" + phone + '\'' +
                ", content='" + content + '\'' +
                ", ownerRole='" + ownerRole + '\'' +
                '}';
    }
}
