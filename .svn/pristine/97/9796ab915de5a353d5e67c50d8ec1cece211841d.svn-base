package com.pay.tp.core.controller.msgmanage;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.tp.core.beans.sms.SmsMsgRsp;
import com.pay.tp.core.beans.sms.SmsMsgUserDto;
import com.pay.tp.core.biz.impl.DingDingBiz;
import com.pay.tp.core.entity.msgmanage.MsgChannel;
import com.pay.tp.core.entity.msgmanage.MsgUser;
import com.pay.tp.core.service.msgmanage.MsgChannelService;
import com.pay.tp.core.service.msgmanage.MsgUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 消息通讯，用户管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/msg/manage/user")
public class MsgManageUserControl {

    @Autowired
    private MsgUserService msgUserService;

    @Autowired
    private MsgChannelService msgChannelService;

    @Autowired
    private DingDingBiz dingDingBiz;

    /**
     * 用户信息变更
     */
    @PostMapping("/updateUser")
    public SmsMsgRsp updateUser(@Validated @RequestBody SmsMsgUserDto userDto) {
        log.info("update user {}", userDto);
        MsgUser user = userDto.convertToSmsMsgUser(userDto);
        msgUserService.updateByPrimaryKey(user);
        return new SmsMsgRsp().setCode("00").setMsg("变更成功");
    }


    /**
     * 新增用户
     */
    @PostMapping("/addUser")
    public SmsMsgRsp addUser(@Validated @RequestBody SmsMsgUserDto userDto) {
        log.info("add user {}", userDto);
        MsgUser user = userDto.convertToSmsMsgUser(userDto);
        user.setUserNo(user.getPhoneNo());
        msgUserService.addUser(user);
        return new SmsMsgRsp().setCode("00").setMsg("新增用户成功");
    }


    /**
     * 新增用户
     */
    @PostMapping("/activeUser")
    public SmsMsgRsp activeUser(@Validated @RequestBody SmsMsgUserDto userDto) {
        log.info("active user {}", userDto);

        MsgUser user = msgUserService.findByUserNo(userDto.getUserNo(), userDto.getChannelCode());

        if (user == null) {
            return new SmsMsgRsp().setCode("99").setMsg("用户不存在" + userDto);
        }

        MsgChannel channel = msgChannelService.findByCode(userDto.getChannelCode());
        if ("DING_DING".equals(channel.getType())) {
            dingDingBiz.activeUser(user);
        } else {
            return new SmsMsgRsp().setCode("88").setMsg("不支持的用户通道类型");
        }
        return new SmsMsgRsp().setCode("00").setMsg("激活用户成功");
    }


    /**
     * 分页查询用户列表
     */
    @PostMapping("/pageUser")
    public PageInfo<SmsMsgUserDto> pageUser(@RequestParam Map<String, Object> params) {
        log.info("page user {}", params);
        Integer pageNum = Integer.valueOf(params.getOrDefault("currentPage", 1).toString());
        PageHelper.startPage(pageNum, 10);
        List<MsgUser> users = msgUserService.findByParams(params);
        PageInfo page = new PageInfo(users);
        List<SmsMsgUserDto> dtoList = users.stream().map(e -> new SmsMsgUserDto().convertFor(e))
                .collect(Collectors.toList());
        page.setList(dtoList);
        return page;
    }

    /**
     * 单笔查询用户
     */
    @GetMapping("/findUser")
    public SmsMsgUserDto findUser(@RequestParam("id") Long id) {
        log.info("find user {}", id);
        MsgUser user = msgUserService.findById(id);
        SmsMsgUserDto dto = new SmsMsgUserDto().convertFor(user);
        return dto;
    }

}
