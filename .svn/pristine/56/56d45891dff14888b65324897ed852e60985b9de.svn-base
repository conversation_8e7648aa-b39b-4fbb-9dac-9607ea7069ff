package com.pay.tp.core.beans.tencent;

import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * @date 2022年07月19日 9:43
 */
@Data
@ToString
public class ApiAppWillFaceResultRes {

    /** 0代表成功 */
    private String code;

    /** 返回结果描述 */
    private String msg;

    /** 业务流水号 */
    private String bizSeqNo;

    /** 请求接口的时间 */
    private String transactionTime;

    private WillBodyResult result;

    public boolean success() {
        return "0".equals(code);
    }

    @Data
    @ToString
    public static class WillBodyResult{
        /** 人脸核身结果 */
        private String faceCode;

        /** 人脸核身结果描述 */
        private String faceMsg;

        /** 意愿表达结果 */
        private String willCode;

        /** 意愿表达结果描述 */
        private String willMsg;

        /** 订单编号 */
        private String orderNo;

        /** 活体检测得分 */
        private String liveRate;

        /** 后台返回的刷脸风险信息 */
        private Map<String, Object> riskInfo;

        /** 人脸比对得分 */
        private String similarity;

        /** 进行刷脸的时间 */
        private String occurredTime;

        /** 人脸核身时的照片，base64 位编码 */
        private String photo;

        /** 人脸核身时的视频，base64 位编码 */
        private String video;

        /** 人脸核身时的 sdk 版本号 */
        private String sdkVersion;

        /** 腾讯云控制台申请的 appid */
        private String appId;

        /** 意愿表达用户音频 意愿表达阶段的音频文件，base64位编码 */
        private String willUserAudio;

        /** 意愿表达播报音频 意愿表达阶段的音频文件，base64位编码 */
        private String willReadAudio;

        /** 意愿核身完整视频：从用户播报音频到回复音频过程，base64位编码 */
        private String willUserVideo;

        /** ASR 客户初始化上送的文字信息 */
        private String willStandText;

        /** ASR 客户初始化上送的答案文字信息 */
        private String willStandAnswer;

        /** ASR 识别结果文本信息 */
        private String willUserAnswer;
    }

}
