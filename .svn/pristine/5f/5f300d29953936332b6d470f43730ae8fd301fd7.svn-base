package com.pay.tp.core.entity.sms;

import com.pay.tp.core.entity.BaseEntity;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.entity
 * @Description: TODO
 * @date Date : 2019年02月13日 16:50
 */
public class SmsMovement extends BaseEntity {

    private String content;
    private String alert;
    private String extras;
    private String tagsAnd;
    private String tags;
    private String aliases;
    private String platforms;
    private String result;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAlert() {
        return alert;
    }

    public void setAlert(String alert) {
        this.alert = alert;
    }

    public String getExtras() {
        return extras;
    }

    public void setExtras(String extras) {
        this.extras = extras;
    }

    public String getTagsAnd() {
        return tagsAnd;
    }

    public void setTagsAnd(String tagsAnd) {
        this.tagsAnd = tagsAnd;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getAliases() {
        return aliases;
    }

    public void setAliases(String aliases) {
        this.aliases = aliases;
    }

    public String getPlatforms() {
        return platforms;
    }

    public void setPlatforms(String platforms) {
        this.platforms = platforms;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public SmsMovement() {
        super();
    }

    public SmsMovement(String requestNo, String sys, String content, String alert, String extras, String tagsAnd, String tags, String aliases, String platforms) {
        super(requestNo, sys);
        this.content = content;
        this.alert = alert;
        this.extras = extras;
        this.tagsAnd = tagsAnd;
        this.tags = tags;
        this.aliases = aliases;
        this.platforms = platforms;
    }

    @Override
    public String toString() {
        return "SmsMovement{" +
                "content='" + content + '\'' +
                ", alert='" + alert + '\'' +
                ", extras='" + extras + '\'' +
                ", tagsAnd='" + tagsAnd + '\'' +
                ", tags='" + tags + '\'' +
                ", aliases='" + aliases + '\'' +
                ", platforms='" + platforms + '\'' +
                ", result='" + result + '\'' +
                "} " + super.toString();
    }
}
