//package com.pay.tp.core.biz.impl;
//
//import java.nio.charset.Charset;
//import java.util.HashMap;
//import java.util.Map;
//
//import org.apache.http.HttpHost;
//import org.apache.http.HttpResponse;
//import org.apache.http.client.config.RequestConfig;
//import org.apache.http.client.methods.HttpPost;
//import org.apache.http.entity.StringEntity;
//import org.apache.http.impl.client.CloseableHttpClient;
//import org.apache.http.impl.client.HttpClientBuilder;
//import org.apache.http.util.EntityUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.pay.frame.common.base.bean.ResultsBean;
//import com.pay.frame.common.base.util.FytUtils;
//import com.pay.frame.common.base.util.RandomUtils;
//import com.pay.frame.common.base.util.StringUtils;
//import com.pay.tp.core.beans.fyt.FytFindUserReqBean;
//import com.pay.tp.core.beans.fyt.FytFindUserRespBean;
//import com.pay.tp.core.beans.fyt.FytRegisterReqBean;
//import com.pay.tp.core.beans.fyt.FytReqBean;
//import com.pay.tp.core.beans.fyt.FytRespBean;
//
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @description:
// * @date 2023/10/13 13:45
// */
//@Slf4j
//@Component
//public class FytBiz {
//
//	@Autowired
//	private RestTemplate restTemplate;
//    @Value("${proxy.squid.host:}")
//    private String proxyHost;
//
//    @Value("${proxy.squid.port:0}")
//    private int proxyPort;
//
//    private static final String FYT_SUCCESS_STATUS = "1";
//
//    @Value("${fyt.register.urlQa}")
//    private String registerUrlQa;
//    @Value("${fyt.register.url}")
//    private String registerUrl;
//    @Value("${fyt.findUser.urlQa}")
//    private String findUserUrlQa;
//    @Value("${fyt.findUser.url}")
//    private String findUserUrl;
//    @Value("${common.register.qa:false}")
//    private boolean registerQa;
//
//    /**
//     * fyt注册
//     *
//     * @param req
//     */
//    public ResultsBean<String> register(FytRegisterReqBean req) {
//        if (StringUtils.isBlank(req.getName())) {
//            req.setName("用户" + req.getPhone().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
//        }
//        String url = registerQa ? registerUrlQa : registerUrl;
//
//        FytRespBean fytRespBean = exeRequest(url, JSON.parseObject(JSON.toJSONString(req), Map.class));
//        log.info("fyt register resp:{}", JSON.toJSONString(fytRespBean));
//        if (FYT_SUCCESS_STATUS.equals(fytRespBean.getStatus())) {
//            return ResultsBean.SUCCESS();
//        }
//        return ResultsBean.FAIL(fytRespBean.getMessage());
//    }
//
//    /**
//     * 查询是否注册法援通用户
//     *
//     * @param req
//     * @return
//     */
//    public ResultsBean<FytFindUserRespBean> findUser(FytFindUserReqBean req) {
//        String url = registerQa ? findUserUrlQa : findUserUrl;
//        log.info("findUser查询是否注册法援通用户调用，不进行接口调用，直接返回SUCCESS");
//        FytRespBean fytRespBean = exeRequest(url, JSON.parseObject(JSON.toJSONString(req), Map.class));
//        log.info("fyt findUser resp:{}", JSON.toJSONString(fytRespBean));
//        if(StringUtils.isBlank(fytRespBean.getData())) {
//        	return ResultsBean.SUCCESS();
//        }else {
//        	return ResultsBean.SUCCESS(JSON.parseObject(fytRespBean.getData(), FytFindUserRespBean.class));
//        }
//    }
//    /**
//     * 请求头header加上签名sign和请求时间戳timestamp
//     *
//     * @return
//     */
//    private static Map<String, String> getReqHeaders(String sign) {
//        Map<String, String> hMap = new HashMap<>();
//        hMap.put("sign", sign);
//        hMap.put("Accept", "application/json;");
//        hMap.put("Content-Type", "application/json;charset=utf-8;");
//        hMap.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
//        return hMap;
//    }
//    /**
//     * 执行调用
//     *
//     * @param url
//     * @param params
//     * @return
//     */
//    @SneakyThrows
//    public FytRespBean exeRequest(String url, Map<String, Object> params) {
////        HttpPost post = new HttpPost(url);
////        RequestConfig config = RequestConfig.custom()
////                .setConnectTimeout(5000)
////                .setProxy(new HttpHost(proxyHost, proxyPort, "http"))
////                .build();
////        post.setConfig(config);
//
//        //转换有序map
//        String orderReqStrParam = FytUtils.getOrderReqStrParam(params);
//        //请求头
////        Map<String, String> headers = getReqHeaders(FytUtils.getSign(orderReqStrParam));
////        if (headers != null && !headers.isEmpty()) {
////            headers.forEach((k, v) -> {
////                post.addHeader(k, v);
////            });
////        }
//        //请求体
//        String iv = RandomUtils.getUUID().substring(0, 16);
//        String encryParam = FytUtils.getEncryParam(orderReqStrParam, iv);
//        FytReqBean fytReqBean = new FytReqBean().setData(encryParam)
//        		.setIv(iv)
//        		.setSign(FytUtils.getSign(orderReqStrParam))
//        		.setTimestamp(String.valueOf(System.currentTimeMillis() / 1000));
////        post.setEntity(new StringEntity(new ObjectMapper().writeValueAsString(fytReqBean), Charset.forName("UTF-8")));
////        //执行调用
//        long st = System.currentTimeMillis();
////        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
////        HttpResponse response = httpClient.execute(post);
////        String respDate = EntityUtils.toString(response.getEntity());
////        log.info("fyt exeRequest url:{} reqHeader:{} reqParam:{} originParam:{} encryParam:{} resp status:{} resp data:{} 耗时：{}ms", url, JSON.toJSONString(headers), JSON.toJSONString(fytReqBean), JSON.toJSONString(params), encryParam, response.getStatusLine().getStatusCode(), respDate, System.currentTimeMillis() - st);
////
////        if (HttpStatus.OK.value() == response.getStatusLine().getStatusCode()) {
////            return JSONObject.parseObject(respDate, FytRespBean.class);
////        } else {
////            throw new RuntimeException("调用接口失败");
////        }
//        
//        HttpEntity<FytReqBean> request = new HttpEntity<>(fytReqBean);
//        log.info("fyt url:{}  reqParam:{}  encryParam:{}", url, JSON.toJSONString(fytReqBean),  encryParam);
//        ResponseEntity<String> postForEntity = restTemplate.postForEntity(url, request, String.class);
//		log.info("fyt resp data:{} 耗时：{}ms", postForEntity, System.currentTimeMillis() - st);
//        if (HttpStatus.OK.value() == postForEntity.getStatusCodeValue()) {
//            return JSONObject.parseObject(postForEntity.getBody(), FytRespBean.class);
//        } else {
//            throw new RuntimeException("调用接口失败");
//        }
//
//    }
//
//    
////    public static void main(String[] args) {
////    	FytFindUserReqBean req=new FytFindUserReqBean();
////    	req.setPhone("13565652365");
////		findUser(req);
////	}
//}
