<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.msgmanage.MsgRecordMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.msgmanage.MsgRecord">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="SYS" jdbcType="VARCHAR" property="sys"/>
        <result column="REQUEST_NO" jdbcType="VARCHAR" property="requestNo"/>
        <result column="CONTENT" jdbcType="VARCHAR" property="content"/>
        <result column="PHONE" jdbcType="VARCHAR" property="phone"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="RESULT_CODE" jdbcType="VARCHAR" property="resultCode"/>
        <result column="RESULT" jdbcType="VARCHAR" property="result"/>
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode"/>
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="CHANNEL_USER_NO" jdbcType="VARCHAR" property="channelUserNo"/>
        <result column="MSG_TEMPLATE_ID" jdbcType="DECIMAL" property="msgTemplateId"/>
        <result column="MSG_TEMPLATE_PARAM" jdbcType="VARCHAR" property="msgTemplateParam"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, CONTENT, PHONE, "RESULT_CODE", "RESULT",
    CHANNEL_CODE, USER_NAME, CHANNEL_USER_NO, MSG_TEMPLATE_ID, MSG_TEMPLATE_PARAM
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_RECORD
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <select id="findByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_RECORD
        <where>
            1=1
            <if test="params.channelCode != null and params.channelCode != ''">
                and CHANNEL_CODE=#{params.channelCode}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and
                USER_NAME=#{params.userName,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.channelUserNo != null and params.channelUserNo != ''">
                and CHANNEL_USER_NO=#{params.channelUserNo}
            </if>
            <if test="params.msgTemplateId != null and params.msgTemplateId != ''">
                and MSG_TEMPLATE_ID=#{params.msgTemplateId}
            </if>
            <if test="params.requestNo != null and params.requestNo != ''">
                and REQUEST_NO=#{params.requestNo}
            </if>
            <if test="params.sys != null and params.sys != ''">
                and SYS=#{params.sys}
            </if>
            <if test="params.phone != null and params.phone != ''">
                and
                PHONE=#{params.phone,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
            </if>
            <if test="params.resultCode != null and params.resultCode != ''">
                and RESULT_CODE=#{params.resultCode}
            </if>
            <if test="params.createTimeStart !=null and params.createTimeStart !=''">
                and CREATE_TIME &gt;= to_date(#{params.createTimeStart} || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="params.createTimeEnd !=null and params.createTimeEnd !=''">
                and CREATE_TIME &lt;= to_date(#{params.createTimeEnd} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY ID DESC
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.msgmanage.MsgRecord">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_MSG_RECORD_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.MSG_RECORD (ID, OPTIMISTIC, CREATE_TIME, SYS,
        REQUEST_NO, CONTENT, PHONE,
        RESULT_CODE, "RESULT", CHANNEL_CODE,
        USER_NAME, CHANNEL_USER_NO, MSG_TEMPLATE_ID,
        MSG_TEMPLATE_PARAM)
        values (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
        #{sys,jdbcType=VARCHAR},
        #{requestNo,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{resultCode,jdbcType=VARCHAR}, #{result,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR},
        #{userName,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{channelUserNo,jdbcType=VARCHAR}, #{msgTemplateId,jdbcType=DECIMAL},
        #{msgTemplateParam,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.msgmanage.MsgRecord">
    update UBADMA.MSG_RECORD
    set OPTIMISTIC = OPTIMISTIC + 1,
      SYS = #{sys,jdbcType=VARCHAR},
      REQUEST_NO = #{requestNo,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      PHONE = #{phone,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
      RESULT_CODE = #{resultCode,jdbcType=VARCHAR},
      "RESULT" = #{result,jdbcType=VARCHAR},
      CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
      CHANNEL_USER_NO = #{channelUserNo,jdbcType=VARCHAR},
      MSG_TEMPLATE_ID = #{msgTemplateId,jdbcType=DECIMAL},
      MSG_TEMPLATE_PARAM = #{msgTemplateParam,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
    and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
  </update>

    <select id="findByRequestNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_RECORD
        where REQUEST_NO = #{requestNo,jdbcType=VARCHAR}
        and RESULT_CODE = 0
    </select>
    
    <delete id="deleteDingMsg" >
        delete from UBADMA.MSG_RECORD WHERE CREATE_TIME &gt;= SYSDATE -40 
        AND CREATE_TIME &lt;= SYSDATE -30 
        AND ROWNUM &lt; 10000
    </delete> 
</mapper>