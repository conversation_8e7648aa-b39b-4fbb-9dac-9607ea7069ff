package com.pay.tp.core.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * WILL_BODY_RECORD
 * <AUTHOR>
@Data
public class WillBodyRecord implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 版本标识
     */
    private Long optimistic;

    /**
     * 用户编号
     */
    private String ownerNo;

    /**
     * 服务费
     */
    private String amount;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 意愿核身类型
     */
    private String willType;

    /**
     * 活体检测分数
     */
    private String liveRate;

    /**
     * 人脸比对得分
     */
    private String similarity;

    /**
     * 验证状态
     * UNKNOWN ： 未验证
     * FINISH_VERIFY 已验证
     */
    private String validateStatus;

    /**
     * 人脸识别结果
     */
    private String faceCode;

    /**
     * 人脸识别结果描述
     */
    private String faceMsg;

    /**
     * 语音意愿表达结果
     */
    private String willCode;

    /**
     * 语音意愿表达结果描述
     */
    private String willMsg;

    /**
     * 地址
     */
    private String url;

    /**
     * 内容
     */
    private String content;

    /**
     * 来源
     */
    private String source;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    private Date updateTime;

    private String brand;

    /**
     * 阿里下载标识
     */
    private String aliFlag;

    /**
     * 阿里路径
     */
    private String aliPath;


    private String content1;

    private static final long serialVersionUID = 1L;

}