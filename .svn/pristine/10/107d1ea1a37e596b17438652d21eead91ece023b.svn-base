package com.pay.tp.core.mapper.sms;

import java.util.List;

import com.pay.tp.core.enums.Status;
import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.sms.SmsChannel;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.mapper
 * @Description: TODO
 * @date Date : 2018年12月25日 14:16
 */
public interface SmsChannelMapper {
	
    public List<SmsChannel> findActive(@Param("channelType") String channelType);
    
    List<SmsChannel> findByStatus(@Param("channelType") String channelType, @Param("status") String status);

    SmsChannel findById(@Param("id") String id);

    int updateStatusById(@Param("status") String status, @Param("id") Long id);

    int updateDisableAll(@Param("channelType") String channelType);

    List<SmsChannel> findAll();
    
    
}
