package com.pay.tp.core.service;

import com.pay.tp.core.entity.WxMsgTempCfg;
import com.pay.tp.core.mapper.WxMsgTempCfgMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> @date 2023/3/29
 * @apiNote
 */
@Service
public class WxMsgTempCfgService {

    @Autowired
    private WxMsgTempCfgMapper wxMsgTempCfgMapper;


    public List<WxMsgTempCfg> findByTempId(String tempId) {
        return wxMsgTempCfgMapper.findByTempId(tempId);
    }

}
