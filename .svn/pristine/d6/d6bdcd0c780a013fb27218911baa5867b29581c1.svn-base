package com.pay.tp.core.entity.jh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 聚合渠道配置
* <AUTHOR>
* @date 2022/4/21 10:54
*/
@Data
public class JhChannelCfg implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本标识
     */
    private Long optimistic;

    /**
     * 状态
     */
    private String status;

    /**
     * appid
     */
    private String appId;

    /**
     *渠道号
     */
    private String channelNo;

    /**
     *渠道类型 ZFB WX
     */
    private String channelType;

    /**
     *授权URL WX必填
     */
    private String authUrl;

    /**
     *创建时间
     */
    private Date createTime;

    /**
     *更新时间
     */
    private Date updateTime;

    /**
     *操作人
     */
    private String operator;

    /**
     *备注
     */
    private String remark;

    /**
     *品牌PLUS UK
     */
    private String brand;

    /**
     *访问token
     */
    private String accessToken;

    /**
     *APP密钥
     */
    private String appSecret;

}
