package com.pay.tp.core.controller.wx;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.frame.common.base.enums.Status;
import com.pay.tp.core.entity.WxMsgTemp;
import com.pay.tp.core.service.WxMsgTempService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> @date 2023/3/29
 * @apiNote
 */
@Slf4j
@RestController
@RequestMapping("/wxMsgTemp")
public class WxMsgTempController {

    @Autowired
    private WxMsgTempService wxMsgTempService;


    /**
     * 分页查询
     */
    @RequestMapping("/findByPageAll")
    public ResultsBean<PageInfo<WxMsgTemp>> findByPageAll(@RequestBody Map<String, Object> queryParams) {
        // 当前页
        int currentPage = queryParams.get("currentPage") == null ? 1: Integer.parseInt(queryParams.get("currentPage").toString());
        int pageSize = queryParams.get("pageSize") == null ? PayConstants.PAGE_SIZE: Integer.parseInt(queryParams.get("pageSize").toString());
        PageInfo<WxMsgTemp> page = wxMsgTempService.findByPageAll(currentPage, pageSize, queryParams);
        return ResultsBean.SUCCESS(page);
    }


    @RequestMapping("/findById")
    public ResultsBean<WxMsgTemp> findById(@RequestParam("id") Long id) {
        WxMsgTemp wxMsgTemp = wxMsgTempService.findById(id);
        return ResultsBean.SUCCESS(wxMsgTemp);
    }

    @RequestMapping("/update")
    public ResultsBean<String> update(@RequestBody WxMsgTemp wxMsgTemp) {
        WxMsgTemp msgTemp = wxMsgTempService.findById(wxMsgTemp.getId());
        msgTemp.setOperator(wxMsgTemp.getOperator());
        msgTemp.setTempName(wxMsgTemp.getTempName());
        msgTemp.setUrlType(wxMsgTemp.getUrlType());
        msgTemp.setTempUrl(wxMsgTemp.getTempUrl());
        msgTemp.setAppid(wxMsgTemp.getAppid());
        msgTemp.setTempRemark(wxMsgTemp.getTempRemark());
        msgTemp.setStatus(wxMsgTemp.getStatus());
        msgTemp.setTempTitle(wxMsgTemp.getTempTitle());
        wxMsgTempService.updateById(msgTemp);
        return ResultsBean.SUCCESS();
    }

    /**
     * 修改状态
     */
    @RequestMapping(value = "/modifyStatus")
    public ResultsBean<String> modifyStatus(@RequestBody WxMsgTemp wxMsgTemp) {
        try {
            WxMsgTemp msgTemp = wxMsgTempService.findById(wxMsgTemp.getId());
            if(Status.TRUE.getCode().equals(wxMsgTemp.getStatus())) {
                msgTemp.setStatus(Status.FALSE.getCode());
            }else {
                msgTemp.setStatus(Status.TRUE.getCode());
            }

            wxMsgTempService.updateById(msgTemp);
        } catch (Exception e) {
            return ResultsBean.FAIL(e.getMessage());
        }
        return ResultsBean.SUCCESS();
    }

}
