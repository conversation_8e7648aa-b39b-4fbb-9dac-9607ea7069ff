package com.pay.tp.core.service.auth;

import org.springframework.stereotype.Service;

import com.pay.tp.core.beans.auth.BankCardRes;
import com.pay.tp.core.remote.auth.jixin.JXCloudResp;

/**
 * @Description: 吉信鉴权解析
 * @see: AmiCloudService 此处填写需要参考的类
 * @version Nov 20, 2019 3:08:06 PM 
 * <AUTHOR>
 */
@Service
public class JxCloudService {


    public BankCardRes convertJX(JXCloudResp resp) {
    	BankCardRes jx = new BankCardRes();
        jx.setRspCod("000000");
        jx.setChannelRspCod(resp.getPlatformCode());
        jx.setRemark(resp.getPlatformDesc());
        jx.setRspMsg(resp.getPlatformDesc());

        switch (resp.getPlatformCode()) {
        	
        	/** 信息正确，收费 */
            case "*********":
                jx.setValidateStatus("01");
                break;
            /** 认证不一致，收费 */    
            case "*********":
                jx.setValidateStatus("02");	
                break;
            case "*********":
                jx.setValidateStatus("02");	
                break;
            case "*********":
                jx.setValidateStatus("02");	
                break;
            case "*********":
                jx.setValidateStatus("02");	
                break;
            case "*********":
                jx.setValidateStatus("02");	
                break;
            case "*********":
                jx.setValidateStatus("02");	
                break;
            case "001010093":
                jx.setValidateStatus("02");	
                break;
            /** 认证失败，不收费 */    
            default:
                jx.setValidateStatus("04");	
                break;
        }
        return jx;
    }
}
