package com.pay.tp.core.service.auth;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.pay.tp.core.exception.AuthRemoteException;
import com.pay.tp.core.beans.auth.BankCardRes;
import com.pay.tp.core.remote.auth.xinlian.XinlianRes;

/**
 * @Description: 信联鉴权解析
 * @see: AmiCloudService 此处填写需要参考的类
 * @version Nov 20, 2019 3:08:06 PM 
 * <AUTHOR>
 */
@Service
public class XlCloudService {

	/**
	 * @Description  一句话描述方法用法
	 * @param resp
	 * 000000	交易成功	　
		000001	账号余额不足	　
		000002	账号错误或被禁用	
		000003	请求报文不合法	　
		000004	交易失败	　
		000005	认证账户尝试次数过多 
			1、同一张身份证一天验证不能超过10次；
			2、同一银行卡号验证不能超过3次；
		认证受限后建议隔日再试。
		000011	机构标识和操作员不合法	　
		000012	该笔交易流水已存在	　
		000014	通道不支持该笔交易	
		000042	报文解密失败	
		000043	报文核签失败	
		000092	账户类型不支持	
		000097	其他错误	　
		000098	交易状态未知	　
		000099	系统异常	　

	 * @return
	 * @see 认证结果： 
		01-认证一致  （收费）
		02-认证不一致（收费）
		03-认证不确定（不收费）
		04-认证失败  （不收费）
		05-认证受限（收费
	 */
    public BankCardRes convertXl(XinlianRes resp) {
    	BankCardRes xl = new BankCardRes();
    	if("000001".equals(resp.getRspCod())) {
    		 throw new AuthRemoteException("账号余额不足");
    	}
    	if("000002".equals(resp.getRspCod())) {
    		throw new AuthRemoteException("账号错误或被禁用");
    	}
    	
    	xl.setRspCod(resp.getRspCod());	// 返回编码
    	xl.setChannelRspCod(resp.getRspCod());	// 通道返回码
    	xl.setRemark(resp.getRemark());	// 描述
    	xl.setRspMsg(resp.getRspMsg());	// 返回结果
    	xl.setCooperSerialNo(resp.getCooperSerialNo());
    	xl.setCardType(resp.getCardType());
    	if(StringUtils.isEmpty(resp.getRemark())) {
    		xl.setRemark(resp.getRspMsg());	// 描述
    	}
    	if("02".equals(resp.getValidateStatus())) {
    		xl.setRemark("验证信息不一致");	// 描述
    	}
    	
    	xl.setBankName(resp.getBankName()); // 银行名称
    	xl.setValidateStatus(resp.getValidateStatus()); // 验证状态
    	xl.setOriTransDate(resp.getOriTransDate()); // 交易时间
    	xl.setPaySerialNo(resp.getPaySerialNo()); // 支付序列号
        

        return xl;
    }
}
