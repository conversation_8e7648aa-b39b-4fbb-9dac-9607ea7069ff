package com.pay.tp.auth.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.tp.BaseTest;
import com.pay.tp.core.beans.auth.TpAuthChannelParam;
import com.pay.tp.core.entity.auth.TpAuthChannel;
import com.pay.tp.core.enums.BusinessCode;
import com.pay.tp.core.enums.ChannelStatus;
import com.pay.tp.core.service.auth.AuthChannelService;

public class AuthChannelServiceTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(AuthChannelServiceTest.class);

    @Autowired
    private AuthChannelService authChannelService;

    @Test
    public void testAddOrMod()
    {
        TpAuthChannelParam authChannelParam = new TpAuthChannelParam();
        authChannelParam.setBusinessCode(BusinessCode.BANK_CARD_AUTH);
        authChannelParam.setChannelNo("MOCK");
        authChannelParam.setChannelName("挡板");
        authChannelParam.setStatus(ChannelStatus.ENABLE);
        authChannelParam.setPriority(99);
        authChannelService.addOrModChannel(authChannelParam);
    }

    @Test
    public void testFindUni()
    {
        TpAuthChannel authChannel = authChannelService.findByUni(BusinessCode.BANK_CARD_AUTH.name(),"JX");
        logger.info("查询结果：{}",authChannel);
    }

    @Test
    public void testFindBusi()
    {
        List<TpAuthChannel> retList = authChannelService.findByBusiCodeAndStatus(BusinessCode.BANK_CARD_AUTH.name(),ChannelStatus.ENABLE.name());
        retList.forEach(a ->
                logger.info("可用通道：{}",a)
        );
    }

    @Test
    public void testFindPage()
    {
        Map<String,String> queryParam = new HashMap<>();
        PageInfo<Map<String, Object>> pageInfo = authChannelService.findPageAuthChannelList(1, PayConstants.PAGE_SIZE,queryParam);
        List<Map<String, Object>> retList = pageInfo.getList();
        retList.forEach(a ->
                logger.info("分页通道：{}",a)
        );
    }
}
