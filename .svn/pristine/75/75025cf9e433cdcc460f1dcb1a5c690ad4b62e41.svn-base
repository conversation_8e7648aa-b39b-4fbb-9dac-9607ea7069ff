package com.pay.tp.core.biz.impl;

import com.github.pagehelper.util.StringUtil;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.tp.core.beans.wx.SendWxMsgReq;
import com.pay.tp.core.beans.wx.WxMsgTemplateBean;
import com.pay.tp.core.entity.WxMsgTemp;
import com.pay.tp.core.entity.WxMsgTempCfg;
import com.pay.tp.core.entity.msgmanage.MsgChannel;
import com.pay.tp.core.entity.msgmanage.MsgChannelConfig;
import com.pay.tp.core.entity.msgmanage.MsgRecord;
import com.pay.tp.core.enums.ChannelType;
import com.pay.tp.core.enums.Status;
import com.pay.tp.core.remote.wx.WeChatClient;
import com.pay.tp.core.service.WxMsgTempCfgService;
import com.pay.tp.core.service.WxMsgTempService;
import com.pay.tp.core.service.msgmanage.MsgChannelConfigService;
import com.pay.tp.core.service.msgmanage.MsgChannelService;
import com.pay.tp.core.service.msgmanage.MsgRecordService;
import com.pay.tp.core.service.msgmanage.MsgTemplateService;
import com.pay.tp.core.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Slf4j
@Component
public class WeChatBiz {

    @Autowired
    private MsgChannelService msgChannelService;

    @Autowired
    private MsgChannelConfigService msgChannelConfigService;

    @Autowired
    private MsgTemplateService msgTemplateService;

    @Autowired
    private MsgRecordService msgRecordService;

    @Autowired
    private WeChatClient weChatClient;

    @Autowired
    private WxMsgTempService wxMsgTempService;

    @Autowired
    private WxMsgTempCfgService wxMsgTempCfgService;


    /**
     * 获取微信所有通道
     * @return
     */
    public List<MsgChannel> findWxAllChannel() {
        return msgChannelService.findByType(ChannelType.WX_PUSH.name());
    }


    /**
     *  更新微信公众号的 access_token
     * @param brand
     */
    public void loadAccessToken(String brand) {
        String accessToken =  weChatClient.getWeChatAccessToken(brand);

        MsgChannelConfig config = new MsgChannelConfig();
        config.setCfgParam("access_token_"+brand)
                .setCfgValue(accessToken)
                .setChannelCode(ChannelType.WX_PUSH.name())
                .setStatus(Status.ENABLE);
        msgChannelConfigService.save(config);
    }


    /**
     * 发送消息
     * @param sendWxMsgReq
     */
    public void sendWxMessage(SendWxMsgReq sendWxMsgReq) {
        String tempType = sendWxMsgReq.getTemplateType();
        String openid = sendWxMsgReq.getOpenid();
        String brand = sendWxMsgReq.getBrand();
        Map<String, String> msgMap = sendWxMsgReq.getMsgMap();

        WxMsgTemp wxMsgTemp = wxMsgTempService.findByTempType(tempType, brand);
        if(null == wxMsgTemp){
            throw new ServerException("模板信息不存在"+ sendWxMsgReq.getUserNo()+sendWxMsgReq.getTemplateType());
        }

        List<WxMsgTempCfg> tempCfgList = wxMsgTempCfgService.findByTempId(wxMsgTemp.getTempId());
        if(CollectionUtils.isEmpty(tempCfgList)){
            throw new ServerException("模板配置参数不存在"+ sendWxMsgReq.getUserNo()+sendWxMsgReq.getTemplateType());
        }

         // 拼接发送参数
        LinkedHashMap<String, Object> map = getWxData(msgMap, tempCfgList, wxMsgTemp);
//        log.info("拼接Data的数据：{}, {}", sendWxMsgReq.getRequestNo(), map);

        //  微信模板code
        WxMsgTemplateBean wxMsgTemplate = new WxMsgTemplateBean();
        wxMsgTemplate.setTouser(openid);
        wxMsgTemplate.setTemplate_id(wxMsgTemp.getTempId());
        wxMsgTemplate.setData(map);
//        wxMsgTemplate.setTemplate_id(openid+sendWxMsgReq.getRequestNo());
        if("XCX".equals(wxMsgTemp.getUrlType())){
            WxMsgTemplateBean.Miniprogram miniprogram = new WxMsgTemplateBean.Miniprogram();
            miniprogram.setAppid(wxMsgTemp.getAppid());
            miniprogram.setPagepath(wxMsgTemp.getTempUrl());
            wxMsgTemplate.setMiniprogram(miniprogram);
        }else {
            wxMsgTemplate.setUrl(wxMsgTemp.getTempUrl());
        }

        MsgChannelConfig config = msgChannelConfigService.findByChannelCodeAndCfgParam(
                                        ChannelType.WX_PUSH.name(), "access_token_"+ brand);
        Map sendResult = weChatClient.sendWxMessage(wxMsgTemplate, config.getCfgValue());

        saveMsgRecord(sendResult, wxMsgTemplate, map, sendWxMsgReq, wxMsgTemp.getId());
//        log.info("推送微信公众号end", sendWxMsgReq);
    }

    /** 组装参数 */
    private LinkedHashMap<String, Object> getWxData(Map<String, String> msgMap, List<WxMsgTempCfg> msgList,
                                                    WxMsgTemp wxMsgTemp) {
        // 备注不为空放进去
        if (StringUtil.isNotEmpty(wxMsgTemp.getTempRemark())) {
            msgMap.put("remark", wxMsgTemp.getTempRemark());
        }
//        if (StringUtil.isNotEmpty(wxMsgTemp.getTempTitle())) {
//        	msgMap.put("title", wxMsgTemp.getTempTitle());
//        }
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
//        log.info("消息key 模板key:{},{}", msgMap, msgList);
        for(String key: msgMap.keySet()){
            WxMsgTempCfg wxMsgTempCfg = msgList.stream().filter(p -> p.getKey().equals(key)).findFirst().get();

            if(null != wxMsgTempCfg){
                WxReq wxReq = new WxReq();
                if (StringUtil.isNotEmpty(wxMsgTempCfg.getTempColor())){
                    wxReq.setColor(wxMsgTempCfg.getTempColor());
                }
                wxReq.setValue(msgMap.get(key));
                map.put(wxMsgTempCfg.getTempKey(), wxReq);
            }

        }

        return map;
    }

    /** 保存消息 */
    private void saveMsgRecord(Map sendResult, WxMsgTemplateBean wxMsgTemplate, LinkedHashMap<String, Object> map,
                               SendWxMsgReq sendWxMsgReq, Long id) {
        MsgRecord msgRecord = new MsgRecord();
        msgRecord.setResultCode(sendResult.get("errcode").toString());
        msgRecord.setResult(JsonUtil.toJson(sendResult));
        msgRecord.setChannelCode(ChannelType.WX_PUSH.name());
        msgRecord.setMsgTemplateId(id);
        msgRecord.setContent(JsonUtil.toJson(wxMsgTemplate));
        msgRecord.setMsgTemplateParam(JsonUtil.toJson(map));
        String requestNo = ChannelType.WX_PUSH.name() +"_"+ sendWxMsgReq.getBrand() + "_" + sendWxMsgReq.getRequestNo();
        msgRecord.setRequestNo(requestNo);
        msgRecord.setUserName(sendWxMsgReq.getUserNo());
        msgRecord.setPhone(sendWxMsgReq.getPhone());
        msgRecord.setCreateTime(new Date());
        msgRecord.setOptimistic(0L);
        msgRecordService.insert(msgRecord);
    }


    @Data
    public static class WxReq {
        private String value;
        private String color;
    }


}
