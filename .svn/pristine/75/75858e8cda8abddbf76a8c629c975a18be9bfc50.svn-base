package com.pay.tp.core.exception;

/**
 * 定义服务异常
 *
 * <AUTHOR>
 */
public class SmsDingDingException extends RuntimeException {

    protected String errCode;

    protected String errMsg;

    public SmsDingDingException(String errCode, String errMsg) {
        super(errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public SmsDingDingException(String errCode, String errMsg, String message) {
        super(message);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public SmsDingDingException(String errCode, String errMsg, Throwable e) {
        super(errMsg, e);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
