package com.pay.tp.core.controller.dingding;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.sms.SmsMsgReq;
import com.pay.tp.core.biz.impl.DingDingBiz;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Package com.pay.sms.core.controller
 * @Description: 发送钉钉消息
 * @date Date : 2021年01月07日 18:03
 */
@RestController
@RequestMapping("/dingding")
public class DingDingMsgController {
    private final Logger logger = LoggerFactory.getLogger(DingDingMsgController.class);

    @Autowired
    private DingDingBiz dingDingBiz;


    /**
     * 发送钉钉工作通知消息
     *
     * @param dingdingMsgReq
     * @return code = 00，发送成功; code = 99, 发送失败; msg在失败时候为原因说明
     */
    @Deprecated
    @PostMapping("/msg/work")
    public ResultsBean<String> sendMsg(@RequestBody SmsMsgReq dingdingMsgReq) {
        logger.info("DingDingMsgController sendWorkMsg {}。", dingdingMsgReq);
        boolean b = dingDingBiz.sendWorkMsg4TemplateCode(dingdingMsgReq.getRequestNo(),
                dingdingMsgReq.getTemplateCode(),
                dingdingMsgReq.getTemplateParam());

        if (b) {
            return ResultsBean.SUCCESS("发送成功");
        } else {
            return ResultsBean.FAIL("发送失败");
        }
    }

    /**
     *
     * 新建通知 接入此接口
     * 发送钉钉机器人通知消息
     *
     * @param dingdingMsgReq
     * @return code = 00，发送成功; code = 99, 发送失败; msg在失败时候为原因说明
     */
    @PostMapping("/msg/send")
    public ResultsBean<String> msgSend(@RequestBody SmsMsgReq dingdingMsgReq) {
        logger.info("DingDingMsgController sendRobotMsg {}。", dingdingMsgReq);
        boolean b = dingDingBiz.sendRobotMsg2TemplateCode(dingdingMsgReq.getRequestNo(),
                dingdingMsgReq.getTemplateCode(),
                dingdingMsgReq.getTemplateParam());

        if (b) {
            return ResultsBean.SUCCESS("发送成功");
        } else {
            return ResultsBean.FAIL("发送失败");
        }
    }

    /**
     * 装载AccessToken
     *
     * @param channelCode
     * @return code = 00，成功; code = 99, 失败; msg在失败时候为原因说明
     */
    @GetMapping("/accessToken/load/{channelCode}")
    public ResultsBean<String> loadAccessToken(@PathVariable("channelCode") String channelCode) {
        logger.info("装载 AccessToken {}。", channelCode);
        boolean b = dingDingBiz.loadAccessToken(channelCode);
        if (b) {
            return ResultsBean.SUCCESS("发送成功");
        } else {
            return ResultsBean.FAIL("发送失败");
        }
    }

}
