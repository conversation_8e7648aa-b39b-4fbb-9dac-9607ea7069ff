//package com.pay.tp.core.job;
//
//import java.util.List;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Conditional;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import com.pay.tp.core.biz.impl.DingDingBiz;
//import com.pay.tp.core.configuration.JobRegisterCondition;
//import com.pay.tp.core.entity.msgmanage.MsgChannel;
//
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 通道健康检查任务，保证通道可用，不可用时进行报警
// *
// * <AUTHOR>
// */
//@Slf4j
//@Component
//@Conditional({JobRegisterCondition.class})
//public class SmsMsgChannelCheckHealthTask {
//
//    /**
//     * 钉钉通道业务处理
//     */
//    @Autowired
//    private DingDingBiz dingDingBiz;
//
//
//    @Scheduled(cron = "0 0 0/1 * * ?")
//    public void dingDingHealthCheck() {
//        try {
//            List<MsgChannel> channels = dingDingBiz.findAllChannel();
//            channels.forEach(channel -> {
//                boolean isHealth = dingDingBiz.loadAccessToken(channel.getCode());
//                if (isHealth) {
//                    log.info("ding ding channel is healthy: {}", channel.getCode());
//                } else {
//                    log.error("ding ding channel is dead: {}", channel.getCode());
//                }
//            });
//        } catch (Exception e) {
//            log.error("ding ding channel health check error ", e);
//        }
//    }
//}
