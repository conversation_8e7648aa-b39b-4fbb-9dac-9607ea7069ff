package com.pay.tp.core.service.position;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.beans.position.CoordinateParam;
import com.pay.tp.core.entity.position.Coordinate;
import com.pay.tp.core.mapper.position.CoordinateMapper;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.service
 * @Description: TODO
 * @date Date : 2018年12月25日 16:38
 */
@Service
public class CoordinateServiceImpl implements CoordinateService{
    
	@Autowired
    private CoordinateMapper coordinateMapper;


    @Override
    public Coordinate query(CoordinateParam param) {
        Coordinate o=coordinateMapper.query(param);
        if (o!=null) {
            return o;
        }else {
            Coordinate coordinate=new Coordinate(param.getRequestNo(),"", param.getLongitude(), param.getLatitude());
            coordinateMapper.insert(coordinate);
            return coordinate;
        }
    }

    @Override
    public void update(Coordinate o) {
        coordinateMapper.update(o);
    }
    
}
