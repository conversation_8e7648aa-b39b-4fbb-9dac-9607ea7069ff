package com.pay.tp.core.remote.auth;

import com.pay.tp.core.beans.auth.BankCardRes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.remote.PayChannelClient;
import com.pay.tp.core.remote.PayChannelClient.AuthVerifyReq;
import com.pay.tp.core.service.auth.AmiCloudService;

/**
 * @Description: 掌讯鉴权
 * @see: DefaultZXBankCardAuthClient 此处填写需要参考的类
 * @version Nov 20, 2019 3:05:18 PM 
 * <AUTHOR>
 */
@Service("ldysAuthClient")
//@Profile({"proc"})
public class LdysBankCardAuthClient implements BankCardAuthClient {
    private final Logger logger = LoggerFactory.getLogger(LdysBankCardAuthClient.class);


    @Autowired
    private PayChannelClient payChannelClient;
    @Autowired
    private AmiCloudService amiCloudService;

	/**
	 * @Description 认证请求
	 * @param outTradeNo   请求号
	 * @param cardNo       卡号
	 * @param name         名称
	 * @param cidNo        身份证号
	 * @param mobile       手机号
	 * @param customerName 认证人
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
    @Override
    public BankCardRes request(String outTradeNo, String cardNo, String name, String cidNo, String mobile, String customerName, String brand)
            throws Exception {
        logger.info("method =request LDYS {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}",
                outTradeNo, cardNo, name, cidNo, mobile, customerName);

        AuthVerifyReq req=new AuthVerifyReq();
        req.setBrand(brand);
        req.setBankAccountNo(cardNo);
        req.setIdentityNo(cidNo);
        req.setIdentityName(name);
        req.setPrePhone(mobile);
		ResultsBean<String> authVer = payChannelClient.authVer(req);
		  logger.info("method =request LDYS {}, req = {}, res = {}", req, authVer);
        return amiCloudService.convertLdys(authVer);

    }

}


