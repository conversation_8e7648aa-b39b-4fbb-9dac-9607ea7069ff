package com.pay.tp.core.remote.auth.xinlian;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.pay.tp.core.entity.auth.CompanyCheck;

/**
 *
 */
@Service
@Profile({"proc"})
public class DefaultXlCompanyCheckClient  implements XlCompanyCheckClient{

    private final Logger logger = LoggerFactory.getLogger(DefaultXlCompanyCheckClient.class);

    @Value("${custom.xinlian.company.check.url:}")
    private String url;

    @Value("${custom.xinlian.insId:}")
    private String insId;

    @Value("${custom.xinlian.operId:}")
    private String operId;

    private String version = "1.0";

    private String charset = "UTF-8";

    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;



    //01-	匹配
    //02-	不匹配
    //03-	未报送
    //04-	认证失败

    @Override
    public Map<String,Object> parse(CompanyCheck companyCheck){

        Map<String,Object> map = new HashMap<>();

        if("000000".equals(companyCheck.getRspCod())){
            String validateStatus = companyCheck.getValidateStatus();
            if("01".equals(validateStatus)){
                map.put("result", validateStatus);
                map.put("msg", companyCheck.getValidateDescribe());
            }else{
                map.put("result","02");
                map.put("msg", companyCheck.getValidateDescribe());
            }

        }else{
            map.put("result","02");
            map.put("msg", companyCheck.getRspMsg());
        }

        logger.info("metho = parse, companyCheck = {}, map = {}", companyCheck, map);

        return map;
    }


    @Override
    public Map<String,Object> request(String serialNo, String entName, String regNo, String frName, String cidNo) throws Exception {
        logger.info("method = request, entName = {}, regNo = {}, frName = {}, cidNo = {}, serialNo = {}", entName, regNo, frName, cidNo, serialNo);
        Gson gson = new Gson();
        RSAHelper cipher = new RSAHelper();// 初始化自己的私钥,对方的公钥以及密钥长度.
        cipher.initKey(2048);
        Map<String, String> param = new HashMap<>();
        param.put("entName", entName);
        param.put("regNo", regNo);
        param.put("frName", frName);
        param.put("cidNo", cidNo);
        param.put("serialNo", serialNo);
        param.put("version", version);


        String jsonStr = gson.toJson(param);
        // 签名
        byte[] signBytes = cipher.signRSA(jsonStr.getBytes(charset), false,
                charset);
        // 对明文加密
        byte[] cryptedBytes = cipher.encryptRSA(jsonStr.getBytes(charset),
                false, charset);

        String content = "";
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        if(!StringUtils.isEmpty(proxyHost)){
            RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
            httpPost.setConfig(config);
        }
        List<NameValuePair> formParams = new ArrayList<NameValuePair>();
        formParams.add(new BasicNameValuePair("insId", insId));
        formParams.add(new BasicNameValuePair("operId", operId));
        formParams.add(new BasicNameValuePair("sign", Base64.encodeBase64String(signBytes)));
        formParams.add(new BasicNameValuePair("encrypt", Base64.encodeBase64String(cryptedBytes)));
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(formParams, charset));
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity resEntity = httpResponse.getEntity();
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                if (resEntity != null) {
                    content = EntityUtils.toString(resEntity, charset);
                    logger.info("method = request, content = {}", content);
                }else{
                    throw new RuntimeException("无返回结果");
                }
                EntityUtils.consume(resEntity);
            }
        } catch (Exception e) {
            logger.error("",e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {}
        }
        Map<String, String> map = gson.fromJson(content, HashMap.class);
        //签名
        signBytes = Base64.decodeBase64(map.get("sign"));
        //密文
        cryptedBytes = Base64.decodeBase64(map.get("encrypt"));
        // 对密文解密
        byte[] decryptedBytes = cipher.decryptRSA(cryptedBytes, false, charset);
        String body = new String(decryptedBytes, charset);

        logger.info("method = request, body = {}", body);

        boolean isValid = cipher.verifyRSA(decryptedBytes,
                signBytes, false, charset);
        logger.info("method = request, response = {} isValid = {}", body, isValid);

        if (isValid) {
            return gson.fromJson(body, Map.class);
        }else{
            throw new RuntimeException("签名有误");
        }
    }
}