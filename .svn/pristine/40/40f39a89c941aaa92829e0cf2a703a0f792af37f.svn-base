package com.pay.tp.core.remote.auth.zhangxun;

import java.io.IOException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.pay.tp.core.exception.AuthRemoteException;
import com.pay.tp.core.remote.auth.BankCardAuthClient;
import com.pay.tp.core.beans.auth.BankCardRes;
import com.pay.tp.core.service.auth.AmiCloudService;

import sun.misc.BASE64Decoder;

/**
 * @Description: 掌讯鉴权
 * @see: DefaultZXBankCardAuthClient 此处填写需要参考的类
 * @version Nov 20, 2019 3:05:18 PM 
 * <AUTHOR>
 */
@Service("zxAuthClient")
//@Profile({"proc"})
public class DefaultZXBankCardAuthClient implements BankCardAuthClient {
    private final Logger logger = LoggerFactory.getLogger(DefaultZXBankCardAuthClient.class);

    @Value("${custom.zhangxun.bankcard.auth.url:}")
    private String url;
    @Value("${custom.zhangxun.bankcard.auth.merId:}")
    private String merId;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    /**
     * 掌讯提供的公钥
     */
    @Value("${own.zhangxun.zxpubkey}")
    public String zxPubKey;
    /**
     * 掌讯提供的私钥
     */
    @Value("${own.zhangxun.zxprivkey}")
    public String zxPrivKey;
    /**
     * 掌讯提供的3des
     */
    @Value("${own.zhangxun.zxdeskey}")
    public String zxDesKey;

    @Autowired
    private AmiCloudService amiCloudService;

    

	/**
	 * @Description 认证请求
	 * @param outTradeNo   请求号
	 * @param cardNo       卡号
	 * @param name         名称
	 * @param cidNo        身份证号
	 * @param mobile       手机号
	 * @param customerName 认证人
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
    @Override
    public BankCardRes request(String outTradeNo, String cardNo, String name, String cidNo, String mobile, String customerName,String brand)
            throws Exception {
        logger.info("method =request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}",
                outTradeNo, cardNo, name, cidNo, mobile, customerName);

        Gson gson = new Gson();
        Map<String, String> param = new HashMap<>();
        param.put(Constants.out_trade_no, outTradeNo);
        param.put(Constants.tran_time,  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()));
        if(!org.springframework.util.StringUtils.isEmpty(mobile)){
            param.put(Constants.phone, mobile);
            param.put(Constants.verify_type, Constants.verifyType4);
        }else {
            param.put(Constants.verify_type, Constants.verifyType3);
        }
        param.put(Constants.acct_no, cardNo);
        param.put(Constants.name, new String(name.getBytes(), "UTF-8"));
        param.put(Constants.cert_no, cidNo); // TODO
        param.put(Constants.certType, "");
        param.put(Constants.customerCode, merId);

        // 签名
        byte[] signBytes = signRSAzx(builderSignInfo(param).getBytes(Constants.charset), false, Constants.charset);

        param.put(Constants.sign, new String(Base64.encodeBase64(signBytes)));

        // 3des 加密
        String jsonStr = gson.toJson(param);
        logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 请求报文 = {}",
        		outTradeNo, cardNo, name, cidNo, mobile, customerName, jsonStr);

        byte[] cryptedBytes = sign3DES(jsonStr.getBytes());

        String jsonReq = byte2Hex(cryptedBytes);

        String content = "";
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url + merId);
        if (!StringUtils.isEmpty(proxyHost)) {
            RequestConfig config = RequestConfig.custom().setProxy(new HttpHost(proxyHost, proxyPort, "http")).build();
            httpPost.setConfig(config);
        }

        try {

            StringEntity entity = new StringEntity(jsonReq, "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity resEntity = httpResponse.getEntity();
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, statusCode = {}",
            		outTradeNo, cardNo, name, cidNo, mobile, customerName, statusCode);
            if (statusCode == HttpStatus.SC_OK) {
                if (resEntity != null) {
                    content = EntityUtils.toString(resEntity, Constants.charset);
                    logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, content = {}",
                    		outTradeNo, cardNo, name, cidNo, mobile, customerName, content);
                } else {
                	logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 无返回结果",
                			outTradeNo, cardNo, name, cidNo, mobile, customerName);
                    throw new AuthRemoteException("无返回结果");
                }
                EntityUtils.consume(resEntity);
            }else {
            	throw new AuthRemoteException("请求失败：" + statusCode);
            }
        } catch (Exception e) {
            logger.error("", e);
            throw new AuthRemoteException(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
            }
        }


        Map<String, String> map = gson.fromJson(content, HashMap.class);
        logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, map = {}",
        		outTradeNo, cardNo, name, cidNo, mobile, customerName, map);
        try {
            String signContent = map.get("sign");
            String CODE = map.get("code");
            String MESSAGE = map.get("message");
            String OUT_TRADE_NO = map.get("out_trade_no");
            String TRAN_AMT = map.get("tran_amt");
            String TRAN_TIME = map.get("tran_time");

            StringBuilder signbuilder = new StringBuilder();
            signbuilder.append(Constants.code.toUpperCase() + "=" + CODE);
            signbuilder.append("&" + Constants.message.toUpperCase() + "=" + MESSAGE);
            signbuilder.append("&" + Constants.out_trade_no.toUpperCase() + "=" + OUT_TRADE_NO);
            signbuilder.append("&" + Constants.tran_amt.toUpperCase() + "=" + TRAN_AMT);
            signbuilder.append("&" + Constants.tran_time.toUpperCase() + "=" + TRAN_TIME);

            boolean signResult = decryptRSAZX(signbuilder.toString(), signContent, Constants.charset);
            if (signResult) {
            	logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 验签成功",
                		outTradeNo, cardNo, name, cidNo, mobile, customerName);
            } else {
            	logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 验签失败",
                		outTradeNo, cardNo, name, cidNo, mobile, customerName);
            }
        } catch (Exception e) {
        	logger.info("method = request ZX {}, cardNo = {}, name = {}, cidNo = {}, mobile = {}, customerName = {}, 验签异常, e = {}",
        			outTradeNo, cardNo, name, cidNo, mobile, customerName, e.getMessage(), e);
        }


        return amiCloudService.convertZX(gson.fromJson(content, AmiCloudResp.class));

    }

    public String builderSignInfo(Map<String, String> param) {

        StringBuilder builder = new StringBuilder();
        builder.append(Constants.out_trade_no.toUpperCase() + "=" + param.get(Constants.out_trade_no));
        builder.append("&" + Constants.tran_time.toUpperCase() + "=" + param.get(Constants.tran_time));
        builder.append("&" + Constants.verify_type.toUpperCase() + "=" + param.get(Constants.verify_type));
        builder.append("&" + Constants.acct_no.toUpperCase() + "=" + param.get(Constants.acct_no));
        builder.append("&" + Constants.name.toUpperCase() + "=" + param.get(Constants.name));
        builder.append("&" + Constants.cert_no.toUpperCase() + "=" + param.get(Constants.cert_no));
        if(!org.springframework.util.StringUtils.isEmpty(param.get(Constants.phone))){
            builder.append("&" + Constants.phone.toUpperCase() + "=" + param.get(Constants.phone));
        }
        String builderStr = builder.toString();
        logger.info("method = builderSignInfo {} ", builderStr);
        return builderStr;
    }

    /**
     * RSA签名
     *
     * @param localPrivKey
     *            私钥
     * @param plaintext
     *            需要签名的信息
     * @return byte[]
     * @throws Exception
     */
    public byte[] signRSAzx(byte[] plainBytes, boolean useBase64Code,
                            String charset) throws Exception {
        String SIGNATURE_ALGORITHM = "SHA1withRSA";
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(getPrivateKey(zxPrivKey));
        signature.update(plainBytes);

        // 如果是Base64编码的话，需要对签名后的数组以Base64编码
        if (useBase64Code) {
            return Base64.encodeBase64String(signature.sign())
                    .getBytes(charset);
        } else {
            return signature.sign();
        }
    }

    /**
     * 3DES 加密
     * @param src
     * @return
     */
    public byte[] sign3DES(byte[] src) {

        try {
            // 生成密钥
            SecretKey deskey = new SecretKeySpec(zxDesKey.getBytes(), Constants.Algorithm); // 加密
            Cipher c1 = Cipher.getInstance(Constants.Algorithm);
            c1.init(Cipher.ENCRYPT_MODE, deskey);
            return c1.doFinal(src);
        } catch (java.security.NoSuchAlgorithmException e) {
        } catch (javax.crypto.NoSuchPaddingException e) {
        } catch (java.lang.Exception e) {
        }
        return null;

    }

    // 转换成十六进制字符串
    public String byte2Hex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
            // if (n < b.length - 1)
            // hs = hs + ":";
        }
        return hs.toUpperCase();
    }

    public boolean decryptRSAZX(String content, String sign, String charset) {
        try {

            java.security.Signature signature = java.security.Signature.getInstance(Constants.SIGN_ALGORITHMS);

            signature.initVerify(getPublicKey(zxPubKey));

            if (Strings.isNullOrEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }

            return signature.verify(Base64.decodeBase64(sign.getBytes()));
        } catch (Exception e) {
            logger.error("decryptRSAZX error", e);
            return false;
        }
    }

    /**
     * 从字符串中加载公钥
     *
     * @param publicKeyStr 公钥数据字符串
     * @return 返回公钥
     * @throws Exception 加载公钥时产生的异常
     */
    public RSAPublicKey getPublicKey(String publicKeyStr) throws Exception {
        try {
            BASE64Decoder base64Decoder = new BASE64Decoder();
            byte[] buffer = base64Decoder.decodeBuffer(publicKeyStr);
            KeyFactory keyFactory = KeyFactory.getInstance(Constants.SIGN_TYPE_RSA);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
            RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);
            return publicKey;
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("公钥非法");
        } catch (IOException e) {
            throw new Exception("公钥数据内容读取错误");
        } catch (NullPointerException e) {
            throw new Exception("公钥数据为空");
        }
    }

    /**
     * 从字符串中加载私钥
     *
     * @param privateKeyStr 公钥数据字符串
     * @return 返回私钥
     * @throws Exception 加载私钥时产生的异常
     */
    public RSAPrivateKey getPrivateKey(String privateKeyStr) throws Exception {
        try {
            BASE64Decoder base64Decoder = new BASE64Decoder();
            byte[] buffer = base64Decoder.decodeBuffer(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
            KeyFactory keyFactory = KeyFactory.getInstance(Constants.SIGN_TYPE_RSA);
            RSAPrivateKey privateKey = (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
            return privateKey;
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("私钥非法");
        } catch (IOException e) {
            throw new Exception("私钥数据内容读取错误");
        } catch (NullPointerException e) {
            throw new Exception("私钥数据为空");
        }
    }

}


