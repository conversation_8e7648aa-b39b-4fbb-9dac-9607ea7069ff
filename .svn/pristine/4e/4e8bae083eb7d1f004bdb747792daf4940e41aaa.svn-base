package com.pay.tp.core.service.jh;

import java.util.*;

import com.pay.frame.common.base.exception.ServerException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.frame.common.base.enums.Status;
import com.pay.tp.core.entity.jh.JhChannelCfg;
import com.pay.tp.core.mapper.jh.JhChannelCfgMapper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class JhCHannelCfgService {
	@Autowired
	JhChannelCfgMapper jhChannelCfgMapper;
	/**
	* 新增聚合渠道配置
	* <AUTHOR>
	* @date 2022/4/21 14:51
	 * @param jhChannelCfg
	 * @return int
	*/
	@Transactional(rollbackFor = Exception.class)
	public int insert(JhChannelCfg jhChannelCfg) {
		jhChannelCfg.setCreateTime(new Date());
		jhChannelCfg.setUpdateTime(new Date());
		return jhChannelCfgMapper.insert(jhChannelCfg);
	}
	/**
	* 更新聚合渠道配置状态
	* <AUTHOR>
	* @date 2022/4/21 14:51
	 * @param jhChannelCfg
	*/
	@Transactional(rollbackFor = Exception.class)
	public void update(JhChannelCfg jhChannelCfg) {
		JhChannelCfg jhChannelCfgInfo = findById(jhChannelCfg.getId());
		jhChannelCfgInfo.setUpdateTime(new Date());
		jhChannelCfgInfo.setAppId(jhChannelCfg.getAppId());
		jhChannelCfgInfo.setChannelNo(jhChannelCfg.getChannelNo());
		jhChannelCfgInfo.setChannelType(jhChannelCfg.getChannelType());
		jhChannelCfgInfo.setRemark(jhChannelCfg.getRemark());
		jhChannelCfgInfo.setAccessToken(jhChannelCfg.getAccessToken());
		jhChannelCfgInfo.setAppSecret(jhChannelCfg.getAppSecret());
		jhChannelCfgInfo.setAuthUrl(jhChannelCfg.getAuthUrl());
		jhChannelCfgInfo.setStatus(jhChannelCfg.getStatus());
		jhChannelCfgMapper.update(jhChannelCfgInfo);
	}

	public JhChannelCfg findById(Long id){
		JhChannelCfg jhChannelCfg = jhChannelCfgMapper.findById(id);
		return Optional.ofNullable(jhChannelCfg).orElseThrow(() -> new ServerException("聚合渠道配置不存在"));
	}

	@Transactional(rollbackFor = Exception.class)
	public int updateAccessToken(String accessToken,String appId,String appSecret) {
		return jhChannelCfgMapper.updateAccessToken(accessToken, appId, appSecret);
	}
	/**
	* 分页查询聚合渠道配置
	* <AUTHOR>
	* @date 2022/4/21 14:52
	 * @param param
	 * @return com.github.pagehelper.PageInfo<com.pay.manage.core.entity.JhChannelCfg>
	*/
	public PageInfo<JhChannelCfg> findPage(Map<String, Object> param) {
		int currentPage = param.get("currentPage") == null ? 1 : Integer.parseInt(param.get("currentPage").toString());
		PageHelper.startPage(currentPage, PayConstants.PAGE_SIZE_20, true);
		List<JhChannelCfg> list = jhChannelCfgMapper.findByPageAll(param);
		PageInfo<JhChannelCfg> page = new PageInfo<JhChannelCfg>(list);
		return page;
	}

	public List<JhChannelCfg> findByChannel(String channelType) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("channelType", channelType);
		param.put("status", Status.TRUE.getCode());
		List<JhChannelCfg> list = jhChannelCfgMapper.findByPageAll(param);
		return list;
	}
	public JhChannelCfg findByAppIdAndChannelNo(String appId, String channelNo) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("appId", appId);
		param.put("channelNo", channelNo);
		List<JhChannelCfg> list = jhChannelCfgMapper.findByPageAll(param);
		if(CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}

	public JhChannelCfg selectByAppId(String appId) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("appId", appId);
		param.put("status", Status.TRUE.getCode());
		List<JhChannelCfg> list = jhChannelCfgMapper.findByPageAll(param);
		if(CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}

}
