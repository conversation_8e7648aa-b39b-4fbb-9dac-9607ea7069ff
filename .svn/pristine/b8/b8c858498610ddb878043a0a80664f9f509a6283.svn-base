package com.pay.tp.core.mapper.jh;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.entity.jh.JhChannelCfg;

public interface JhChannelCfgMapper {
    List<JhChannelCfg> findByPageAll(Map<String, Object> queryParams);

    int insert(JhChannelCfg jhChannelCfg);

    int update(JhChannelCfg jhChannelCfg);
    int updateAccessToken(@Param("accessToken") String accessToken,@Param("appId") String appId,@Param("appSecret") String appSecret);

    JhChannelCfg findById(Long id);

}