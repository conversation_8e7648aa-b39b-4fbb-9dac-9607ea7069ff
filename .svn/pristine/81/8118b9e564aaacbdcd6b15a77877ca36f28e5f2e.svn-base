package com.pay.tp.core.entity.auth;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FaceRecognitionAuth implements Serializable {

	private static final long serialVersionUID = -4691559135579059602L;
	private Long id;

	private Long optismistic;

	private Date createTime ;

	private Date updateTime ;

	private String requestNo;

	private String customerNo;

	private String name;

	private String cidNo;

	private String tradeNo;

	/** 响应码 */
	private String rspCod;

	/** 结果码 */
	private String code;

	/** 相应描述 */
	private String rspMsg;

	/** 补充说明 */
	private String remark;

	private String status;

}
