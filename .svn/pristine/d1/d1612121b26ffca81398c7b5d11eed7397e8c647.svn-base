package com.pay.tp.core.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.ToString;
import lombok.experimental.Accessors;
@ToString
@Accessors(chain=true)
public class TpPersonVerifyLog implements Serializable{
	private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
    private Long id;
    private Long optimistic;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 法人姓名
     */
    private String legalPerson;

    /**
     * 身份证号
     */
    private String identityNo;

    /**
     * 校验来源
     */
    private String verifySource;

    /**
     * 来源日志ID
     */
    private String sourceLogId;

    /**
     * 分数
     */
    private String score;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 品牌
     */
    private String brand;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOptimistic() {
        return optimistic;
    }

    public void setOptimistic(Long optimistic) {
        this.optimistic = optimistic;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo == null ? null : userNo.trim();
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson == null ? null : legalPerson.trim();
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo == null ? null : identityNo.trim();
    }

    public String getVerifySource() {
        return verifySource;
    }

    public void setVerifySource(String verifySource) {
        this.verifySource = verifySource == null ? null : verifySource.trim();
    }

    public String getSourceLogId() {
        return sourceLogId;
    }

    public void setSourceLogId(String sourceLogId) {
        this.sourceLogId = sourceLogId == null ? null : sourceLogId.trim();
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score == null ? null : score.trim();
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode == null ? null : errorCode.trim();
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg == null ? null : errorMsg.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }
}