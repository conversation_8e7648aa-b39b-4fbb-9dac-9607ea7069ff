package com.pay.tp.core.exception;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.enums.ResultsError;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Description: 控制层全局异常处理
 * 
 * @version 2021年08月01日 下午
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {


	/**
	 * 不支持的请求异常
	 *
	 * @param e
	 * @return
	 **/
	@ExceptionHandler(HttpRequestMethodNotSupportedException.class)
	public ResultsBean httpRequestMethodNotSupportedExceptionHandler(HttpRequestMethodNotSupportedException e) {
		log.error("不支持的请求方法", e);
		return ResultsBean.EXCEPTION("不支持' " + e.getMethod() + "'请求");
	}


	/**
	 * Form 表单请求
	 * JavaBean 请求参数异常
	 *
	 * @param e
	 * @description
	 **/
	@ExceptionHandler(BindException.class)
	public ResultsBean bindExceptionHandler(BindException e) {
		List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
		List<String> collect = fieldErrors.stream()
				.map(DefaultMessageSourceResolvable::getDefaultMessage)
				.collect(Collectors.toList());
		log.error("form 请求数据绑定验证异常", e);
		return ResultsBean.EXCEPTION(JsonUtils.bean2Json(collect));
	}

	/**
	 * 独立参数请求校验异常
	 *
	 * @param e
	 * @return
	 */
	@ExceptionHandler(ConstraintViolationException.class)
	public ResultsBean constraintViolationExceptionHandler(ConstraintViolationException e) {
		Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
		List<String> collect = constraintViolations.stream()
				.map(ConstraintViolation::getMessage)
				.collect(Collectors.toList());
		log.error("参数直传 请求数据绑定验证异常", e);
		String join = String.join(",", collect);
		return ResultsBean.EXCEPTION(join);
	}

	/**
	 * RequestBody为 json 的参数校验异常捕获
	 *
	 * @param e
	 * @return
	 */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	public ResultsBean methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
		List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
		List<String> collect = fieldErrors.stream()
				.map(DefaultMessageSourceResolvable::getDefaultMessage)
				.collect(Collectors.toList());
		log.error("application-json request body 请求数据绑定验证异常", e);
		String join = String.join(",", collect);
		return ResultsBean.EXCEPTION(join);
	}

	/**
	 * @param ex
	 * @return
	 * @Description 定义异常处理
	 */
	@ResponseBody
	@ExceptionHandler(value = Exception.class)
	public ResultsBean<Object> handlerException(Exception ex) {
		log.error(ex.toString(), ex);
		String message = Optional.ofNullable(ex.getMessage()).orElse(ResultsError.FACADE_REQUEST_ERROR.getMsg());
		if (ex instanceof ServerException) {
			/** 业务自定义异常 */
			ServerException serverex = (ServerException) ex;
			return ResultsBean.FAIL(message, serverex.getCode());
		} else if (ex instanceof NullPointerException) {
			return ResultsBean.FAIL(ResultsError.FACADE_REQUEST_ERROR.getMsg());
		} else {
			/** 系统异常 */
			return ResultsBean.EXCEPTION("服务处理异常");
		}
	}

}
