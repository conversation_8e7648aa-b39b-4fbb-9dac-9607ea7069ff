package com.pay.tp.core.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.tp.core.biz.impl.TencentBiz;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.enums.WillType;
import com.pay.tp.core.mapper.WillBodyRecordMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @date 2022年07月19日 10:01
 */
@Slf4j
@Service
public class WillBodyRecordService {

    @Autowired
    private WillBodyRecordMapper willBodyRecordMapper;

    @Lazy
    @Autowired
    private TencentBiz tencentBiz;


    @Transactional
    public void insert(WillBodyRecord willBodyRecord) {
        int i = willBodyRecordMapper.insert(willBodyRecord);
        if (i != 1) {
            throw new ServerException("初始化意愿核身异常!");
        }
    }

    @Transactional
    public void update(WillBodyRecord willBodyRecord) {
        int i = willBodyRecordMapper.updateByPrimaryKey(willBodyRecord);
        if (i != 1) {
            throw new ServerException("更新意愿核身异常!");
        }
    }


    public WillBodyRecord findByOrderNo(String orderNo, String brand) {
        return willBodyRecordMapper.findByOrderNo(orderNo, brand);
    }


    public List<WillBodyRecord> findValidateStatus(String status, String startTime, String willType) {
        return willBodyRecordMapper.findValidateStatus(status, startTime, willType);
    }

    /**
     * 分页查询
     *
     * @param param
     * @return com.github.pagehelper.PageInfo<com.pay.tp.core.entity.WillBodyRecord>
     * <AUTHOR>
     * @date 2022/7/20 11:14
     */
    public PageInfo<WillBodyRecord> findPage(Map<String, String> param) {
        int currentPage = param.get("currentPage") == null ? 1 : Integer.parseInt(param.get("currentPage").toString());
        PageHelper.startPage(currentPage, PayConstants.PAGE_SIZE, true);
        List<WillBodyRecord> list = willBodyRecordMapper.findByPageAll(param);
        PageInfo<WillBodyRecord> page = new PageInfo<WillBodyRecord>(list);
        return page;
    }


    public List<WillBodyRecord> findAliFlag(String startTime) {
        return willBodyRecordMapper.findAliFlag(startTime);
    }


    public List<WillBodyRecord> findByCustNo(String customerNo, String brand) {
        return willBodyRecordMapper.findByCustNo(customerNo, brand);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public ResultsBean<String> pushHistoryDbSl(String customerNo, String posSn, String amount, String brand) {
        log.info("pushHistoryDbSl param:{} {} {} {}", customerNo, posSn, amount, brand);
        List<WillBodyRecord> brList = findByCustNo(customerNo, brand);
        log.info("商户：{} db双录信息：{}", customerNo, JSON.toJSONString(brList));
        if(CollectionUtils.isEmpty(brList)){
            log.info("DB双录记录为空：{}", customerNo);
            return ResultsBean.FAIL("双录信息不存在");
        }

        brList = brList.stream().filter(x -> x.getAmount().equals(amount)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(brList)){
            log.info("未找到金额一样的双录信息：{}", customerNo);
            return ResultsBean.FAIL("双录信息不存在");
        }

        WillBodyRecord willBodyRecord = brList.get(0);
        log.info("复用双录信息：{}", JSON.toJSONString(willBodyRecord));
        JSONObject jo = JSON.parseObject(willBodyRecord.getContent1());
        jo.put("posSn", posSn);

        willBodyRecord.setContent1(JSON.toJSONString(jo));
        willBodyRecord.setOrderNo(willBodyRecord.getOrderNo() + "_REUSE");
        willBodyRecord.setCreateTime(new Date());
        willBodyRecord.setUpdateTime(new Date());
        willBodyRecord.setId(null);
        willBodyRecord.setValidateStatus("FINISH_VERIFY");
        insert(willBodyRecord);

        WillBodyRecord bodyRecord = findByOrderNo(willBodyRecord.getOrderNo(), brand);
        if(WillType.APP.name().equals(bodyRecord.getWillType())){
            tencentBiz.uploadSlToChannel(bodyRecord);
        }else{
            throw new ServerException("小程序暂不支持此功能");
        }
        return ResultsBean.SUCCESS();
    }
}
