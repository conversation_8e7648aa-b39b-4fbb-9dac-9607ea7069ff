package com.pay.tp.core.controller.auth;


import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.pay.frame.common.base.exception.ServerException;
import com.pay.tp.core.beans.auth.FaceRecognitionAuthParam;
import com.pay.tp.core.biz.FaceRecognitionAuthBiz;


/**
* <AUTHOR>
* @version 创建时间：2021年5月12日 下午2:08:09
* @ClassName
* @Description 人脸识别认证
*/
//@RestController
//@RequestMapping("/faceRecognition")
public class FaceRecognitionAuthController {
	private final Logger logger = LoggerFactory.getLogger(FaceRecognitionAuthController.class);
	
	@Autowired
	private FaceRecognitionAuthBiz faceRecognitionAuthBiz;

	/**
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/auth", method = RequestMethod.POST)
	public Map<String,Object> auth(@RequestBody FaceRecognitionAuthParam param) throws Exception {
		logger.info("method = auth, param = {}", param);
		throw new ServerException("功能缺失");
//		Map<String, Object> results = faceRecognitionAuthBiz.auth(param);
//		return results;

	}

}
