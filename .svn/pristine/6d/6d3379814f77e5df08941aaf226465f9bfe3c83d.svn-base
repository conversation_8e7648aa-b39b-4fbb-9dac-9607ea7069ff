package com.pay.tp.core.beans.auth;

import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.configuration
 * @Description: TODO
 * @date Date : 2018年12月18日 14:33
 */

/**
 * 企业四要素验证
 */
public class CompanyCheckParam  implements java.io.Serializable{
	private static final long serialVersionUID = 1L;
	/**
     * 请求号
     */
    @NotBlank
    private String requestNo;
    /**
     * 企业名称
     */
    @NotBlank
    private String entName;
    /**
     * 营业执照号码
     */
    @NotBlank
    private String regNo;
    /**
     * 法人名称
     */
    @NotBlank
    private String frName;
    /**
     * 身份证编号
     */
    @NotBlank
    private String cidNo;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getEntName() {
        return entName;
    }

    public void setEntName(String entName) {
        this.entName = entName;
    }

    public String getRegNo() {
        return regNo;
    }

    public void setRegNo(String regNo) {
        this.regNo = regNo;
    }

    public String getFrName() {
        return frName;
    }

    public void setFrName(String frName) {
        this.frName = frName;
    }

    public String getCidNo() {
        return cidNo;
    }

    public void setCidNo(String cidNo) {
        this.cidNo = cidNo;
    }

    @Override
    public String toString() {
        return "CompanyCheckParam{" +
                "requestNo='" + requestNo + '\'' +
                ", entName='" + entName + '\'' +
                ", regNo='" + regNo + '\'' +
                ", frName='" + frName + '\'' +
                ", cidNo='" + cidNo + '\'' +
                '}';
    }
}
