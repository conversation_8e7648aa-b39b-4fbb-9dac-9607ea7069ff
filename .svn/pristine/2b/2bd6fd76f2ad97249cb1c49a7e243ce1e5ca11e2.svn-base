package com.pay.tp.core.entity.msgmanage;

import java.io.Serializable;
import java.util.Date;

import com.pay.tp.core.enums.Status;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * MSG_CHANNEL_CONFIG
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MsgChannelConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 配置状态
     * ENABLE
     * DISABLE
     */
    private Status status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 通道编号
     */
    private String channelCode;

    /**
     * 配置参数名
     */
    private String cfgParam;

    /**
     * 配置参数值
     */
    private String cfgValue;
}