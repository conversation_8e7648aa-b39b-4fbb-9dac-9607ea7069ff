package com.pay.tp.core.mapper.sms;

import com.pay.tp.core.beans.sms.JiguangParam;
import com.pay.tp.core.entity.sms.SmsMovement;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.mapper
 * @Description: TODO
 * @date Date : 2018年12月24日 10:46
 */
public interface SmsMovementMapper {

    SmsMovement query(JiguangParam param);

    void insert(SmsMovement smsMovement);

    void update(SmsMovement smsMovement);
}
