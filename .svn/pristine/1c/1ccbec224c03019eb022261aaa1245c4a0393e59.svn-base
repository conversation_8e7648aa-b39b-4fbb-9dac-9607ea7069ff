package com.pay.tp.core.biz.impl;

import com.pay.frame.common.base.constants.FilePathConstants;
import com.pay.frame.common.base.enums.Status;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.RandomUtils;
import com.pay.frame.common.base.util.SftpClientUtils;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.entity.AppFileLog;
import com.pay.tp.core.service.AppFileLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> @date 2024/3/20
 * @apiNote
 */
@Slf4j
@Component
public class AppFileLogBiz {

    @Autowired
    private AppFileLogService appFileLogService;

    @Autowired
    private ALiYunBiz aliYunBiz;

    @Value("${spring.application.name}")
    private String appName;


    /**
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String downAppFileLog(Long id) {
        String filePath = "";
        try {
            AppFileLog appFileLog = appFileLogService.findById(id);
            if (appFileLog == null) {
                throw new ServerException("ID不存在");
            }

            if (!StringUtils.isEmpty(appFileLog.getFtpUrl())) {
                return appFileLog.getFtpUrl();
            }


            String name = appFileLog.getUserNo() + "_"+ RandomUtils.getFlowNo();
            filePath = aliYunBiz.downFile(appFileLog.getUrl(),
                    FilePathConstants.getLoaclTempPath(appName)+ name +".xlog");

            appFileLog.setFtpUrl(filePath);
            appFileLogService.updateFtpUrl(appFileLog);
            return filePath;
        } catch (Exception e) {
            log.error("下载APP日志文件异常：{},{},{}", id, filePath, e);
        }

        return "";
    }


    /**
     * 删除文件
     * @param startTime
     */
    public void delFile(String startTime) {
        List<AppFileLog> logList = appFileLogService.findBeforeTime(startTime);
        log.info("查询待删除的文件数量：{}", logList.size());
        for (AppFileLog fileLog : logList) {
            try {
                if (StringUtils.isEmpty(fileLog.getFtpUrl())) {
                    aliYunBiz.deleteFile(fileLog.getUrl());
                }else {
                    SftpClientUtils.delFile(fileLog.getFtpUrl());
                }

                fileLog.setStatus(Status.DELETE.getCode());
                appFileLogService.updateStatus(fileLog);

            }catch (Exception e) {
                log.error("删除文件失败：{},{}", e, fileLog);
            }
        }
        log.info("删除文件end：{}", logList.size());
    }


}
