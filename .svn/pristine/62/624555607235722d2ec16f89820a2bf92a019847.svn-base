<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.msgmanage.MsgTemplateLogMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.msgmanage.MsgTemplateLog">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="OPERATOR_NAME" jdbcType="VARCHAR" property="operatorName"/>
        <result column="BEFORE" jdbcType="VARCHAR" property="before"/>
        <result column="AFTER" jdbcType="VARCHAR" property="after"/>
        <result column="TEMPLATE_ID" jdbcType="DECIMAL" property="templateId"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, CREATE_TIME, OPERATOR_NAME, "BEFORE", "AFTER", TEMPLATE_ID
  </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_TEMPLATE_LOG
        where ID = #{id,jdbcType=DECIMAL}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.msgmanage.MsgTemplateLog">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_MSG_TEMPLATE_LOG_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.MSG_TEMPLATE_LOG (ID, CREATE_TIME, OPERATOR_NAME, "BEFORE",
        "AFTER", TEMPLATE_ID)
        values (#{id,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{operatorName,jdbcType=VARCHAR},
        #{before,jdbcType=VARCHAR},
        #{after,jdbcType=VARCHAR}, #{templateId,jdbcType=DECIMAL})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.msgmanage.MsgTemplateLog">
    update UBADMA.MSG_TEMPLATE_LOG
    set CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      OPERATOR_NAME = #{operatorName,jdbcType=VARCHAR},
      "BEFORE" = #{before,jdbcType=VARCHAR},
      "AFTER" = #{after,jdbcType=VARCHAR},
      TEMPLATE_ID = #{templateId,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>