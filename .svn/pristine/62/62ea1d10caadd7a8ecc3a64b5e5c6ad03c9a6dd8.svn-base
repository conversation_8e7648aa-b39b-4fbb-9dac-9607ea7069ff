package com.pay.tp.core.mapper.position;

import com.pay.tp.core.beans.position.CoordinateParam;
import com.pay.tp.core.entity.position.Coordinate;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.mapper
 * @Description: TODO
 * @date Date : 2018年12月24日 14:43
 */
public interface CoordinateMapper {

    Coordinate query(CoordinateParam param);

    void insert(Coordinate coordinate);

    void update(Coordinate coordinate);
    
}
