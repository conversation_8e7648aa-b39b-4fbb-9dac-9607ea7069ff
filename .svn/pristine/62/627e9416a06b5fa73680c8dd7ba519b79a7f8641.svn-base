package com.pay.tp.wx;

import com.baidu.aip.util.Base64Util;
import com.pay.frame.common.base.util.Base64Utils;
import com.pay.frame.common.base.util.FileUtil;
import com.pay.tp.BaseTest;
import com.pay.tp.core.controller.baidu.BaiDuController;
import com.pay.tp.core.controller.xcx.XcxCustController;
import com.pay.tp.core.remote.wx.WeChatClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;
import sun.misc.BASE64Encoder;

import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;


public class XcxControllerTest extends BaseTest {

    @Autowired
    private XcxCustController xcxCustController;

//    @Autowired
//    private SmsMsgWxCheckHealthTask smsMsgWxCheckHealthTask;
    @Autowired
    private WeChatClient weChatClient;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private BaiDuController baiDuController;

    
    @Test
    public void weChatAuth() throws Exception {
        String fileName = "D:\\我的\\照片\\身份证号.jpg";
//        File f = new FileBody(fileName);

//    	xcxCustController.code2session("051oVV0w3MrAGY2xlp0w3gq22f3oVV0c");
//    	xcxCustController.code2session("021oyYkl2x6Pl94iMfll2tFvHp4oyYkT");
//    	xcxCustController.getuserphonenumber("a87b1fadcc7372ec05e5d2c57a7e702eefd39c76fe7dd3dac6cd1e9a5b0e9949");
    }



    // 身份证识别
//    String url = SmallWxUtil.uploadCard("photo");
//    String postForm = MyHttpUtils.postForm(url, file);
//    JSONObject object = JSONObject.parseObject(postForm);
//    String errcode = object.getString("errcode");
//    if (!"0".equals(errcode)) {
//        throw new AppletsException("身份识别失败，请上传有效证件");
//    }


}
