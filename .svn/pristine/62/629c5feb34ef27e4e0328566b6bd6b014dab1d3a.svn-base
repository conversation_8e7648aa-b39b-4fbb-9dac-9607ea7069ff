package com.pay.tp.core.remote.position;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;

import javax.annotation.PostConstruct;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR> zhang<PERSON>an
 * @Package com.pay.position.core.client
 * @Description: TODO
 * @date Date : 2018年12月24日 16:05
 */
//@Component
public class AliyunClient {

    private final Logger logger = LoggerFactory.getLogger(AliyunClient.class);

    @Value("${custom.aliyun.ip.url}")
    private String url;
    @Value("${custom.aliyun.ip.app.code}")
    private String appCode;
    @Value("${custom.aliyun.connection.timeout:1000}")
    private int connectionTimeout = 1000;
    @Value("${custom.aliyun.socket.timeout:1000}")
    private int socketTimeout = 1000;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    private Properties properties;

    @PostConstruct
    public void init() throws IOException {
        properties = PropertiesLoaderUtils.loadAllProperties("shengfen.properties");
    }

    public String getShengfenCode(String shengfen){

        if(StringUtils.isEmpty(shengfen)){
            return "";
        }

        shengfen = shengfen.replace("市", "").replace("省", "")
                .replace("自治区", "").replace("维吾尔", "")
                .replace("壮族", "").replace("回族", "").replace("族", "");


        String code =  properties.getProperty(shengfen);
        if(!StringUtils.isEmpty(code)) {
            return code;
        }else{
            Iterator<String> it = properties.stringPropertyNames().iterator();
            for(;((Iterator) it).hasNext();){
                String key = it.next();
                if(shengfen.contains(key)){
                    return properties.getProperty(key);
                }
            }
        }
        return "";
    }


    
    public Map<String, Object> request(String requestNo, String ip) throws Exception {
        logger.info("method = request, requestNo = {}, ip = {}", requestNo, ip);

        CloseableHttpClient client = null;
        try{
            client = HttpClientBuilder.create().build();
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", "APPCODE " + appCode);
            Map<String, String> querys = new HashMap<String, String>();
            querys.put("ip", ip);

            RequestConfig.Builder builder = RequestConfig.custom()
                    .setConnectTimeout(connectionTimeout).setSocketTimeout(socketTimeout);

            if(!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)){
                builder.setProxy(new HttpHost(proxyHost, proxyPort)).build();
            }

            HttpGet get = new HttpGet(url + "?ip=" + URLEncoder.encode(ip, "utf-8"));
            get.setConfig(builder.build());
            get.addHeader("Authorization", "APPCODE " + appCode);

            HttpResponse response = client.execute(get);

            int statusCode = response.getStatusLine().getStatusCode();

            if(HttpStatus.SC_OK == statusCode){
                String content = EntityUtils.toString(response.getEntity());
                logger.info("method = request, requestNo = {}, content = {}", requestNo, content);
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String,Object> map = objectMapper.readValue(content, Map.class);
                if(0 != Integer.parseInt(map.remove("error_code")+"")){
                    throw new RuntimeException(map.remove("reason")+"");
                }

                return (Map<String, Object>) map.remove("result");

            }else{
                logger.error("method = request, requestNo = {}, statusCode = {}", requestNo, statusCode);
            }
        }finally {
            if(client != null){
                client.close();
            }
        }

        return new HashMap<>();
    }
    
    
}
