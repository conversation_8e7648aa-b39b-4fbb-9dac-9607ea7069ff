package com.pay.tp.core.controller.msgmanage;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.tp.core.beans.sms.SmsMsgRsp;
import com.pay.tp.core.beans.sms.SmsMsgTemplateDto;
import com.pay.tp.core.entity.msgmanage.MsgTemplate;
import com.pay.tp.core.service.msgmanage.MsgTemplateService;

import lombok.extern.slf4j.Slf4j;


/**
 * 消息通讯，信息模板管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/msg/manage/template")
public class MsgManageTemplateControl {

    @Autowired
    private MsgTemplateService msgTemplateService;


    /**
     * 修改消息模板
     */
    @PostMapping("/updateTemplate")
    public SmsMsgRsp updateTemplate(@Validated @RequestBody SmsMsgTemplateDto templateDto) {
        log.info("update template {}", templateDto);
        MsgTemplate template = templateDto.convertToSmsMsgTemplate(templateDto);
        msgTemplateService.updateByPrimaryKey(template);
        return new SmsMsgRsp().setCode("00").setMsg("变更成功");
    }


    /**
     * 新增消息模板
     */
    @PostMapping("/addTemplate")
    public SmsMsgRsp addTemplate(@Validated @RequestBody SmsMsgTemplateDto templateDto) {
        log.info("add template {}", templateDto);
        MsgTemplate template = templateDto.convertToSmsMsgTemplate(templateDto);
        msgTemplateService.insert(template);
        return new SmsMsgRsp().setCode("00").setMsg("新增消息模板成功");
    }


    /**
     * 分页查询列表
     */
    @PostMapping("/pageTemplate")
    public PageInfo<SmsMsgTemplateDto> pageTemplate(@RequestParam Map<String, Object> params) {
        log.info("page template {}", params);
        Integer pageNum = Integer.valueOf(params.getOrDefault("currentPage", 1).toString());
        PageHelper.startPage(pageNum, 10);
        List<MsgTemplate> users = msgTemplateService.findByParams(params);
        PageInfo page = new PageInfo(users);
        List<SmsMsgTemplateDto> dtoList = users.stream().map(e -> new SmsMsgTemplateDto().convertFor(e))
                .collect(Collectors.toList());
        page.setList(dtoList);
        return page;
    }

    /**
     * 单笔查询
     */
    @GetMapping("/findTemplate")
    public SmsMsgTemplateDto findTemplate(@RequestParam("id") Long id) {
        log.info("find template {}", id);
        MsgTemplate template = msgTemplateService.findById(id);
        SmsMsgTemplateDto dto = new SmsMsgTemplateDto().convertFor(template);
        return dto;
    }
}
