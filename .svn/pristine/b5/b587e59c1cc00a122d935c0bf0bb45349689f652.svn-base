package com.pay.tp.core.service.auth;

import com.pay.tp.core.beans.auth.CompanyCheckParam;
import com.pay.tp.core.entity.auth.CompanyCheck;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.service
 * @Description: TODO
 * @date Date : 2018年12月22日 17:13
 */
public interface CompanyCheckService {

    CompanyCheck query(CompanyCheckParam param);

    void update(CompanyCheck companyCheck);
}
