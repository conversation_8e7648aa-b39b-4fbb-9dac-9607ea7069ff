package com.pay.tp.core.service.msgmanage;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.entity.msgmanage.MsgTemplate;
import com.pay.tp.core.entity.msgmanage.MsgTemplateLog;
import com.pay.tp.core.mapper.msgmanage.MsgTemplateLogMapper;
import com.pay.tp.core.utils.JsonUtil;

/**
 * 消息模板操作日志服务层
 *
 * <AUTHOR>
 */
@Service
public class MsgTemplateLogService {

    @Autowired
    private MsgTemplateLogMapper msgTemplateLogMapper;

    public int insert(MsgTemplateLog templateLog) {
        templateLog.setCreateTime(new Date());
        return msgTemplateLogMapper.insert(templateLog);
    }

    public int save(MsgTemplate before, MsgTemplate after) {
        MsgTemplateLog templateLog = new MsgTemplateLog();
        templateLog.setCreateTime(new Date())
                .setBefore(JsonUtil.toJson(before))
                .setAfter(JsonUtil.toJson(after))
                .setTemplateId(after.getId());
        return msgTemplateLogMapper.insert(templateLog);
    }
}