<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.WillBodyRecordMapper">
  <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.WillBodyRecord">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic" />
    <result column="OWNER_NO" jdbcType="VARCHAR" property="ownerNo" />
    <result column="AMOUNT" jdbcType="VARCHAR" property="amount" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="WILL_TYPE" jdbcType="VARCHAR" property="willType" />
    <result column="LIVE_RATE" jdbcType="VARCHAR" property="liveRate" />
    <result column="SIMILARITY" jdbcType="VARCHAR" property="similarity" />
    <result column="VALIDATE_STATUS" jdbcType="VARCHAR" property="validateStatus" />
    <result column="FACE_CODE" jdbcType="VARCHAR" property="faceCode" />
    <result column="FACE_MSG" jdbcType="VARCHAR" property="faceMsg" />
    <result column="WILL_CODE" jdbcType="VARCHAR" property="willCode" />
    <result column="WILL_MSG" jdbcType="VARCHAR" property="willMsg" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
    <result column="OPERATOR" jdbcType="VARCHAR" property="operator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="ALI_FLAG" jdbcType="VARCHAR" property="aliFlag" />
    <result column="ALI_PATH" jdbcType="VARCHAR" property="aliPath" />
    <result column="CONTENT1" jdbcType="VARCHAR" property="content1" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, OPTIMISTIC, OWNER_NO, AMOUNT, ORDER_NO, WILL_TYPE, LIVE_RATE, SIMILARITY, VALIDATE_STATUS,
    FACE_CODE, FACE_MSG, WILL_CODE, WILL_MSG, URL, CONTENT, "SOURCE", "OPERATOR", CREATE_TIME, UPDATE_TIME,
    BRAND, ALI_FLAG, ALI_PATH, CONTENT1
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UBADMA.WILL_BODY_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="findByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WILL_BODY_RECORD
    where ORDER_NO = #{orderNo} AND BRAND = #{brand}
  </select>

  <select id="findValidateStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WILL_BODY_RECORD
    where VALIDATE_STATUS = #{status} AND WILL_TYPE = #{willType}
    AND CREATE_TIME  <![CDATA[ >= ]]> to_date(#{startTime} || ' 00:00:00', 'yyyy-MM-dd hh24:mi:ss')
    <if test="willType != null and willType == 'EID' ">
      AND URL IS NULL and FACE_CODE = '0' and WILL_CODE = '0'
    </if>
  </select>

  <select id="findAliFlag" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WILL_BODY_RECORD
    where VALIDATE_STATUS = 'FINISH_VERIFY' AND ALI_FLAG = 'N'
    AND ALI_PATH IS NOT NULL
    AND CREATE_TIME  <![CDATA[ >= ]]> to_date(#{startTime} || ' 00:00:00', 'yyyy-MM-dd hh24:mi:ss')
  </select>

  <select id="findByCustNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.WILL_BODY_RECORD
    where ALI_PATH IS NOT NULL
    AND OWNER_NO = #{customerNo} AND BRAND = #{brand} and FACE_CODE = '0' and WILL_CODE = '0'
  </select>

    <select id="findByPageAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
      select <include refid="Base_Column_List"></include>
      from UBADMA.WILL_BODY_RECORD where 1=1
      <if test="param.ownerNo != null and param.ownerNo != ''">
        and owner_no = #{param.ownerNo,jdbcType=VARCHAR}
      </if>
      <if test="param.amount != null and param.amount != ''">
        and amount = #{param.amount,jdbcType=VARCHAR}
      </if>
      <if test="param.status != null and param.status != ''">
        <choose>
          <when test="param.status == 'Y'.toString() ">
            and FACE_CODE = '0' and WILL_CODE = '0'
          </when>
          <when test="param.status == 'N'.toString() ">
            and (FACE_CODE != '0' or WILL_CODE != '0')
          </when>
        </choose>
      </if>
      <if test="param.brand != null and param.brand != ''">
        and BRAND = #{param.brand,jdbcType=VARCHAR}
      </if>
      <if test="param.willType != null and param.willType != ''">
        and WILL_TYPE = #{param.willType,jdbcType=VARCHAR}
      </if>
      <if test="param.faceCode != null and param.faceCode != ''">
        and FACE_CODE = #{param.faceCode,jdbcType=VARCHAR}
      </if>
      <if test="param.willCode != null and param.willCode != ''">
        and WILL_CODE = #{param.willCode,jdbcType=VARCHAR}
      </if>
      <if test="param.createTimeStart != null and param.createTimeStart != '' " >
        <![CDATA[
			  and CREATE_TIME >= to_date(#{param.createTimeStart,jdbcType=TIMESTAMP} || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
			]]>
      </if>
      <if test="param.createTimeEnd != null and param.createTimeEnd != '' " >
        <![CDATA[
			  and CREATE_TIME <= to_date(#{param.createTimeEnd,jdbcType=TIMESTAMP} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
			]]>
      </if>
        order by id desc
    </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from UBADMA.WILL_BODY_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>

  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.pay.tp.core.entity.WillBodyRecord" useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
      SELECT UBADMA.SEQ_WILL_BODY_RECORD_ID.nextval FROM dual
    </selectKey>
    insert into UBADMA.WILL_BODY_RECORD (ID, OPTIMISTIC, OWNER_NO, AMOUNT, ORDER_NO,
      WILL_TYPE, LIVE_RATE, SIMILARITY,
      VALIDATE_STATUS, FACE_CODE, FACE_MSG,
      WILL_CODE, WILL_MSG, URL,
      CONTENT, "SOURCE", "OPERATOR",
      CREATE_TIME, UPDATE_TIME, BRAND,
      ALI_FLAG, ALI_PATH, CONTENT1
      )
    values (#{id,jdbcType=DECIMAL}, 0, #{ownerNo,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR},
      #{willType,jdbcType=VARCHAR}, #{liveRate,jdbcType=VARCHAR}, #{similarity,jdbcType=VARCHAR},
      #{validateStatus,jdbcType=VARCHAR}, #{faceCode,jdbcType=VARCHAR}, #{faceMsg,jdbcType=VARCHAR},
      #{willCode,jdbcType=VARCHAR}, #{willMsg,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
      #{content,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
      SYSDATE, #{updateTime,jdbcType=TIMESTAMP}, #{brand,jdbcType=VARCHAR},
      #{aliFlag,jdbcType=VARCHAR}, #{aliPath,jdbcType=VARCHAR}, #{content1,jdbcType=VARCHAR}
      )
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.WillBodyRecord">
    update UBADMA.WILL_BODY_RECORD
    set OPTIMISTIC = OPTIMISTIC + 1,
      UPDATE_TIME = SYSDATE,
      OWNER_NO = #{ownerNo,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=VARCHAR},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      WILL_TYPE = #{willType,jdbcType=VARCHAR},
      LIVE_RATE = #{liveRate,jdbcType=VARCHAR},
      SIMILARITY = #{similarity,jdbcType=VARCHAR},
      VALIDATE_STATUS = #{validateStatus,jdbcType=VARCHAR},
      FACE_CODE = #{faceCode,jdbcType=VARCHAR},
      FACE_MSG = #{faceMsg,jdbcType=VARCHAR},
      WILL_CODE = #{willCode,jdbcType=VARCHAR},
      WILL_MSG = #{willMsg,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      "SOURCE" = #{source,jdbcType=VARCHAR},
      "OPERATOR" = #{operator,jdbcType=VARCHAR},
      ALI_FLAG = #{aliFlag,jdbcType=VARCHAR},
      ALI_PATH = #{aliPath,jdbcType=VARCHAR},
      CONTENT1 = #{content1,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>


</mapper>