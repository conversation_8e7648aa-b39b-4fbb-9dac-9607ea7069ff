package com.pay.tp.core.mapper.position;

import java.util.List;
import java.util.Map;

import com.pay.tp.core.beans.position.CellParam;
import com.pay.tp.core.entity.position.Cell;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.mapper
 * @Description: TODO
 * @date Date : 2018年12月24日 14:43
 */
public interface CellMapper {

    Cell query(CellParam param);

    List<Cell> queryByLatLng(Cell param);

    void insert(Cell cell);

    void update(Cell cell);

    List<Cell> queryByParam(CellParam param);

    List<Cell> queryLdysByParam(CellParam param);

    void updateByCell(Cell cell);

    List<Cell> queryLdysAllByParam(Cell cell);

    void insertCell(Cell cell);

    List<Cell> findByCondition(@Param("params") Map<String, Object> paramsMap);

    Cell findById(Long id);
}
