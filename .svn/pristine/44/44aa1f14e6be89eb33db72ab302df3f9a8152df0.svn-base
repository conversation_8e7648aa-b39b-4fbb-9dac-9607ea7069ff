<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.msgmanage.MsgTemplateMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.msgmanage.MsgTemplate">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="MSG_TEMPLATE" jdbcType="VARCHAR" property="msgTemplate"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="TEMPLATE_NAME" jdbcType="VARCHAR" property="templateName"/>
        <result column="TEMPLATE_CODE" jdbcType="VARCHAR" property="templateCode"/>
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode"/>
        <result column="USER_GROUP_NO" jdbcType="VARCHAR" property="userGroupNo"/>
        <result column="DESC" jdbcType="VARCHAR" property="desc"/>
        <result column="EXT_INFO" jdbcType="VARCHAR" property="extInfo"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, MSG_TEMPLATE, "STATUS", TEMPLATE_NAME, TEMPLATE_CODE, CHANNEL_CODE,
    USER_GROUP_NO, "DESC", EXT_INFO
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_TEMPLATE
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <select id="findByTemplateCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_TEMPLATE
        where TEMPLATE_CODE = #{templateCode,jdbcType=DECIMAL}
    </select>
    <select id="findByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_TEMPLATE
        <where>
            1=1
            <if test="params.id != null">
                and ID=#{params.id}
            </if>
            <if test="params.templateName != null and params.templateName != ''">
                and TEMPLATE_NAME=#{params.templateName}
            </if>
            <if test="params.templateCode != null and params.templateCode != ''">
                and TEMPLATE_CODE=#{params.templateCode}
            </if>
            <if test="params.channelCode != null and params.channelCode != ''">
                and CHANNEL_CODE=#{params.channelCode}
            </if>
            <if test="params.userGroupNo != null and params.userGroupNo != ''">
                and USER_GROUP_NO=#{params.userGroupNo}
            </if>
            <if test="params.status != null and params.status != ''">
                and "STATUS"=#{params.status}
            </if>
            <if test="params.dateStart !=null and params.dateStart !=''">
                and CREATE_TIME &gt;= to_date(#{params.dateStart},'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="params.dateEnd !=null and params.dateEnd !=''">
                and CREATE_TIME &lt;= to_date(#{params.dateEnd},'yyyy-MM-dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY TEMPLATE_CODE
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.msgmanage.MsgTemplate">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_MSG_TEMPLATE_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.MSG_TEMPLATE (ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME,
        MSG_TEMPLATE, "STATUS", TEMPLATE_NAME, TEMPLATE_CODE, CHANNEL_CODE, USER_GROUP_NO,
        "DESC", EXT_INFO)
        values (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{msgTemplate,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR},
        #{templateCode,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR}, #{userGroupNo,jdbcType=VARCHAR},
        #{desc,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.msgmanage.MsgTemplate">
    update UBADMA.MSG_TEMPLATE
    set OPTIMISTIC = OPTIMISTIC + 1,
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      MSG_TEMPLATE = #{msgTemplate,jdbcType=VARCHAR},
      "STATUS" = #{status,jdbcType=VARCHAR},
      TEMPLATE_NAME = #{templateName,jdbcType=VARCHAR},
      TEMPLATE_CODE = #{templateCode,jdbcType=VARCHAR},
      CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      USER_GROUP_NO = #{userGroupNo,jdbcType=VARCHAR},
      "DESC" = #{desc,jdbcType=VARCHAR},
      EXT_INFO = #{extInfo,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
    and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
  </update>
</mapper>