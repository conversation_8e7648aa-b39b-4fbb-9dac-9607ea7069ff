package com.pay.tp.core.utils;

import com.pay.frame.common.base.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * 检查工具类
 */
@Slf4j
public class CheckUtil {

    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 判断是否为null
     *
     * @param values
     * @param <T>
     * @return
     */
    public static <T> boolean isNull(T... values) {
        return checkIsNull(values);
    }

    /**
     * 判断是否为null 为null则抛出异常
     *
     * @param message
     * @param values
     * @param <T>
     */
    public static <T> void isNullException(String message, T... values) {
        if (checkIsNull(values)) {
            throw new ServerException(message);
        }
    }

    /**
     * 判断list map string是否为空或null
     *
     * @param values
     * @param <T>
     * @return
     */
    public static <T> boolean isEmpty(T... values) {
        return checkIsEmpty(values);
    }

    /**
     * 判断list map string是否为空或null 为空或null则抛出异常
     *
     * @param values
     * @param <T>
     * @return
     */
    public static <T> void isEmptyException(String message, T... values) {
        if (checkIsEmpty(values)) {
            throw new ServerException(message);
        }
    }

    /**
     * 不等于true 抛出异常
     *
     * @param message
     * @param obj
     */
    public static void notTrueException(String message, boolean obj) {
        if (!obj) {
            throw new ServerException(message);
        }
    }

    /**
     * 校验@Validator注解
     *
     * @return
     */
    public static <T> void validator(T object) {
        String msg = doValidator(object);
        if (StringUtils.isNotBlank(msg)) {
            throw new ServerException(msg);
        }
    }

    private static <T> boolean checkIsNull(T... values) {
        if (values == null || values.length == 0) {
            return true;
        }
        for (T va : values) {
            if (va == null) {
                return true;
            }
        }
        return false;
    }

    private static <T> boolean checkIsEmpty(T... values) {
        if (values == null || values.length == 0) {
            return true;
        }
        for (T va : values) {
            if (va instanceof String) {
                if (StringUtils.isBlank((String) va)) {
                    return true;
                }
            } else if (va instanceof Collection) {
                if (CollectionUtils.isEmpty((Collection) va)) {
                    return true;
                }
            } else if (va instanceof Map) {
                if (MapUtils.isEmpty((Map) va)) {
                    return true;
                }
            } else if (va == null) {
                return true;
            }
        }
        return false;
    }

    private static <T> String doValidator(T object) {
        if (object == null) {
            throw new ServerException("参数对象不能为空");
        }

        Set<ConstraintViolation<T>> validate = validator.validate(object);
        if (CollectionUtils.isNotEmpty(validate)) {
            StringBuilder message = new StringBuilder();
            for (ConstraintViolation<T> violation : validate) {
                message.append(",").append(violation.getMessage());
            }
            message = message.replace(0, 1, "");
            return message.toString();
        }
        return null;
    }

//    public static void main(String[] args) {
//        AppWillBodyReq req = new AppWillBodyReq();
//        req.setVariableValue("99");
//        req.setOrderNo("");
//        CheckUtil.validator(req);
//
//        CheckUtil.isNullException("参数必填", null);
//        System.out.println(CheckUtil.isEmpty(Lists.newArrayList()));
//        System.out.println(CheckUtil.isEmpty(Maps.newHashMap()));
//    }
}
