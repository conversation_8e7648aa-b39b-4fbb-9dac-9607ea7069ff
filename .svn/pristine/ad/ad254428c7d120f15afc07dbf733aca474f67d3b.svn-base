package com.pay.tp.core.beans.sms;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.util.Map;
import java.util.Set;

/**极光推送参数
 * <AUTHOR> z<PERSON><PERSON>an
 * @Package com.pay.sms.core.beans
 * @Description: TODO
 * @date Date : 2018年12月24日 10:47
 */
public class JiguangParam {
    /**
     * app名称
     */
    @NotBlank
    private String appName;
    /**
     * 请求号
     */
    @NotBlank
    private String requestNo;

    /**
     * 消息内容
     */
    private String content;
    /**
     * 通知内容
     */
    private String alert;
    /**
     * 附加信息
     */
    @Size(max = 20)
    private Map<String, String> extras;
    /**
     *标签交集列表 
     */
    @Size(max = 10)
    private Set<String> tagsAnd;
    /**
     * 标签并集列表
     */
    @Size(max = 10)
    private Set<String> tags;
    /**
     * 别名列表
     */
    @Size(max = 10)
    private Set<String> deviceNumbers;
    /**
     * 设备号列表
     */
    @Size(max = 10)
    private Set<String> aliases;
    /**
     * 平台列表
     */
    private Set<String> platforms;

    /**
     * 通知扩展
     */
    private Boolean mutableContent;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAlert() {
        return alert;
    }

    public void setAlert(String alert) {
        this.alert = alert;
    }

    public Map<String, String> getExtras() {
        return extras;
    }

    public void setExtras(Map<String, String> extras) {
        this.extras = extras;
    }

    public Set<String> getTagsAnd() {
        return tagsAnd;
    }

    public void setTagsAnd(Set<String> tagsAnd) {
        this.tagsAnd = tagsAnd;
    }

    public Set<String> getTags() {
        return tags;
    }

    public void setTags(Set<String> tags) {
        this.tags = tags;
    }

    public Set<String> getAliases() {
        return aliases;
    }

    public void setAliases(Set<String> aliases) {
        this.aliases = aliases;
    }

    public Set<String> getPlatforms() {
        return platforms;
    }

    public void setPlatforms(Set<String> platforms) {
        this.platforms = platforms;
    }

    public Set<String> getDeviceNumbers() {
        return deviceNumbers;
    }

    public void setDeviceNumbers(Set<String> deviceNumbers) {
        this.deviceNumbers = deviceNumbers;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Boolean getMutableContent(){
        return mutableContent;
    }

    public void setMutableContent(Boolean mutableContent){
        this.mutableContent=mutableContent;
    }

    @Override
    public String toString() {
        return "JiguangParam{" +
                "requestNo='" + requestNo + '\'' +
                ", content='" + content + '\'' +
                ", alert='" + alert + '\'' +
                ", extras=" + extras +
                ", tagsAnd=" + tagsAnd +
                ", tags=" + tags +
                ", aliases=" + aliases +
                ", platforms=" + platforms +
                ", appName=" + appName +
                ", deviceNumbers=" + deviceNumbers +
                ", mutableContent=" + mutableContent +
                '}';
    }
}
