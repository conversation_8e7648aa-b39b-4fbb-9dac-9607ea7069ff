package com.pay.tp.core.biz.impl;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.pay.tp.core.beans.auth.FaceRecognitionAuthParam;
import com.pay.tp.core.biz.FaceRecognitionAuthBiz;
import com.pay.tp.core.entity.auth.FaceRecognitionAuth;
import com.pay.tp.core.mapper.auth.FaceRecognitionAuthMapper;
import com.pay.tp.core.beans.auth.FaceRecognitionRes;
import com.pay.tp.core.service.auth.FaceRecognitionAuthService;

@Component
public class FaceRecognitionAuthBizImpl implements FaceRecognitionAuthBiz {
	private final Logger logger = LoggerFactory.getLogger(FaceRecognitionAuthBizImpl.class);


	@Autowired
	private FaceRecognitionAuthService faceRecognitionAuthService;

	@Autowired
	private FaceRecognitionAuthMapper faceRecognitionAuthMapper;
	
	/**
	 * @Description  人脸识别认证
	 * @param param
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
	public Map<String, Object> auth(FaceRecognitionAuthParam param) throws Exception {

		Map<String, Object> map = null;
		//要求幂等
		FaceRecognitionAuth auth = faceRecognitionAuthService.query(param);
		if (auth != null && !StringUtils.isEmpty(auth.getRspCod())) {
			// 请求参数存在请求结果，则直接返回解析
			return parse(auth);
		} else {
			// 初始化鉴权结果
			auth = create(param);

			try {
				// 请求鉴权
				FaceRecognitionRes faceRecognitionRes = new FaceRecognitionRes();
//				.request(auth.getRequestNo(),
//						auth.getName().trim(), auth.getCidNo().trim(),param.getIdPhoto().trim(),param.getDataPhoto().trim());

				// 更新auth结果
				auth.setCode(faceRecognitionRes.getCode());
				auth.setRspCod(faceRecognitionRes.getRspCod());
				auth.setRspMsg(faceRecognitionRes.getRspMsg());
				auth.setRemark(faceRecognitionRes.getRemark());
				auth.setTradeNo(faceRecognitionRes.getTradeNo());
				if (faceRecognitionRes.getCode().equals("01")){
					auth.setStatus("INIT");
				}

			    faceRecognitionAuthService.update(auth);
				map = parse(auth);

			}catch(Exception e){
				logger.info("人脸识别认证异常 param：{}，err：{},{}", param, e.getMessage(), e);
			}
				return map;
		}
	}


	/** 初始化人脸识别记录 */
	private FaceRecognitionAuth create(FaceRecognitionAuthParam param) {
		FaceRecognitionAuth auth = new FaceRecognitionAuth();
		auth.setCidNo(param.getCidNo());
		auth.setCustomerNo(param.getCustomerNo());
		auth.setName(param.getName());
		auth.setRequestNo(param.getRequestNo());
		faceRecognitionAuthMapper.insert(auth);
		return auth;
	}
	
	
	/**
	 * @Description 认证解析
	 * @param auth
	 * @return
	 * @see 01-认证一致 （收费） 
	 * 		02-认证不一致（收费） 
	 * 		03-认证不确定（不收费） 
	 * 		04-认证失败 （不收费） 
	 * 		05-认证受限（收费）
	 */
	private Map<String, Object> parse(FaceRecognitionAuth auth) {

        Map<String, Object> map = new HashMap<>();

        if ("0".equals(auth.getRspCod())) {
            String code = auth.getCode();
            if ("01".equals(code) ) {
                map.put("result", code);
                String remark = auth.getRemark();
                if (remark != null) {
                    remark = remark.replace("认证成功,", "");
                }
                map.put("msg", remark);
				map.put("tradeNo", auth.getTradeNo());
            } else {
                map.put("result", "02");
                map.put("msg", auth.getRemark());
				map.put("tradeNo", auth.getTradeNo());
            }
        }else{
            map.put("result", "02");
            map.put("msg", auth.getRemark());
        }

        logger.info("metho = parse： auth = {}, map = {}", auth, map);

        return map;
    }
	

}
