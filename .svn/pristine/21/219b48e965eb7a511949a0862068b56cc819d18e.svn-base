package com.pay.tp.core.mapper.auth;

import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.entity.auth.BankcardAuth;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @version 创建时间：2018年12月19日 下午11:22:29
* @ClassName 
* @Description 
*/
public interface BankCardAuthMapper {

	List<BankcardAuth> queryEncrypt(@Param("cardNo") String cardNo, @Param("name") String name, @Param("cidNo") String cidNo, @Param("mobile") String mobile);
	List<BankcardAuth> query(BankCardAuthParam param);

	void insert(BankcardAuth auth);
	void insertFromSdData(BankcardAuth auth);

	void update(BankcardAuth auth);
	/**
	 * 根据查询条件查询结果
	 * @param params 查询条件
	 * @return 查询结果
	 */
	public List<BankcardAuth> findByCondition(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize, @Param("params") Map<String, String> params);

	/**
	 * 根据id查询实体
	 * @param id 实体ID
	 * @return 实体
	 */
	public BankcardAuth findById(@Param("id") Long id);
	
}
