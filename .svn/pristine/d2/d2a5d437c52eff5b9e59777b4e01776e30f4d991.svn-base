<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="60 seconds" debug="false" >

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <property name="LOG_HOME" value="@tomcat.base@/@project.artifactId@"/>
    <property name="APP_NAME" value="@project.artifactId@"/>
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{MMdd HHmmss SSS} [%-4t] %-5p %c: [%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}] %m%n</pattern>
        </encoder>
    </appender>
    <!-- 详细日志 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${APP_NAME}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${APP_NAME}.%d{yyyy-MM-dd-HH}.log</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>180</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.pay.frame.common.base.plugins.LogbackSensitiveEncodeLayout">
                <sensitiveKey>
                    phoneNo;telephone;linkman,1,0,*;
                    identityNo;linkPhone;legalPerson,1,0,*;address,1,0,*;
                    identityNoOld;linkPhoneOld;legalPersonOld,1,0,*;addressOld,1,0,*;
                    username;bankAccountName,1,0,*;bankAccountNo;reservePhone;
                    bankAccountNameOld,1,0,*;bankAccountNoOld;reservePhoneOld;
                    prePhone;operator,1,0,*;pan;
                    receiveAddress;receiveName;receiveTelephone;
                    cardNo;accountName,1,0,*;cidNo;mobile;
                    "phoneNo";"telephone";"linkman",2,0,*;
                    "identityNo";"linkPhone";"legalPerson",2,0,*;"address",2,0,*;
                    "identityNoOld";"linkPhoneOld";"legalPersonOld",2,0,*;"addressOld",2,0,*;
                    "username";"bankAccountName",2,0,*;"bankAccountNo";"reservePhone";
                    "bankAccountNameOld",2,0,*;"bankAccountNoOld";"reservePhoneOld";
                    "prePhone";"operator",1,0,*;"pan";
                    "receiveAddress";"receiveName";"receiveTelephone";
                    "cardNo";"accountName",2,0,*;"cidNo";"mobile"
                </sensitiveKey>
                <pattern>%d{MMdd HHmmss SSS} [%-4t] %-5p %c: [%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}] %L %m%n</pattern>
            </layout>
        </encoder>
    </appender>


    <!-- 简要日志 -->
    <appender name="ABSTRACT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${APP_NAME}.abstract.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/${APP_NAME}.abstract.%d{yyyy-MM-dd-HH}.log</FileNamePattern>
            <MaxHistory>180</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{MMdd HHmmss SSS} - [%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}] %m%n</pattern>
        </encoder>
    </appender>

    <logger name="abstractLogger" level="INFO" additivity="false">
        <appender-ref ref="ABSTRACT"/>
    </logger>

    <!-- 性能日志 -->
    <appender name="PERFORMANCE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${APP_NAME}.performance.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/${APP_NAME}.performance.%d{yyyy-MM-dd-HH}.log</FileNamePattern>
            <MaxHistory>180</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%m,%d{yyyyMMdd HHmmss},%-4t%n</pattern>
        </encoder>
    </appender>

    <logger name="performanceLogger" level="INFO" additivity="false">
        <appender-ref ref="PERFORMANCE"/>
    </logger>

<!-- 
<logger level="DEBUG" name="com.aliyun.oss"/>  
-->
    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="@log.appender@"/>
    </root>
</configuration>
