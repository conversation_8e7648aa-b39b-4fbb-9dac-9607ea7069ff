package com.pay.tp.core.mapper.auth;

import java.util.List;

import com.pay.tp.core.beans.auth.FaceRecognitionAuthParam;
import com.pay.tp.core.entity.auth.FaceRecognitionAuth;


public interface FaceRecognitionAuthMapper {

	void insert(FaceRecognitionAuth auth);

	void updateById(FaceRecognitionAuth auth);

	 List<FaceRecognitionAuth> queryByRequestNo(FaceRecognitionAuthParam param);

	 List<FaceRecognitionAuth> query(FaceRecognitionAuthParam param);
}
