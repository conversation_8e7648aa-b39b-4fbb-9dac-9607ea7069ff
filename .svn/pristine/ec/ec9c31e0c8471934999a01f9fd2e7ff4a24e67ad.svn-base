package com.pay.tp.core.beans.sms;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 信息通信请求参数
 * 发送信息请求Bean
 *
 * <AUTHOR>
 */
@Data
public class SmsMsgReq {

    /**
     * 唯一请求号
     */
    @NotBlank
    private String requestNo;

    /**
     * 消息模板ID
     */
    @NotBlank
    private String templateCode;

    /**
     * 消息模板参数 {"p1":"v1", "p2":"v2"}
     */
    private String templateParam;

    /**
     * 消息类型
     */
    private String msgType;

}
