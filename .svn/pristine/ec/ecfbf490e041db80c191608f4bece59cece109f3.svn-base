package com.pay.tp.core.entity.msgmanage;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MSG_TEMPLATE_LOG
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MsgTemplateLog implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 消息模板名称
     */
    private String operatorName;

    /**
     * 变更前
     */
    private String before;

    /**
     * 变更后
     */
    private String after;

    /**
     * 模板ID
     */
    private Long templateId;
}