package com.pay.tp.core.remote.sms.aliyun;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.JsonUtils;
import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.remote.sms.SmsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AliyunSmsClient implements SmsClient {


    @Value("${custom.aliyun.sms.url:}")
    private String url;
    @Value("${custom.aliyun.sms.accessKeyId:}")
    private String accessKeyId;
    @Value("${custom.aliyun.sms.accessKeySecret:}")
    private String accessKeySecret;
    @Value("${custom.aliyun.sms.sign:}")
    private String sign;


    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    @Autowired
    private AliyunSmsTemplateUtil aliyunSmsTemplateUtil;


    /**
     * 短信API产品名称（短信产品名固定，无需修改）
     */
    private final String product = "Dysmsapi";


    @Override
    public Map<String, Object> request(String requestNo, String content, String phone) throws Exception {
        try {
            AliyunSmsTemplateReverse templateReverse = aliyunSmsTemplateUtil.parseTemplate(content);
            log.info("aliyun sms template parse reverse content：{}， reverse：{}", content, templateReverse);

            IClientProfile profile = DefaultProfile.getProfile("cn-beijing", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-beijing", "cn-beijing", product, url);
            if (!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)) {
                log.info("proxy set {}, {}", proxyHost, proxyPort);
                HttpClientConfig httpClientConfig = HttpClientConfig.getDefault();
                httpClientConfig.setHttpProxy("http://" + proxyHost + ":" + proxyPort);
                httpClientConfig.setHttpsProxy("http://" + proxyHost + ":" + proxyPort);
                profile.setHttpClientConfig(httpClientConfig);
            }
            IAcsClient acsClient = new DefaultAcsClient(profile);
            SendSmsRequest request = new SendSmsRequest();
            request.setSysProtocol(ProtocolType.HTTPS);
            request.setMethod(MethodType.POST);
            request.setPhoneNumbers(phone);
            request.setSignName(sign);
            request.setTemplateCode(templateReverse.getCode());
            request.setTemplateParam(templateReverse.getParams());

            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            log.info("aliyun send sms: 【request: {}】, 【response: {}】", request, JsonUtils.bean2Json(sendSmsResponse));
            Map<String, Object> map = new HashMap<>(2);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                map.put("code", 0);
                map.put("result", 0);
                map.put("msg", sendSmsResponse.getMessage());
            } else {
                map.put("code", -1);
                map.put("msg", sendSmsResponse.getMessage());
            }
            return map;
        } catch (Exception e) {
            log.error("短信通道发送失败 {}", content, e);
            throw new ServerException("短信通道发送失败");
        }
    }

    @Override
    public Map<String, Object> parse(SmsMsg sms) {
        Map<String, Object> map = new HashMap<>(2);
        if ("0".equals(sms.getResult())) {
            map.put("code", 0);
        } else {
            map.put("code", -1);
            map.put("msg", sms.getMessage());
        }

        return map;
    }

    @Override
    public List<SmsMsg> parse(List<Map<String, String>> list) {
        // TODO 暂时不接收回调，如果使用异步调用接口时需要接收回调。
        return null;
    }
}
