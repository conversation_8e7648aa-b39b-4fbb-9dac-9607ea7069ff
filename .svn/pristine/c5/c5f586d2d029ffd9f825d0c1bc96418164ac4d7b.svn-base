package com.pay.tp.core.entity.msgmanage;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * MSG_CHANNEL_CONFIG_LOG
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MsgChannelConfigLog {
    /**
     * ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 通道配置ID
     */
    private Long channelConfigId;

    /**
     * 变更前
     */
    private String before;

    /**
     * 变更后
     */
    private String after;

    /**
     * 操作员
     */
    private String operator;
}