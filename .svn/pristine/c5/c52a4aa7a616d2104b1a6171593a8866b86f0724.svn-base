//package com.pay.tp.core.job;
//
//import com.pay.frame.common.base.util.DateUtil;
//import com.pay.tp.core.biz.impl.AppFileLogBiz;
//import com.pay.tp.core.configuration.JobRegisterCondition;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Conditional;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR> * @date 2024/3/21
// * @apiNote
// */
//@Slf4j
//@Component
//@Conditional({JobRegisterCondition.class})
//public class AppFileLogTask {
//
//    @Autowired
//    private AppFileLogBiz appFileLogBiz;
//
//
//    @Scheduled(cron = "0 5 0 * * ?")
//    public void appWillBody() {
//        String startTime = DateUtil.addDay(-7);
//        appFileLogBiz.delFile(startTime);
//    }
//
//}
