package com.pay.tp.core.controller.auth;


import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.PayConstants;
import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.biz.BankCardAuthBiz;
import com.pay.tp.core.entity.auth.BankcardAuth;
import com.pay.tp.core.service.auth.BankCardAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**银行卡鉴权接口Api
* <AUTHOR>
* @version 创建时间：2018年12月19日 下午2:08:09
* @ClassName 
* @Description 
*/
@RestController
@RequestMapping("bankcard")
public class BankCardAuthController {
	private final Logger logger = LoggerFactory.getLogger(BankCardAuthController.class);
	
	@Autowired
	private BankCardAuthBiz bankCardAuthBiz;

	@Autowired
	private BankCardAuthService bankCardAuthService;

	
	/**银行卡鉴权接口
	 * result = \"01\", msg = \"验证通过\",result = \"02\", msg = \"验证不通过（具体说明）\",result = \"03\", msg = \"验证不确定\"
	 */
	@RequestMapping(value = "/auth", method = RequestMethod.POST)
	public Map<String,Object> auth(@Validated @RequestBody BankCardAuthParam param) throws Exception {

//		logger.info("method = auth, param = {}", param);
		
		Map<String, Object> results = bankCardAuthBiz.auth(param);
		logger.info("method = auth, results = {}", results);
		return results;
		
		
		
		/*Map<String,Object> map = new HashMap<>();
		//要求幂等
		BankcardAuth auth = bankCardAuthService.query(param);
		if (auth.getRspCod() != null) {
			return xlBankCardAuthClient.parse(auth);
		} else {

			XinlianRes xinlianRes = xlBankCardAuthClient.request(auth.getRequestNo(),auth.getCardNo(), auth.getName(),
					auth.getCidNo(), auth.getMobile(), auth.getCustomerName());

			BeanUtils.copyProperties(xinlianRes, auth, "id", "optismistic", "createTime", "requestNo", "name", "cidNo", "mobile", "cardNo");
			bankCardAuthService.update(auth);
			return xlBankCardAuthClient.parse(auth);
		}*/
	}


	/**
	 * @Description: 鉴权记录分页查询列表
	 * @param params
	 */
	@RequestMapping(value = "/findPageList", method = RequestMethod.POST)
	public ResultsBean<PageInfo<BankcardAuth>> findPageList(@RequestBody Map<String, String> params) {
		int currentPage = params.get("currentPage") == null ? 1: Integer.parseInt(params.get("currentPage"));
		PageInfo<BankcardAuth> page = bankCardAuthService.findPageList(currentPage, PayConstants.PAGE_SIZE, params);
		return ResultsBean.SUCCESS(page);
	}

	/**
	 * 根据id查询
	 * @param id
	 */
	@RequestMapping(value = "/findById", method = RequestMethod.GET)
	public ResultsBean<BankcardAuth> findById(@RequestParam("id") Long id) {
		BankcardAuth auth = bankCardAuthService.findById(id);
		return ResultsBean.SUCCESS(auth);
	}

	
}
