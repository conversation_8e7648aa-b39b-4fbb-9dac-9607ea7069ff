package com.pay.tp.core.remote.sms;

import cn.jiguang.common.ClientConfig;
import cn.jiguang.common.DeviceType;
import cn.jiguang.common.connection.HttpProxy;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.audience.AudienceTarget;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosAlert;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> zhangjian
 * @Package com.pay.sms.core.client
 * @Description: TODO
 * @date Date : 2019年02月13日 14:16
 */
@Component
public class JiguangClient {

    private final Logger logger = LoggerFactory.getLogger(JiguangClient.class);

    @Value("${custom.jiguang.merchant.app:}")
    private String merchantAppKey;
    @Value("${custom.jiguang.merchant.secret:}")
    private String merchantSecret;
    
    @Value("${custom.jiguang.agent.app:}")
    private String agentAppKey;
    @Value("${custom.jiguang.agent.secret:}")
    private String agentSecret;
    
    @Value("${custom.jiguang.agentuk.app:}")
    private String agentUkAppKey;
    @Value("${custom.jiguang.agentuk.secret:}")
    private String agentUkSecret;

    @Value("${custom.jiguang.newagentuk.app:}")
    private String newAgentUkAppKey;
    @Value("${custom.jiguang.newagentuk.secret:}")
    private String newAgentUkSecret;

    
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;
    @Value("${custom.jiguang.production:true}")
    private boolean apnsProduction;

    private Map<String, String> map = new HashMap<>();



    @PostConstruct
    public void init() {
        map.put("app-merchant-key", merchantAppKey);
        map.put("app-merchant-secret", merchantSecret);
        
        map.put("app-agent-key", agentAppKey);
        map.put("app-agent-secret", agentSecret);
        
        map.put("app-agent-uk-key", agentUkAppKey);
        map.put("app-agent-uk-secret", agentUkSecret);

        map.put("new-app-agent-uk-key", newAgentUkAppKey);
        map.put("new-app-agent-uk-secret", newAgentUkSecret);
        
    }

    public String request(String appName, String requestNo, String alert, String content, Map<String, String> extras, Set<String> tagsAnd, Set<String> tags, Set<String> deviceNumbers, Set<String> aliases, Set<String> platforms,Boolean mutableContent) {

//        logger.info("method = request, appName = {}, requestNo = {}, alert = {}, content = {}, extras = {}, tagsAdd = {}, tags = {}, deviceNumbers = {}, aliases = {}, platforms = {}", appName, requestNo, alert, content, extras, tagsAnd, tags, deviceNumbers, aliases, platforms);
        HttpProxy httpProxy = null;
        if(!StringUtils.isEmpty(proxyHost)){
            httpProxy = new HttpProxy(proxyHost, proxyPort);
        }
        JPushClient jPushClient = new JPushClient(map.get(appName+"-secret"), map.get(appName+"-key"), httpProxy, ClientConfig.getInstance());

        Message message = null;
        if(!StringUtils.isEmpty(content)){
            Message.Builder messageBuilder = Message.newBuilder();
            messageBuilder.setMsgContent(content);
            if (extras != null && !extras.isEmpty()) {
                messageBuilder.addExtras(extras);
            }
            message = messageBuilder.build();
        }

        Notification notification = null;
        if(!StringUtils.isEmpty(alert)){
            Notification.Builder notificationBuilder = Notification.newBuilder();
            notificationBuilder.setAlert(alert);
            AndroidNotification.Builder  androidNotificationBuilder = AndroidNotification.newBuilder();
            IosNotification.Builder  iosNotificationBuilder = IosNotification.newBuilder();
            androidNotificationBuilder.setAlert(alert);

            IosAlert.Builder iosAlert= IosAlert.newBuilder();
            iosAlert.setTitleAndBody(extras.get("title"),null,alert);
            iosNotificationBuilder.setAlert(iosAlert.build());

            if (mutableContent!=null){
                iosNotificationBuilder.setMutableContent(mutableContent);
            }
            if(extras != null && !extras.isEmpty()){
                androidNotificationBuilder.addExtras(extras);
                iosNotificationBuilder.addExtras(extras);
            }

            AndroidNotification androidNotification = androidNotificationBuilder.build();
            IosNotification iosNotification = iosNotificationBuilder.build();

            notificationBuilder.addPlatformNotification(androidNotification);
            notificationBuilder.addPlatformNotification(iosNotification);
            notification = notificationBuilder.build();

        }


        Audience.Builder audienceBuilder = Audience.newBuilder();
        if (tags != null && !tags.isEmpty()) {
            audienceBuilder.addAudienceTarget(AudienceTarget.tag(tags));
        }

        if (tagsAnd != null && !tagsAnd.isEmpty()) {
            audienceBuilder.addAudienceTarget(AudienceTarget.tag_and(tagsAnd));
        }

        if (deviceNumbers != null && !deviceNumbers.isEmpty()) {
            audienceBuilder.addAudienceTarget(AudienceTarget.registrationId(deviceNumbers));
        }

        Audience audience = audienceBuilder.build();


        PushPayload.Builder pushPayloadBuilder = PushPayload.newBuilder();

        if (platforms == null || platforms.isEmpty()) {
            pushPayloadBuilder.setPlatform(Platform.all());
        } else {
            Platform.Builder platformBuilder = Platform.newBuilder();
            for (String platform : platforms) {
                platformBuilder.addDeviceType(DeviceType.valueOf(platform));
            }
            pushPayloadBuilder.setPlatform(platformBuilder.build());
        }

        pushPayloadBuilder.setAudience(audience).setOptions(Options.newBuilder()
                        .setApnsProduction(apnsProduction).build());

        if(notification != null){
            pushPayloadBuilder.setNotification(notification);
        }

        if(message != null){
            pushPayloadBuilder.setMessage(message);
        }
        //TODO ios测试代码，生产是否取消待确认
//        Options options = Options.newBuilder().build();
//        options.setApnsProduction(false);
//        pushPayloadBuilder.setOptions(options);

        PushPayload pushPayload = pushPayloadBuilder
                .build();


        try {
//            logger.info("pushPayload:{}", pushPayload);
            PushResult result = jPushClient.sendPush(pushPayload);
            logger.info("Got result - " + result);
            return result.toString();

        } catch (APIConnectionException e) {
            logger.error("Connection error, should retry later", e);
            return "";

        } catch (APIRequestException e) {
            logger.error("Should review the error, and fix the request", e);
            logger.info("HTTP Status: " + e.getStatus());
            logger.info("Error Code: " + e.getErrorCode());
            logger.info("Error Message: " + e.getErrorMessage());
            return "";
        }
    }
}