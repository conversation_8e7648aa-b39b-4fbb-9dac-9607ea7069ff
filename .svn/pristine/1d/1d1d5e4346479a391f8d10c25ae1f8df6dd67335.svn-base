<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.sms.SmsMovementMapper">


    <select id="query" resultType="com.pay.tp.core.entity.sms.SmsMovement"
            parameterType="com.pay.tp.core.beans.sms.JiguangParam">
        select
          ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, CONTENT, ALERT, RESULT, EXTRAS, TAGS_AND,TAGS,ALIASES,PLATFORMS
        from UBADMA.SMS_MOVEMENT
        where REQUEST_NO = #{requestNo}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.sms.SmsMovement">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_SMS_MOVEMENT_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.SMS_MOVEMENT (ID, OPTIMISTIC, CREATE_TIME, REQUEST_NO, CONTENT, ALERT, EXTRAS, TAGS_AND,TAGS,ALIASES,PLATFORMS)
        values (#{id,jdbcType=DECIMAL},#{optismistic},#{createTime},#{requestNo},#{content}, #{alert}, #{extras}, #{tagsAnd}, #{tags}, #{aliases},
        #{platforms})
    </insert>

    <update id="update">
        update UBADMA.SMS_MOVEMENT set
        result = #{result}
        where ID = #{id}
  	</update>

</mapper>