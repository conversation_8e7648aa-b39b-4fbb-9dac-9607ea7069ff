package com.pay.tp.core.beans.auth;

import lombok.Data;

/**
 * <AUTHOR> zhaomeng
 * @Description: 人脸识别认证
 * @date Date : 2021年05月12日 14:33
 */
@Data
public class FaceRecognitionAuthParam implements java.io.Serializable{
    private static final long serialVersionUID = -3953787863849467726L;
    /**
     * 请求号
     */
    private String requestNo;
    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String cidNo;
    /**
     * 身份证人像面翻拍照
     */
    private String idPhoto;
    /**
     * 活体现场照
     */
    private String dataPhoto;

    private String customerNo;

}
