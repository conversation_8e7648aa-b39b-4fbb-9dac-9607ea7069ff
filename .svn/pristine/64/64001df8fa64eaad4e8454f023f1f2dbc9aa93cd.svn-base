<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.auth.CompanyCheckMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.auth.CompanyCheck">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTISMISTIC" jdbcType="DECIMAL" property="optismistic"/>
        <result column="CREATETIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="REQUEST_NO" jdbcType="VARCHAR" property="requestNo"/>
        <result column="ENT_NAME" jdbcType="VARCHAR" property="entName"/>
        <result column="REG_NO" jdbcType="VARCHAR" property="regNo"/>
        <result column="FR_NAME" jdbcType="VARCHAR" property="frName"/>
        <result column="CID_NO" jdbcType="VARCHAR" property="cidNo"
                typeHandler="com.pay.frame.common.base.plugins.SensitiveTypeHandler"/>
        <result column="TRANS_DATE" jdbcType="VARCHAR" property="transDate"/>
        <result column="TRANS_TIME" jdbcType="VARCHAR" property="transTime"/>
        <result column="PAY_SERIAL_NO" jdbcType="VARCHAR" property="paySerialNo"/>
        <result column="VALIDATE_STATUS" jdbcType="VARCHAR" property="validateStatus"/>
        <result column="RSP_MSG" jdbcType="VARCHAR" property="rspMsg"/>
        <result column="VALIDATE_DESCRIBE" jdbcType="VARCHAR" property="validateDescribe"/>
        <result column="RESULT_ENT_NAME" jdbcType="VARCHAR" property="resultEntName"/>
        <result column="RESULT_REG_NO" jdbcType="VARCHAR" property="resultRegNo"/>
        <result column="DATA" jdbcType="VARCHAR" property="data"/>
        <result column="RSP_COD" jdbcType="VARCHAR" property="rspCod"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTISMISTIC, CREATETIME, REQUEST_NO, ENT_NAME, REG_NO, FR_NAME, CID_NO, TRANS_DATE,
    TRANS_TIME, PAY_SERIAL_NO, VALIDATE_STATUS, RSP_MSG, VALIDATE_DESCRIBE, RESULT_ENT_NAME,
    RESULT_REG_NO, "DATA", RSP_COD
  </sql>

    <select id="query" resultMap="BaseResultMap"
            parameterType="com.pay.tp.core.beans.auth.CompanyCheckParam">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_COMPANY_CHECK
        where ENT_NAME = #{entName}
        and REG_NO = #{regNo}
        and FR_NAME = #{frName}
        and CID_NO = #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler}
        and RSP_COD ='000000'
        order by CREATETIME desc
    </select>

    <select id="queryByRequestNo" resultType="com.pay.tp.core.entity.auth.CompanyCheck"
            parameterType="com.pay.tp.core.beans.auth.CompanyCheckParam">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.TP_COMPANY_CHECK
        where REQUEST_NO = #{requestNo}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.auth.CompanyCheck">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_TP_COMPANY_CHECK.nextval FROM dual
        </selectKey>
        insert into UBADMA.TP_COMPANY_CHECK (ID,OPTISMISTIC, CREATETIME, ENT_NAME,REG_NO,FR_NAME,CID_NO,REQUEST_NO)
        values (#{id,jdbcType=DECIMAL},0, sysdate, #{entName},#{regNo},#{frName},
        #{cidNo,jdbcType=VARCHAR,typeHandler=com.pay.frame.common.base.plugins.SensitiveTypeHandler},
        #{requestNo})
    </insert>

    <update id="update" parameterType="java.util.Map">
		    update UBADMA.TP_COMPANY_CHECK set
		    TRANS_DATE = #{transDate},
		    PAY_SERIAL_NO=#{paySerialNo},
		    RSP_MSG=#{rspMsg},
		    TRANS_TIME =#{transTime},
		    RSP_COD=#{rspCod},
		    VALIDATE_STATUS = #{validateStatus},
		    VALIDATE_DESCRIBE = #{validateDescribe},
		    data = #{data}
		    where ID = #{id}
  		</update>

</mapper>