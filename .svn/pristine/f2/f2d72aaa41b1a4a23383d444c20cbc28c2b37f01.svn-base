//package com.pay.tp.core.controller.fyt;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.pay.frame.common.base.bean.ResultsBean;
//import com.pay.tp.core.beans.fyt.FytFindUserReqBean;
//import com.pay.tp.core.beans.fyt.FytFindUserRespBean;
//import com.pay.tp.core.beans.fyt.FytGetH5JumpUrlReqBean;
//import com.pay.tp.core.beans.fyt.FytRegisterReqBean;
//import com.pay.tp.core.biz.impl.FytBiz;
//
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 法援通
// */
//@Slf4j
//@RestController
//@RequestMapping("/fyt")
//public class FytController {
//
//    @Autowired
//    private FytBiz fytBiz;
//
//
//    /**
//     * 注册法援通用户
//     */
//    @PostMapping("/register")
//    public ResultsBean<String> register(@RequestBody @Validated FytRegisterReqBean req) {
//        return fytBiz.register(req);
//    }
//
//
//    /**
//     * 查询是否注册法援通用户
//     */
//    @PostMapping("/findUser")
//    public ResultsBean<FytFindUserRespBean> findUser(@RequestBody @Validated FytFindUserReqBean req) {
//        return fytBiz.findUser(req);
//    }
//}
