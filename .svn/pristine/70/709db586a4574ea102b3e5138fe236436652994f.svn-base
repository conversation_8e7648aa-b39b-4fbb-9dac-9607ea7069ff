package com.pay.tp.core.remote.auth.jixin;


import org.apache.commons.codec.digest.DigestUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 签名工具类
 *
 * <AUTHOR>
 * @className SignUtil
 * @Description
 * @contact
 * @date 2016-6-7 下午11:11:00
 */
public class SignUtil {


    /**
     * 根据map key升序排序
     *
     * @param sortedParams
     * @return
     */
    public static String getSign(Map<String, Object> sortedParams, String signkey) throws Exception {

        StringBuffer signSrc = new StringBuffer();
        List<String> keys = new ArrayList<String>(sortedParams.keySet());
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = sortedParams.get(key);
            if (key != null && !"".equals(key) && value != null && !"sign".equals(key)) {
                signSrc.append(key + "=" + value);
            }
        }
        String sign = DigestUtils.md5Hex(signSrc.toString() + signkey);
        System.out.println("加签原串:" + signSrc.toString() + signkey);
        return sign;

    }

    /**
     * map转对象
     *
     * @param map
     * @param beanClass
     * @return
     * @throws Exception
     */
    public static Object mapToObject(Map<String, Object> map, Class<?> beanClass) throws Exception {
        if (map == null)
            return null;

        Object obj = beanClass.newInstance();

        BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        for (PropertyDescriptor property : propertyDescriptors) {
            Method setter = property.getWriteMethod();
            if (setter != null) {
                setter.invoke(obj, map.get(property.getName()));
            }
        }

        return obj;
    }

}
