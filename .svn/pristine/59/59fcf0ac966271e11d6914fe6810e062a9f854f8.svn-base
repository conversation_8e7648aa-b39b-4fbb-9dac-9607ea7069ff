<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.AppFileLogMapper">
  <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.AppFileLog">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="APP_CODE" jdbcType="VARCHAR" property="appCode" />
    <result column="USER_NO" jdbcType="VARCHAR" property="userNo" />
    <result column="USER_TYPE" jdbcType="VARCHAR" property="userType" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
    <result column="FTP_URL" jdbcType="VARCHAR" property="ftpUrl" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, CREATE_TIME, APP_CODE, USER_NO, USER_TYPE, URL, FTP_URL, STATUS
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UBADMA.APP_FILE_LOG
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="findBeforeTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.APP_FILE_LOG
    where STATUS != 'DELETE' AND CREATE_TIME >= SYSDATE-14
    AND CREATE_TIME <![CDATA[ <= ]]> to_date(#{startTime} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss')
  </select>

  <select id="findByPageAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UBADMA.APP_FILE_LOG
    <where>
      STATUS != 'DELETE'
      <if test="status != null and status != '' ">
        and STATUS = #{status,jdbcType=VARCHAR}
      </if>
      <if test="appCode != null and appCode != '' ">
        and APP_CODE = #{appCode,jdbcType=VARCHAR}
      </if>
      <if test="userNo != null and userNo != '' ">
        and USER_NO = #{userNo,jdbcType=VARCHAR}
      </if>
      <if test="userType != null and userType != '' ">
        and USER_TYPE = #{userType,jdbcType=VARCHAR}
      </if>
      <if test="createTimeStart != null and createTimeStart != '' " >
        <![CDATA[
		    and CREATE_TIME >= to_date(#{createTimeStart,jdbcType=TIMESTAMP} || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
		]]>
      </if>
      <if test="createTimeEnd != null and createTimeEnd != '' " >
        <![CDATA[
		    and CREATE_TIME <= to_date(#{createTimeEnd,jdbcType=TIMESTAMP} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
		]]>
      </if>
    </where>
    order by id desc
  </select>

  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.pay.tp.core.entity.AppFileLog" useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
      SELECT UBADMA.SEQ_APP_FILE_LOG_ID.nextval FROM dual
    </selectKey>
    insert into UBADMA.APP_FILE_LOG (ID, CREATE_TIME, APP_CODE, USER_NO,
      USER_TYPE, URL, STATUS)
    values (#{id,jdbcType=DECIMAL}, SYSDATE, #{appCode,jdbcType=VARCHAR}, #{userNo,jdbcType=VARCHAR},
      #{userType,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 'TRUE')
  </insert>

  <update id="updateFtpUrl" parameterType="com.pay.tp.core.entity.AppFileLog">
    update UBADMA.APP_FILE_LOG
    set
      FTP_URL = #{ftpUrl,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <update id="updateStatus" parameterType="com.pay.tp.core.entity.AppFileLog">
    update UBADMA.APP_FILE_LOG
    set
      STATUS = #{status,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

</mapper>