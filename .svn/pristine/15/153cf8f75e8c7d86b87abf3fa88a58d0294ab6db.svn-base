package com.pay.tp.core.exception;

/**
 * 乐观锁异常
 *
 * <AUTHOR>
 */
public class SmsOptimisticException extends RuntimeException {

    protected String errCode;

    protected String errMsg;

    public SmsOptimisticException(String errCode, String errMsg) {
        super(errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public SmsOptimisticException(String errCode, String errMsg, String message) {
        super(message);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public SmsOptimisticException(String errCode, String errMsg, Throwable e) {
        super(errMsg, e);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
