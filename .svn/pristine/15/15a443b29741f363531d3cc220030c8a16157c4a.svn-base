package com.pay.tp.core.remote;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.CommonConstants;
import com.pay.tp.core.remote.bean.CustomerRsp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 商户查询
 */
@FeignClient(value = CommonConstants.CUST_EUREKA_SERVER_INSTANCE_CORE)
public interface CustomerQueryClient {


	/**
	 * @param customerNo
	 * @return
	 * @Description 按照商户编号查询
	 */
	@RequestMapping(value = CommonConstants.CUST_APPLICATION_NAME_CORE + "/customerQuery/getCust/{custNo}", method = RequestMethod.GET)
	ResultsBean<CustomerRsp> findCustByNo(@PathVariable("custNo") String customerNo);
	
	@RequestMapping(value = CommonConstants.CUST_APPLICATION_NAME_CORE + "/customerQuery/findByThirdCustNo", method = RequestMethod.GET)
	ResultsBean<CustomerRsp> findByThirdCustNo(@RequestParam("thirdCustNo") String thirdCustNo, @RequestParam("origin") String origin) ;
}
