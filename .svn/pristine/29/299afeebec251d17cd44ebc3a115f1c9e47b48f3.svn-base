package com.pay.tp.core.controller.tencent;

import com.github.pagehelper.PageInfo;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.service.WillBodyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/slResult")
@Slf4j
public class SlResultProvider {

    @Autowired
    private WillBodyRecordService willBodyRecordService;

    /**
     * 分页查询
     *
     * @param param
     * @return com.pay.frame.common.base.bean.ResultsBean<com.github.pagehelper.PageInfo < com.pay.tp.core.entity.WillBodyRecord>>
     * <AUTHOR>
     * @date 2022/7/20 14:01
     */
    @RequestMapping("/findPage")
    public ResultsBean<PageInfo<WillBodyRecord>> findPage(@RequestBody Map<String, String> param) {
        PageInfo<WillBodyRecord> page = willBodyRecordService.findPage(param);
        return ResultsBean.SUCCESS(page);
    }


}
