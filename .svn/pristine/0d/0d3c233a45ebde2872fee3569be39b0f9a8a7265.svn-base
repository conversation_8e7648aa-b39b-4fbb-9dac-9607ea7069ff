package com.pay.tp.core.beans.tencent;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @date 2022年07月19日 10:18
 */
@Data
public class AppWillBodyResultReq {

    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @NotBlank(message = "用户编号不能为空")
    private String ownerNo;

    private String willVideoPath;

    // 1.4.4 最新版本字段
    private String aliVideoPath;

    @NotBlank(message = "变量值不能为空")
    private String variableValue;

    @NotBlank(message = "来源不能为空")
    private String source;

    @NotBlank(message = "操作员不能为空")
    private String operator;

    /**
     * 活体检测分数
     */
    private String liveRate;

    /** 人脸比对得分 */
    private String similarity;

    /**
     * 人脸识别结果
     */
    private String faceCode;

    /**
     * 人脸识别结果描述
     */
    private String faceMsg;

    /**
     * 语音意愿表达结果
     */
    private String willCode;

    /**
     * 语音意愿表达结果描述
     */
    private String willMsg;

    private String brand;


//============================= 嘉联双录参数 =============================
    /**
     * 交易流水号
     */
    private String transId;

    /**
     * 是否为补充双录
     */
    private boolean supplySl = false;
}
