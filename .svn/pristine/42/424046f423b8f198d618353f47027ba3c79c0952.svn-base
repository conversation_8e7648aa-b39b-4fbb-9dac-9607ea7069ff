package com.pay.tp.core.controller.tencent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.StringUtils;
import com.pay.tp.core.beans.tencent.AppWillBodyResultReq;
import com.pay.tp.core.beans.tencent.EidWillBodyResultReq;
import com.pay.tp.core.biz.impl.TencentBiz;
import com.pay.tp.core.entity.WillBodyRecord;
import com.pay.tp.core.enums.WillType;
import com.pay.tp.core.service.WillBodyRecordService;
import com.tencentcloudapi.faceid.v20180301.models.GetEidTokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***
 * 腾讯云
 */
@Slf4j
@RestController
@RequestMapping("/tencent")
public class TencentController {
    
    @Autowired
    private TencentBiz tencentBiz;

	@Autowired
	private WillBodyRecordService willBodyRecordService;


    @PostMapping("/findTicket")
    public ResultsBean<Map<String, String>> findTicket(@RequestBody Map<String,String> param) {
    	try {
    		Map<String, String> map = tencentBiz.findTicket(param);
    		return ResultsBean.SUCCESS(map);
    	} catch (ServerException e) {
    		log.error("se获取ticket异常 error{}", e);
    		return ResultsBean.FAIL(e.getMessage());
    	} catch (Exception e) {
    		log.error("获取ticket异常 error{}", e);
    		return ResultsBean.FAIL("获取ticket异常");
    	}
    }


	/**
	 *	保存意愿核身
	 * @param userId
	 * @return
	 */
	@PostMapping("/appWillBody")
	public ResultsBean<String> appWillBody(@RequestBody @Validated AppWillBodyResultReq req) {
		try {
			tencentBiz.appWillBody(req);
			return ResultsBean.SUCCESS();
		} catch (ServerException e) {
			log.error("App 保存意愿核身异常 error{}", e);
			return ResultsBean.FAIL(e.getMessage());
		} catch (Exception e) {
			log.error("App 保存意愿核身异常 error{}", e);
			return ResultsBean.FAIL("保存意愿核身异常");
		}
	}


	/**
	 *	保存意愿核身
	 * 聚合商户端使用
	 */
	@PostMapping("/unAppWillBody")
	public ResultsBean<String> unAppWillBody(@RequestBody @Validated AppWillBodyResultReq req) {
		try {
//			if (StringUtils.isBlank(req.getAliVideoPath())){
//				throw new ServerException("双录视频不能为空");
//			}
			tencentBiz.unAppWillBody(req);
			return ResultsBean.SUCCESS("成功");
		} catch (ServerException e) {
			log.error("App 保存意愿核身异常 error{}", e);
			return ResultsBean.FAIL(e.getMessage());
		} catch (Exception e) {
			log.error("App 保存意愿核身异常 error{}", e);
			return ResultsBean.FAIL("保存意愿核身异常");
		}
	}


	/**
	 *	下载阿里视频
	 * @param userId
	 * @return
	 */
	@PostMapping("/downALiPath")
	public ResultsBean<String> downALiPath(@RequestParam("orderNo") String orderNo, @RequestParam("brand") String brand) {
		try {
			WillBodyRecord bodyRecord = willBodyRecordService.findByOrderNo(orderNo, brand);
			if(null == bodyRecord){
				return ResultsBean.FAIL("订单号不存在"+orderNo +"_"+ brand);
			}

			if(WillType.APP.name().equals(bodyRecord.getWillType())){
				tencentBiz.downALiPath(bodyRecord);
			}else{
				tencentBiz.getEIdvideo(bodyRecord);
			}

			return ResultsBean.SUCCESS();
		} catch (Exception e) {
			log.error("下载阿里视频或拉取意愿视频 异常 error{}", e);
			return ResultsBean.FAIL("下载阿里视频或拉取意愿视频 异常");
		}
	}


	/**
	 *	上传双录
	 * @return
	 */
	@PostMapping("/bossPushSl")
	public ResultsBean<String> bossPushSl(@RequestParam("orderNo") String orderNo, @RequestParam("brand") String brand) {
		try {
			WillBodyRecord bodyRecord = willBodyRecordService.findByOrderNo(orderNo, brand);
			if(null == bodyRecord){
				return ResultsBean.FAIL("订单号不存在"+orderNo +"_"+ brand);
			}

			if(WillType.APP.name().equals(bodyRecord.getWillType())){
				tencentBiz.uploadSlToChannel(bodyRecord);
			}else{
				throw new ServerException("小程序暂不支持此功能");
//				tencentBiz.getEIdvideo(bodyRecord);
			}

			return ResultsBean.SUCCESS();
		} catch (Exception e) {
			log.error("上传双录 异常 error{}", e);
			return ResultsBean.FAIL("上传双录 异常");
		}
	}

	@PostMapping("/pushHistoryDbSl")
	public ResultsBean<String> pushHistoryDbSl(@RequestParam("customerNo") String customerNo,
											   @RequestParam("posSn") String posSn, @RequestParam("amount") String amount, @RequestParam("brand") String brand) {
		try {
			return willBodyRecordService.pushHistoryDbSl(customerNo, posSn, amount, brand);
		} catch (Exception e) {
			log.error("复用db上传双录 异常 error{}", e);
			return ResultsBean.FAIL("上传双录 异常");
		}
	}


	/**
	 * 查询小程序 eidtoken
	 * @param param
	 * @return
	 */
	@PostMapping("/findEidToken")
	public ResultsBean<GetEidTokenResponse> findEidToken(@RequestBody Map<String,String> param) {
		try {
			GetEidTokenResponse resp = tencentBiz.findEidToken(param);
			return ResultsBean.SUCCESS(resp);

		} catch (ServerException e) {
			log.error("se获取eidtoken异常 error{}", e);
			return ResultsBean.FAIL(e.getMessage());

		} catch (Exception e) {
			log.error("获取eidtoken异常 error{}", e);
			return ResultsBean.FAIL("获取eidtoken异常");
		}
	}


	/**
	 * 查询 eid 双录是否成功
	 * @param req
	 * @return
	 */
	@PostMapping("/findEidResult")
	public ResultsBean<Map<String, String>> findEidResult(@RequestBody @Validated EidWillBodyResultReq req) {
		try {

			WillBodyRecord record = tencentBiz.saveEidResult(req);
			if("0".equals(record.getFaceCode()) && "0".equals(record.getWillCode())){
				return ResultsBean.SUCCESS();
			}

			return ResultsBean.FAIL(record.getFaceMsg());
		} catch (Exception e) {
			log.error("查询 eid 双录是否成功异常 error{}", e);
			return ResultsBean.FAIL(e.getMessage());
		}
	}


}
