package com.pay.tp.core.remote.auth.xinlian;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.pay.tp.core.entity.auth.CompanyCheck;

/**
 *
 */
@Service
@Profile({"qa","dev"})
public class MockXlCompanyCheckClient implements XlCompanyCheckClient{

    private final Logger logger = LoggerFactory.getLogger(MockXlCompanyCheckClient.class);



    //01-	匹配
    //02-	不匹配
    //03-	未报送
    //04-	认证失败

    @Override
    public Map<String,Object> parse(CompanyCheck companyCheck){

        Map<String,Object> map = new HashMap<>();

        if("000000".equals(companyCheck.getRspCod())){
            String validateStatus = companyCheck.getValidateStatus();
            if("01".equals(validateStatus)){
                map.put("result", validateStatus);
                map.put("msg", companyCheck.getValidateDescribe());
            }else{
                map.put("result","02");
                map.put("msg", companyCheck.getValidateDescribe());
            }

        }else{
            map.put("result","02");
            map.put("msg", companyCheck.getRspMsg());
        }

        return map;
    }


    @Override
    public Map<String,Object> request(String serialNo, String entName, String regNo, String frName, String cidNo) throws Exception {
        logger.info("method = request, entName = {}, regNo = {}, frName = {}, cidNo = {}, serialNo = {}", entName, regNo, frName, cidNo, serialNo);

        long random = System.currentTimeMillis()%2;
        logger.info("##########" +  random + "");
        if(random == 0){
            String json = "{\"transDate\":\"20190111\",\"validateStatus\":\"01\",\"paySerialNo\":\"" + System.currentTimeMillis() + "\",\"rspMsg\":\"认证通过\",\"transTime\":\"184237\",\"reqSerialNo\":\"" + System.currentTimeMillis() + "\",\"rspCod\":\"000000\",\"version\":\"1.0\"}";
            return new Gson().fromJson(json, Map.class);
        }else{
            String json = "{\"transDate\":\"20190111\",\"paySerialNo\":\"" + System.currentTimeMillis() + "\",\"rspMsg\":\"参数企业法人身份证不合法\",\"transTime\":\"184237\",\"reqSerialNo\":\"" + System.currentTimeMillis() + "\",\"rspCod\":\"000010\",\"version\":\"1.0\"}";
            return new Gson().fromJson(json, Map.class);
        }


    }
}