package com.pay.tp.core.mapper.sms;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.tp.core.beans.sms.SmsParam;
import com.pay.tp.core.entity.sms.SmsMsg;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.sms.core.mapper
 * @Description: TODO
 * @date Date : 2018年12月24日 10:46
 */
public interface SmsMsgMapper {

    SmsMsg query(SmsParam param);

    void insert(SmsMsg sms);

    void update(SmsMsg Sms);
    
    public SmsMsg findById(Long id);
	
	public List<Map<String, Object>> findByPageAll(@Param("queryParams") Map<String, Object> queryParams);

    SmsMsg findByMsgIdPhone(@Param("phone") String phone, @Param("msgId") String msgId);

    void updateResMsg(SmsMsg smsMsg);
}
