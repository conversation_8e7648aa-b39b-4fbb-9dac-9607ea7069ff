package com.pay.tp.core.configuration;

import com.pay.job.executor.spring.SpringJobExecutor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 定时调度平台配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = {"job.executor"}, matchIfMissing = true)
@EnableConfigurationProperties(JobExecutorProperties.class)
public class JobExecutorConfig {

    @Bean(initMethod = "start", destroyMethod = "destroy")
    public SpringJobExecutor springJobExecutor(JobExecutorProperties jobExecutorProperties) {
        SpringJobExecutor springJobExecutor = new SpringJobExecutor();
        springJobExecutor.setDispatcherAddresses(jobExecutorProperties.getDispatcherAddresses());
        springJobExecutor.setAppName(jobExecutorProperties.getAppName());
        springJobExecutor.setIp(jobExecutorProperties.getIp());
        springJobExecutor.setPort(jobExecutorProperties.getPort());
        springJobExecutor.setAccessToken(jobExecutorProperties.getAccessToken());
        springJobExecutor.setLogPath(jobExecutorProperties.getLogPath());
        springJobExecutor.setLogRetentionDays(jobExecutorProperties.getLogRetentionDays());

        return springJobExecutor;
    }

}