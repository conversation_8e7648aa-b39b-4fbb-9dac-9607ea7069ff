<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.msgmanage.MsgChannelMapper">
    <resultMap id="BaseResultMap" type="com.pay.tp.core.entity.msgmanage.MsgChannel">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="WEIGHT" jdbcType="DECIMAL" property="weight"/>
        <result column="DESC" jdbcType="VARCHAR" property="desc"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, CODE, "NAME", "TYPE", "STATUS", WEIGHT, "DESC"
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL
        where ID = #{id,jdbcType=DECIMAL}
    </select>

    <select id="findByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL
        where CODE = #{code,jdbcType=VARCHAR}
        and STATUS = 'ENABLE'
    </select>

    <select id="findByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL
        where "TYPE" = #{type,jdbcType=VARCHAR}
        and STATUS = 'ENABLE'
    </select>

    <select id="findByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UBADMA.MSG_CHANNEL
        <where>
            1=1
            <if test="params.code != null and params.code != ''">
                and CODE=#{params.code}
            </if>
            <if test="params.status != null and params.status != ''">
                and STATUS=#{params.status}
            </if>
            <if test="params.name != null and params.name != ''">
                and "NAME"=#{params.name}
            </if>
            <if test="params.type != null and params.type != ''">
                and "TYPE"=#{params.type}
            </if>
            <if test="params.dateStart !=null and params.dateStart !=''">
                and CREATE_TIME &gt;= to_date(#{params.dateStart},'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="params.dateEnd !=null and params.dateEnd !=''">
                and CREATE_TIME &lt;= to_date(#{params.dateEnd},'yyyy-MM-dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.msgmanage.MsgChannel">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_MSG_CHANNEL_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.MSG_CHANNEL (ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, CODE,
        "NAME", "TYPE", "STATUS", WEIGHT,
        "DESC")
        values (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR}, #{weight,jdbcType=DECIMAL},
        #{desc,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.pay.tp.core.entity.msgmanage.MsgChannel">
    update UBADMA.MSG_CHANNEL
    set OPTIMISTIC = OPTIMISTIC + 1,
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CODE = #{code,jdbcType=VARCHAR},
      "NAME" = #{name,jdbcType=VARCHAR},
      "TYPE" = #{type,jdbcType=VARCHAR},
      "STATUS" = #{status,jdbcType=VARCHAR},
      WEIGHT = #{weight,jdbcType=DECIMAL},
      "DESC" = #{desc,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
    and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
  </update>
</mapper>