package com.pay.tp.core.beans.position;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;


/**基站定位请求参数
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.beans
 * @Description: TODO
 * @date Date : 2018年12月24日 14:29
 */
public class CellParam {
    @NotBlank
    private String requestNo;
    /**
     * 移动或者联通基站
     */
    @NotNull
    private String mnc;

    /**
     * 区域号
     */
    @NotNull
    private String lac;

    /**
     * 基站号
     */
    @NotNull
    private String cell;

    /**
     * 移动国家代码(中国的为 460)
     */
    @NotNull
    private String mcc;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getMnc() {
        return mnc;
    }

    public void setMnc(String mnc) {
        this.mnc = mnc;
    }

    public String getLac() {
        return lac;
    }

    public void setLac(String lac) {
        this.lac = lac;
    }

    public String getCell() {
        return cell;
    }

    public void setCell(String cell) {
        this.cell = cell;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    @Override
    public String toString() {
        return "CellParam{" +
                "requestNo='" + requestNo + '\'' +
                ", mnc='" + mnc + '\'' +
                ", lac='" + lac + '\'' +
                ", cell='" + cell + '\'' +
                ", mcc='" + mcc + '\'' +
                '}';
    }
}
