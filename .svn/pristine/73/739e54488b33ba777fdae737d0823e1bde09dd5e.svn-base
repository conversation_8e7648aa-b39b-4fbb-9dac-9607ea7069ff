package com.pay.tp.core.controller.wx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.wx.SendWxMsgReq;
import com.pay.tp.core.biz.impl.WeChatBiz;
import com.pay.tp.core.entity.jh.JhChannelCfg;
import com.pay.tp.core.remote.wx.WeChatClient;
import com.pay.tp.core.service.jh.JhCHannelCfgService;
import com.pay.tp.core.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/***
 * 微信授权登陆
 */
/**
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@RequestMapping("/weChat")
public class WeChatController {

    @Autowired
    private WeChatClient weChatClient;

    @Autowired
    private WeChatBiz weChatBiz;
    
    @Autowired
    private JhCHannelCfgService jhCHannelCfgService;


    /**
     * 公众号授权登录登录保存
     */
    @PostMapping("/weChatAuth")
    public ResultsBean<String> weChatAuth(@RequestParam("code") String code, @RequestParam("brand") String brand) {
        log.info("login params code:{} brand:{}", code,brand);
        try {
            String str = weChatClient.request(code, brand);
            JSONObject jsonObject = JSON.parseObject(str);
            log.info("公众号登录信息返回：{}", jsonObject);
            String openid = jsonObject.get("openid").toString();
            if (StringUtils.isEmpty(openid)) {
                return ResultsBean.FAIL("未关注公众号");
            }

            return ResultsBean.SUCCESS(openid);
        } catch (Exception e) {
            log.error("公众号登陆异常 error{}", e);
            return ResultsBean.FAIL("公众号登陆异常");
        }
    }


    /**
     * 发送消息
     * @param msgList
     * @param templateCode  requestNo , agentNo
     * @return
     */
    @RequestMapping(value = "/sendWxMessage")
    public ResultsBean<String> sendWxMessage(@RequestBody SendWxMsgReq sendWxMsgReq){
        if(StringUtils.isEmpty(sendWxMsgReq.getRequestNo())){
            return ResultsBean.FAIL("请求号不能为空");
        }
        weChatBiz.sendWxMessage(sendWxMsgReq);
        return ResultsBean.SUCCESS();
    }


    @PostMapping("/getJhOpenId")
    public ResultsBean<String> getJhOpenId(@RequestParam("code") String code,@RequestParam("appId") String appId) {
        log.info("login params code={},appId={},", code,appId);
        try {
        	JhChannelCfg jhChannelCfg = jhCHannelCfgService.selectByAppId(appId);
        	if (jhChannelCfg == null ) {
        		  return ResultsBean.FAIL("appid不存在或配置已关闭");
			}
            String str = weChatClient.getOpenId(code,jhChannelCfg.getAppId(),jhChannelCfg.getAppSecret());
            JSONObject jsonObject = JSON.parseObject(str);
            log.info("公众号登录信息返回：{}", jsonObject);
            String openid = jsonObject.get("openid").toString();
            if (StringUtils.isEmpty(openid)) {
                return ResultsBean.FAIL("未关注公众号");
            }

            return ResultsBean.SUCCESS(openid);
        } catch (Exception e) {
            log.error("公众号登陆异常 error{}", e);
            return ResultsBean.FAIL("公众号登陆异常");
        }
    }

    /**
     * 获取微信小程序的openId
     *
     * @param code
     * @return
     */
    @PostMapping("/getWxAppOpenId")
    public ResultsBean<Map<String, Object>> getWxAppOpenId(@RequestParam("code") String code,
                                                           @RequestParam("appId") String appId) {

        log.info("getWxAppOpenId params code={},appId={},", code, appId);
        try {
            JhChannelCfg jhChannelCfg = jhCHannelCfgService.selectByAppId(appId);
            if (jhChannelCfg == null) {
                return ResultsBean.FAIL("appid不存在或配置已关闭");
            }

            String str = weChatClient.getWxAppOpenId(code, jhChannelCfg.getAppId(), jhChannelCfg.getAppSecret());
            Map map = JsonUtil.toObject(str, Map.class);
            log.info("getWxAppOpenId res code={},appId={},res={}", code, appId, map);
            return ResultsBean.SUCCESS(map);
        } catch (Exception e) {
            log.error("getWxAppOpenId error", e);
            return ResultsBean.FAIL("系统异常");
        }
    }
}
