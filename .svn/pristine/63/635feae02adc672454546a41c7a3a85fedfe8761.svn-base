package com.pay.tp.core.service.sms;

import com.pay.tp.core.entity.sms.SmsMsg;
import com.pay.tp.core.remote.sms.SmsClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SmsClientAysnc {

    private final Logger logger = LoggerFactory.getLogger(SmsClientAysnc.class);

    @Autowired
    private SmsMsgService smsMsgService;

    @Async
    public void clientRequest(SmsClient smsClient, SmsMsg sms, String channelCode) {
        logger.info("method = clientRequest, smsMsg = {}, channelCode = {}", sms, channelCode);
        try {
            Map<String, Object> res = smsClient.request(sms.getRequestNo(), sms.getContent(), sms.getPhone());

            sms.setResult(res.getOrDefault("result", "").toString());
            sms.setMessage(res.getOrDefault("msg", "").toString());
            sms.setChannelCode(channelCode);
            sms.setMsgId(res.getOrDefault("msgId", "").toString());
            smsMsgService.update(sms);

        } catch (Exception e) {
            logger.error("method = clientRequest Exception = {}", e.getMessage(), e);
        }
    }
}
