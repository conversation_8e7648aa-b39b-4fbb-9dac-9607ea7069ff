<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.position.CellMapper">


    <select id="query" resultType="com.pay.tp.core.entity.position.Cell"
            parameterType="com.pay.tp.core.beans.position.CellParam">
        select ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, MNC,
        LAC, CELL, RESULT_CODE, REASON, D_LAC, D_CELL, D_LNG, D_LAT, O_LNG, O_LAT,
        D_MCC, D_MNC, "PRECISION", ADDRESS
        from UBADMA.POSI_CELL where REQUEST_NO = #{requestNo}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.position.Cell">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_POSI_CELL_ID.nextval FROM dual
        </selectKey>
        insert into UBADMA.POSI_CELL (ID,OPTIMISTIC,CREATE_TIME,MNC,LAC,CELL,REQUEST_NO)
        values (#{id,jdbcType=DECIMAL},0, sysdate, #{mnc},#{lac},
        #{cell},#{requestNo})
    </insert>

    <insert id="insertCell" parameterType="com.pay.tp.core.entity.position.Cell">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_POSI_CELL_ID.nextval FROM dual
        </selectKey>
        INSERT INTO "UBADMA"."POSI_CELL"("ID", "OPTIMISTIC", "CREATE_TIME",
        "REQUEST_NO", "MNC", "LAC", "CELL", "RESULT_CODE", "REASON", "D_LAC", "D_CELL",
        "D_LNG", "D_LAT", "O_LNG", "O_LAT", "D_MNC", "PRECISION", "ADDRESS", "AD_CODE")
        values (#{id,jdbcType=DECIMAL},0, sysdate, #{requestNo}, #{mnc}, #{lac}, #{cell}
        , #{resultCode}, #{reason}, #{dLac}, #{dCell}, #{dLng}, #{dLat}, #{oLng}, #{oLat}
        , #{dMnc}, #{precision}, #{address}, #{adCode} )
    </insert>

    <select id="queryByParam" resultType="com.pay.tp.core.entity.position.Cell"
            parameterType="com.pay.tp.core.beans.position.CellParam">
        select ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, MNC,
        LAC, CELL, RESULT_CODE, REASON, D_LAC, D_CELL, D_LNG, D_LAT, O_LNG, O_LAT,
        D_MCC, D_MNC, "PRECISION", ADDRESS
        from UBADMA.POSI_CELL
        where MNC = #{mnc} AND LAC = #{lac} AND CELL = #{cell} AND (ADDRESS IS NOT NULL OR REASON = '查询不到该基站信息')
    </select>


    <select id="queryLdysByParam" resultType="com.pay.tp.core.entity.position.Cell"
            parameterType="com.pay.tp.core.beans.position.CellParam">
        select ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, MNC,
        LAC, CELL, RESULT_CODE, REASON, D_LAC, D_CELL, D_LNG, D_LAT, O_LNG, O_LAT,
        D_MCC, D_MNC, "PRECISION", ADDRESS, AD_CODE
        from UBADMA.POSI_CELL
        where MNC = #{mnc}
        AND LAC = #{lac}
        AND CELL = #{cell}
        AND RESULT_CODE in ('00')
        and AD_CODE is not null
        order by CREATE_TIME desc
    </select>

    <select id="queryLdysAllByParam" resultType="com.pay.tp.core.entity.position.Cell"
            parameterType="com.pay.tp.core.entity.position.Cell">
        select ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, MNC,
        LAC, CELL, RESULT_CODE, REASON, D_LAC, D_CELL, D_LNG, D_LAT, O_LNG, O_LAT,
        D_MCC, D_MNC, "PRECISION", ADDRESS, AD_CODE
        from UBADMA.POSI_CELL
        where MNC = #{mnc}
        AND LAC = #{lac}
        AND CELL = #{cell}
    </select>
    <update id="update" parameterType="java.util.Map">
		    update UBADMA.POSI_CELL
            set
              RESULT_CODE = #{resultCode},
              REASON = #{reason},
              D_LAC = #{dLac},
              D_CELL = #{dCell},
              D_LAT = #{dLat},
              D_LNG = #{dLng},
              O_LAT = #{oLat},
              O_LNG = #{oLng},
              D_MNC = #{dMnc},
              D_MCC = #{dMcc},
              "PRECISION" = #{precision},
              ADDRESS = #{address}
		    where ID = #{id}
  		</update>

    <update id="updateByCell" parameterType="java.util.Map">
		    update UBADMA.POSI_CELL
            set
              D_LAT = #{dLat},
              D_LNG = #{dLng},
              O_LAT = #{oLat},
              O_LNG = #{oLng},
              ADDRESS = #{address},
              AD_CODE = #{adCode},
              LAC = #{lac},
              MNC = #{mnc},
              CELL = #{cell}
		    where LAC = #{lac} and CELL = #{cell}
  	</update>

    <select id="queryByLatLng" resultType="com.pay.tp.core.entity.position.Cell"
            parameterType="com.pay.tp.core.beans.position.CellParam">
        select ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, MNC,
        LAC, CELL, RESULT_CODE, REASON, D_LAC, D_CELL, D_LNG, D_LAT, O_LNG, O_LAT,
        D_MCC, D_MNC, "PRECISION", ADDRESS
        from UBADMA.POSI_CELL where  D_LAT = #{dLat}  and  D_LNG = #{dLng}
    </select>

    <select id="findByCondition" resultType="com.pay.tp.core.entity.position.Cell">
        select ID, CREATE_TIME, REQUEST_NO, MNC, LAC, CELL, ADDRESS, REASON, AD_CODE
        from UBADMA.POSI_CELL
        <where>
            1=1
            <if test="params.requestNo != null and params.requestNo != ''">
                and REQUEST_NO=#{params.requestNo}
            </if>
            <if test="params.mnc != null and params.mnc != ''">
                and MNC=#{params.mnc}
            </if>
            <if test="params.lac != null and params.lac != ''">
                and LAC=#{params.lac}
            </if>
            <if test="params.cell != null and params.cell != ''">
                and CELL=#{params.cell}
            </if>
            <if test="params.date_start !=null and params.date_start !=''">
                and CREATE_TIME
                <![CDATA[ >= ]]>
                to_date(#{params.date_start},'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="params.date_end !=null and params.date_end !=''">
                and CREATE_TIME
                <![CDATA[ <= ]]>
                to_date(#{params.date_end},'yyyy-MM-dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC Nulls Last
    </select>

    <select id="findById" resultType="com.pay.tp.core.entity.position.Cell">
        select ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, MNC,
        LAC, CELL, RESULT_CODE, REASON, D_LAC, D_CELL, D_LNG, D_LAT, O_LNG, O_LAT,
        D_MCC, D_MNC, "PRECISION", ADDRESS, ad_code
        from UBADMA.POSI_CELL where ID = #{id}
    </select>
</mapper>