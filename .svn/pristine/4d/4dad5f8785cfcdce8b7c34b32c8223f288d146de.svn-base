package com.pay.tp.core.entity.auth;

import com.pay.tp.core.entity.BaseEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 创建时间：2018年12月19日 下午11:22:21
 * @ClassName
 * @Description
 */
public class BankcardAuth extends BaseEntity {
	private String brand;

	/** 机构编号 */
	private String insId;

	/** 操作员编号 */
	private String operId;

	/** 卡号 */
	private String cardNo;

	/** 交易请求流水号 */
	private String cooperSerialNo;

	/** 姓名 */
	private String name;

	/** 身份证 */
	private String cidNo;

	/** 手机号码 */
	private String mobile;

	/** 响应码 */
	private String rspCod;

	/** 相应描述 */
	private String rspMsg;

	/** 运行类型 */
	private String execType;

	/** 验证状态 */
	private String validateStatus;

	/** 银行名称 */
	private String bankName;

	/** 查询日期 */
	private Date oriTransDate;

	/** 支付序列号 */
	private String paySerialNo;

	/** 补充说明 */
	private String remark;

	/** 卡类型 */
	private String cardType;
	private String resv;

	/** 认证人 */
	private String customerName;

	/** 通道品牌 */
	private String channelBrand;
	
	/** 通道返回码 */
	private String channelRspCod;

	/** 用户编号 */
	private String ownerNo;


	/**
	 * CUST_INCR  商户入网
	 * UPDATE_CARD 更新卡号
	 * CTK_QUOTA  磁条卡大额认证
	 * */
	private String authType;



	public BankcardAuth() {
		super();
	}

	public BankcardAuth(String requestNo, String sys, String cardNo, String cidNo, String mobile, String name,
			String customerName, String channelBrand,String insId, String ownerNo, String authType) {
		super(requestNo, sys);
		this.cardNo = cardNo;
		this.cidNo = cidNo;
		this.mobile = mobile;
		this.name = name;
		this.customerName = customerName;
		this.channelBrand = channelBrand;
		this.insId = insId;
		this.ownerNo = ownerNo;
		this.authType = authType;
	}

	public String getInsId() {
		return insId;
	}

	public void setInsId(String insId) {
		this.insId = insId;
	}

	public String getOperId() {
		return operId;
	}

	public void setOperId(String operId) {
		this.operId = operId;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public String getCooperSerialNo() {
		return cooperSerialNo;
	}

	public void setCooperSerialNo(String cooperSerialNo) {
		this.cooperSerialNo = cooperSerialNo;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCidNo() {
		return cidNo;
	}

	public void setCidNo(String cidNo) {
		this.cidNo = cidNo;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getRspCod() {
		return rspCod;
	}

	public void setRspCod(String rspCod) {
		this.rspCod = rspCod;
	}

	public String getRspMsg() {
		return rspMsg;
	}

	public void setRspMsg(String rspMsg) {
		this.rspMsg = rspMsg;
	}

	public String getExecType() {
		return execType;
	}

	public void setExecType(String execType) {
		this.execType = execType;
	}

	public String getValidateStatus() {
		return validateStatus;
	}

	public void setValidateStatus(String validateStatus) {
		this.validateStatus = validateStatus;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public Date getOriTransDate() {
		return oriTransDate;
	}

	public void setOriTransDate(Date oriTransDate) {
		this.oriTransDate = oriTransDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public String getResv() {
		return resv;
	}

	public void setResv(String resv) {
		this.resv = resv;
	}

	public String getPaySerialNo() {
		return paySerialNo;
	}

	public void setPaySerialNo(String paySerialNo) {
		this.paySerialNo = paySerialNo;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getChannelBrand() {
		return channelBrand;
	}

	public void setChannelBrand(String channelBrand) {
		this.channelBrand = channelBrand;
	}

	public String getChannelRspCod() {
		return channelRspCod;
	}

	public void setChannelRspCod(String channelRspCod) {
		this.channelRspCod = channelRspCod;
	}

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getOwnerNo() {
		return ownerNo;
	}

	public void setOwnerNo(String ownerNo) {
		this.ownerNo = ownerNo;
	}

	public String getAuthType() {
		return authType;
	}

	public void setAuthType(String authType) {
		this.authType = authType;
	}


	@Override
	public String toString() {
		return "BankcardAuth{" +
				"brand='" + brand + '\'' +
				", insId='" + insId + '\'' +
				", operId='" + operId + '\'' +
				", cardNo='" + cardNo + '\'' +
				", cooperSerialNo='" + cooperSerialNo + '\'' +
				", name='" + name + '\'' +
				", cidNo='" + cidNo + '\'' +
				", mobile='" + mobile + '\'' +
				", rspCod='" + rspCod + '\'' +
				", rspMsg='" + rspMsg + '\'' +
				", execType='" + execType + '\'' +
				", validateStatus='" + validateStatus + '\'' +
				", bankName='" + bankName + '\'' +
				", oriTransDate=" + oriTransDate +
				", paySerialNo='" + paySerialNo + '\'' +
				", remark='" + remark + '\'' +
				", cardType='" + cardType + '\'' +
				", resv='" + resv + '\'' +
				", customerName='" + customerName + '\'' +
				", channelBrand='" + channelBrand + '\'' +
				", channelRspCod='" + channelRspCod + '\'' +
				", ownerNo='" + ownerNo + '\'' +
				", authType='" + authType + '\'' +
				'}'  + super.toString();
	}
}
