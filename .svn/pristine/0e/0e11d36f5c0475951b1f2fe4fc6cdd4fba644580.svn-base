package com.pay.tp.core.biz;

import java.util.Map;

import com.pay.tp.core.beans.auth.BankCardAuthParam;

/**
 * @Description: 银行卡鉴权业务
 * @see: BankCardAuthBiz 此处填写需要参考的类
 * @version Nov 21, 2019 5:50:58 PM 
 * <AUTHOR>
 */
public interface BankCardAuthBiz {

	/**
	 * @Description  鉴权认证
	 * @param param
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
	public Map<String, Object> auth(BankCardAuthParam param) throws Exception;

}
