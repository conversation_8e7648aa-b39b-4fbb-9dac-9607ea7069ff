package com.pay.tp.core.beans.auth;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.pay.tp.core.enums.BusinessCode;
import com.pay.tp.core.enums.ChannelStatus;

/**
 * <AUTHOR>
 *
 */
public class TpAuthChannelParam implements java.io.Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 通道编号
     */
    @NotBlank
    private String channelNo;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 业务编码（银行卡认证，企业认证）
     */
    @NotNull
    private BusinessCode businessCode;

    /**
     * 优先级
     */
    private int priority;

    /**
     * 状态
     */
    @NotNull
    private ChannelStatus status;

    /**
     * 备注
     */
    private String remark;

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public BusinessCode getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(BusinessCode businessCode) {
        this.businessCode = businessCode;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public ChannelStatus getStatus() {
        return status;
    }

    public void setStatus(ChannelStatus status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TpAuthChannelParam{" +
                "channelNo='" + channelNo + '\'' +
                ", channelName='" + channelName + '\'' +
                ", businessCode=" + businessCode +
                ", priority=" + priority +
                ", status=" + status +
                ", remark='" + remark + '\'' +
                '}';
    }
}
