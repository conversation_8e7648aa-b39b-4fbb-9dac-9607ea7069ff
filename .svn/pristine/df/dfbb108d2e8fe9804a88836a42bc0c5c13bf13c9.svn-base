package com.pay.tp.core.beans.sms;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.beans.BeanUtils;

import com.pay.tp.core.entity.msgmanage.MsgChannel;
import com.pay.tp.core.enums.Status;

import lombok.Data;


/**
 * 消息通道DTO
 *
 * <AUTHOR>
 */
@Data
public class SmsMsgChannelDto {
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 通道编号
     */
    @NotNull
    private String code;

    /**
     * 通道类型
     */
    private String type;

    /**
     * 通道名称
     */
    @NotNull
    private String name;

    /**
     * 状态
     */
    @NotNull
    private String status;

    /**
     * 权重
     */
    private Long weight;

    /**
     * 通道描述
     */
    private String desc;

    /**
     * 通道配置
     */
    private List<SmsMsgChannelConfigDto> configList;

    public SmsMsgChannelDto convertFor(MsgChannel channel) {
        SmsMsgChannelDto dto = new SmsMsgChannelDto();
        BeanUtils.copyProperties(channel, dto);
        dto.setStatus(channel.getStatus().name());
        return dto;
    }

    public MsgChannel convertToSmsMsgChannel(SmsMsgChannelDto channelDto) {
        MsgChannel channel = new MsgChannel();
        BeanUtils.copyProperties(channelDto, channel);
        channel.setStatus(Status.valueOf(channelDto.getStatus()));
        return channel;
    }
}
