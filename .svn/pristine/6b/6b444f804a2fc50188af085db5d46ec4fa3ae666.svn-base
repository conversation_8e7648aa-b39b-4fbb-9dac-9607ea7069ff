package com.pay.tp.core.remote.auth.zhangxun;

public class Constants {


	public static final String verifyType3 = "0030";
	public static final String verifyType4 = "0040";
	public static final String code = "code";
	public static final String message = "message";
	public static final String tran_amt = "tran_amt";

	public static final String charset = "UTF-8";

	public static final String verify_type = "verify_type";
	public static final String out_trade_no = "out_trade_no";
	public static final String tran_time = "tran_time";
	public static final String acct_no = "acct_no";
	public static final String name = "name";
	public static final String cert_no = "cert_no";
	public static final String phone = "phone";
	public static final String sign = "sign";

	public static final String customerCode = "customer_code";


	public static final String certType = "cert_type";

	public static final String SIGN_ALGORITHMS = "SHA1WithRSA";
	public static final String SIGN_TYPE_RSA = "RSA";
	public static final String Algorithm = "DESede"; // 定义 加密算法,可用


}
