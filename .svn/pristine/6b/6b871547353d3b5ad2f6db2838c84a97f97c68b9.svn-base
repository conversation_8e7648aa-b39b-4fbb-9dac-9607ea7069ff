package com.pay.tp.core.remote.position;

import java.io.IOException;
import java.util.Map;
import java.util.Properties;

import javax.annotation.PostConstruct;

import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.tp.core.beans.position.RegeoResponse;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.client
 * @Description: TODO
 * @date Date : 2018年12月25日 15:00
 */
@Component
public class GaodeClient {
    private final Logger logger = LoggerFactory.getLogger(GaodeClient.class);

    @Value("${custom.gaode.geocode.regeo.url:}")
    private String url;

    @Value("${custom.gaode.key:}")
    private String key;

    @Value("${custom.gaode.connection.timeout:1000}")
    private int connectionTimeout = 1000;
    @Value("${custom.gaode.socket.timeout:1000}")
    private int socketTimeout = 1000;
    @Value("${proxy.squid.host:}")
    private String proxyHost;
    @Value("${proxy.squid.port:0}")
    private int proxyPort;

    private Properties properties;

    @PostConstruct
    public void init() throws IOException {
        properties = PropertiesLoaderUtils.loadAllProperties("country.properties");
    }


    public RegeoResponse req(String requestNo, String longitude, String latitude) throws IOException {
    	logger.info("method = request, requestNo = {}, longitude = {}, latitude = {}", requestNo, longitude, latitude);

        CloseableHttpClient client = null;
        try{
        	client = HttpClientBuilder.create().build();
            RequestConfig.Builder builder = RequestConfig.custom().setConnectTimeout(connectionTimeout).setSocketTimeout(socketTimeout);

            if(!org.apache.commons.lang.StringUtils.isEmpty(proxyHost)){
            	builder.setProxy(new HttpHost(proxyHost, proxyPort)).build();
            }

            HttpGet get = new HttpGet(url + "?output=JSON&location=" + longitude + "," + latitude + "&key=" + key);
            get.setConfig(builder.build());

            HttpResponse response = response = client.execute(get);
            int statusCode = response.getStatusLine().getStatusCode();

            if(HttpStatus.SC_OK == statusCode){
            	String content = EntityUtils.toString(response.getEntity());
                logger.info("method = request, requestNo = {}, content = {}", requestNo, content);
                ObjectMapper objectMapper = new ObjectMapper();
                Map map = objectMapper.readValue(content, Map.class);

                if("1".equals(map.get("status"))){
                	RegeoResponse resp = new RegeoResponse();
                    Map regeocode = (Map)map.get("regeocode");
                    resp.setFormattedAddress(regeocode.get("formatted_address").toString());
                    Map addressComponent = (Map)regeocode.get("addressComponent");
                    String adCode = addressComponent.get("adcode").toString();
                    resp.setCountryCode(properties.getProperty("" + addressComponent.get("country"),""));
                    resp.setProvince(addressComponent.get("province").toString());
                    if(resp.getProvince().contains("北京") || resp.getProvince().contains("上海")
                    		|| resp.getProvince().contains("天津") || resp.getProvince().contains("重庆")){
                    	resp.setCity(resp.getProvince());
                    }else{
                        resp.setCity(addressComponent.get("city").toString());
                    }

                    resp.setAdCode(adCode);
                    if(adCode.length() >= 2){
                    	resp.setProvinceCode(adCode.substring(0,2));
                    }
                    if(adCode.length() >= 4){
	                    resp.setCityCode(adCode.substring(0,4));
	                    //resp.setCityCode(resp.getCityCode().substring(0,resp.getCityCode().length()-1)+"0");
                    }
                    return resp;
                }else{
                	throw new RuntimeException(map.get("info").toString());
                }

            }else{
                logger.error("method = request, requestNo = {}, statusCode = {}", requestNo, statusCode);
                throw new RuntimeException("三方接口访问失败");
            }
        }finally {
            if(client != null){
                client.close();
            }
        }
    }
    
    
}
