package com.pay.tp.core.beans.position;

import org.hibernate.validator.constraints.NotBlank;


/**经纬度解析地址
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.position.core.beans
 * @Description: TODO
 * @date Date : 2018年12月25日 16:18
 */
public class CoordinateParam {

    /**
     * 请求号
     */
    @NotBlank
    private String requestNo;

    /**
     * 经度
     */
    @NotBlank
    private String longitude;

    /**
     * 纬度
     */
    @NotBlank
    private String latitude;


    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    @Override
    public String toString() {
        return "CoordinateParam{" +
                "requestNo='" + requestNo + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                '}';
    }
    
}
