package com.pay.tp.auth.service;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.pay.frame.common.base.util.crypto.SM4Utils;
import com.pay.tp.BaseTest;
import com.pay.tp.core.beans.auth.BankCardAuthParam;
import com.pay.tp.core.entity.auth.BankcardAuth;
import com.pay.tp.core.remote.auth.BankCardAuthClient;
import com.pay.tp.core.service.auth.BankCardAuthService;

public class BankCardAuthTest extends BaseTest{
	
	@Autowired
	private BankCardAuthService bankCardAuthService;
	@Autowired
	private BankCardAuthClient ldysAuthClient;
	@Autowired
	private BankCardAuthClient jxAuthClient;
	@Autowired
	private BankCardAuthClient zxAuthClient;
	
	@Autowired
	private BankCardAuthClient xlBjldAuthClient;
	@Autowired
	private BankCardAuthClient xlJilongAuthClient;
	
//	DefaultJXBankCardAuthClient
	
	
//	@Test
//	public void auth() throws Exception {
//		BankCardAuthParam params = new BankCardAuthParam();
//		params.setRequestNo(RandomUtils.getFlowNo()); 	// 请求号
//		params.setCardNo("6212260200043832852"); 		// 卡号
//		params.setCidNo("232723199002061113"); 		// 身份证号
//		params.setName("许秦稷"); 			// 账户名称
//		params.setCustomerName("许秦稷"); 	//  鉴权人
//		params.setMobile(null); 		// 预留手机号
//		Map<String, Object> results = bankCardAuthBiz.auth(params);	
//		System.out.println(results);
//	}
	

	
	@Test
	public void xl2() throws Exception {
		BankCardAuthParam param=new BankCardAuthParam();
		param.setCardNo("****************");
		param.setCidNo("******************");
		param.setName("43稷66");
		long start = System.currentTimeMillis();
		bankCardAuthService.query(param);
		System.out.println(System.currentTimeMillis()-start);
		System.out.println(System.currentTimeMillis()-start);

	}
	
//	 @Value("${crypto.sensitive.sm4}")
//	    private String sm4Key;
//	@Test
//	public void xl3() throws Exception {
//		BankCardAuthParam param=new BankCardAuthParam();
//		param.setCardNo("360200043855552852");
//		param.setCidNo("3723195559002061113");
//		param.setName("3许秦稷");
//		bankCardAuthService.query1(param);
//		System.out.println("dddddddddd");
//	}
//	
//	@Test
//	public void xl4() throws Exception {
//		BankCardAuthParam param=new BankCardAuthParam();
//		param.setCardNo(SM4Utils.encrypt(sm4Key,"260200022243832852"));
//		param.setCidNo(SM4Utils.encrypt(sm4Key,"22723199002333061113"));
//		param.setName(SM4Utils.encrypt(sm4Key,"2许秦稷"));
//		bankCardAuthService.queryAll(param);
//		System.out.println("dddddddddd");
//	}
//	@Test
//	public void zx() throws Exception {
//		BankCardRes request = zxAuthClient.request(RandomUtils.getFlowNo(), "6212260200043832852", "许秦稷", "232723199002061113", null, "许秦稷");
//		System.out.println(request.toString());
//	}
//	@Test
//	public void jx() throws Exception {
//		BankCardRes request = jxAuthClient.request(RandomUtils.getFlowNo(), "6212260200043832852", "许秦稷", "232723199002061113", null, "许秦稷");
//		System.out.println(request.toString());
//	}

}
