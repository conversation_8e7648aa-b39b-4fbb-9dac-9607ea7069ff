package com.pay.tp.core.beans.auth;

import lombok.Data;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;

/**银行卡验证参数
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Package com.pay.tp.auth.configuration
 * @Description: 银行卡验证
 * @date Date : 2018年12月18日 14:33
 */
@Data
public class BankCardAuthParam implements java.io.Serializable{
	private static final long serialVersionUID = 1L;
	private String brand;
	/**
	 * 机构编号
	 */
	private String insId;
    /**
     * 请求号
     */
    @NotBlank
    private String requestNo;
    /**
     * 卡号
     */
    @NotBlank
    @Pattern(regexp = "[0-9]{1,50}")
    private String cardNo;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    //@Pattern(regexp = "[0-9]{11}")
    private String mobile;
    /**
     * 身份证号
     */
    @Pattern(regexp = "[0-9A-Za-z]{15,18}")
    private String cidNo;
    /**
     * 商户名称
     */
    @NotBlank
    @Length(max = 100)
    private String customerName;

    /**
     * 鉴权用户编号
     */
    private String ownerNo;

    /**鉴权来源
     * SELF_CREDIT_CARD
     * CUST_INCR,
     * UPDATE_CARD,
     * CTK_QUOTA
     * */
    private String authType;


}
