package com.pay.tp.core.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.tp.core.entity.WxMsgTemp;
import com.pay.tp.core.mapper.WxMsgTempMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2023/3/29
 * @apiNote
 */
@Service
public class WxMsgTempService {

    @Autowired
    private WxMsgTempMapper wxMsgTempMapper;



    public WxMsgTemp findByTempType(String tempType, String brand) {
        return wxMsgTempMapper.findByTempType(tempType, brand);
    }


    public PageInfo<WxMsgTemp> findByPageAll(int pageNum, int pageSize, Map<String, Object> queryParams) {
        PageHelper.startPage(pageNum, pageSize, true);        
        List<WxMsgTemp> list = wxMsgTempMapper.findByPageAll(queryParams);
        PageInfo<WxMsgTemp> page = new PageInfo<>(list);
        return page;
    }


    public WxMsgTemp findById(Long id) {
        return wxMsgTempMapper.selectByPrimaryKey(id);
    }

    @Transactional
    public void updateById(WxMsgTemp wxMsgTemp) {
        wxMsgTempMapper.updateByPrimaryKey(wxMsgTemp);
    }

}
