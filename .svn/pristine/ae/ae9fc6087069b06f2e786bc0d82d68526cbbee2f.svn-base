package com.pay.tp.core.remote.auth.zhangxun;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.pay.tp.core.beans.auth.BankCardRes;

/**
 * <AUTHOR> zhang<PERSON>an
 * @Package com.pay.tp.auth.remote.xinlian
 * @Description: TODO
 * @date Date : 2019年01月16日 14:44
 */
//@Service
//@Profile({"dev", "qa"})
public class MockZXBankCardAuthClient {

    private final Logger logger = LoggerFactory.getLogger(MockZXBankCardAuthClient.class);

//    认证结果：
////    01-认证一致  （收费）
////    02-认证不一致（收费）
////    03-认证不确定（不收费）
////    04-认证失败  （不收费）
////    05-认证受限（收费）

    /*@Override
    public Map<String, Object> parse(BankcardAuth auth) {

        Map<String, Object> map = new HashMap<>();

        if ("000000".equals(auth.getRspCod())) {
            String validateStatus = auth.getValidateStatus();
            if ("01".equals(validateStatus) || "02".equals(validateStatus) || "03".equals(validateStatus)) {
                map.put("result", validateStatus);
                map.put("msg", auth.getRemark());
            } else {
                map.put("result", "03");
                map.put("msg", auth.getRemark());
            }
        } else if ("000097".equals(auth.getRspCod())) {
            map.put("result", "02");
            map.put("msg", auth.getRemark());
        } else {
            map.put("result", "03");
            map.put("msg", auth.getRemark());
        }

        return map;
    }*/

    //@Override
    public BankCardRes request(String cooperSerialNo, String cardNo, String name, String cidNo, String mobile, String customerName)
            throws Exception {
        logger.info("method = request, cardNo = {}, name = {}, cidNo = {}, mobile = {}, cooperSerialNo = {}", cardNo, name, cidNo, mobile, cooperSerialNo);
        long random = System.currentTimeMillis() % 3;
        logger.info("####ZX######" + random + "");

        BankCardRes bankCardRes = new BankCardRes();
        if (random == 0) {
        	bankCardRes.setRspCod("000000");
        	bankCardRes.setValidateStatus("01");
        	bankCardRes.setRemark("测试一致");
            return bankCardRes;
        } else if (random == 1) {
        	bankCardRes.setRspCod("000097");
        	bankCardRes.setRemark("测试不一致");
            return bankCardRes;
        } else {
        	bankCardRes.setRspCod("000098");
        	bankCardRes.setRemark("测试不确定");
            return bankCardRes;
        }

    }
}
