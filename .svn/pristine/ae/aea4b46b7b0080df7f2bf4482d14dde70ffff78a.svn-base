package com.pay.tp.core.remote;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.CommonConstants;
import com.pay.tp.core.beans.tencent.WillBodyBean;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @date 2022年07月20日 10:50
 */
@FeignClient(value = CommonConstants.CUST_EUREKA_SERVER_INSTANCE_CORE)
public interface WillBodyClient {

    /**
     * 更新
     * @param willBody
     * @return
     */
    @RequestMapping(value = CommonConstants.CUST_APPLICATION_NAME_CORE + "/willBody/saveOrUpdate", method = RequestMethod.POST)
    public ResultsBean<String> saveOrUpdate(@RequestBody WillBodyBean willBody);

    @RequestMapping(value = CommonConstants.CUST_APPLICATION_NAME_CORE + "/willBody/unSaveOrUpdate", method = RequestMethod.POST)
    public ResultsBean<String> unSaveOrUpdate(@RequestBody WillBodyBean willBody);

}
