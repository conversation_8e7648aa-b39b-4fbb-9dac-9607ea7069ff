package com.pay.tp.position;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.pay.tp.BaseTest;
import com.pay.tp.core.beans.position.CoordinateBean;
import com.pay.tp.core.beans.position.CoordinateParam;
import com.pay.tp.core.controller.position.CoordinateController;

public class CoordinateControllerTest extends BaseTest {

	@Autowired
	private CoordinateController coordinateController;
	
	
	@Test
	public void test() throws Exception {
		CoordinateParam param = new CoordinateParam();
		param.setLatitude("39.916527");
		param.setLongitude("116.397128");
		param.setRequestNo("2");
		CoordinateBean cell = coordinateController.coordinate(param);
		System.out.println(cell);
	}
	
}
