package com.pay.tp.core.remote.auth;

import com.pay.tp.core.beans.auth.BankCardRes;

/**
 * @Description: 银行卡鉴权请求
 * @see: BankCardAuthClient 此处填写需要参考的类
 * @version Nov 20, 2019 1:23:22 PM
 * <AUTHOR>
 */
public interface BankCardAuthClient {

	/**
	 * @Description 认证请求
	 * @param outTradeNo   请求号
	 * @param cardNo       卡号
	 * @param name         名称
	 * @param cidNo        身份证号
	 * @param mobile       手机号
	 * @param customerName 认证人
	 * @return
	 * @throws Exception
	 * @see 需要参考的类或方法
	 */
	public BankCardRes request(String outTradeNo, String cardNo, String name, String cidNo, String mobile,
							   String customerName, String brand) throws Exception;
}
