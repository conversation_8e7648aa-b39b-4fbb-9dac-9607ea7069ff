package com.pay.tp.core.beans;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;

/**
 *
 */
@Data
public class BaiDuOcrBean {

    /** 识别类型 */
    @NotBlank(message = "识别图片类型不能为空")
    @Pattern(message = "识别图片类型只支持 BANK_CARD, ID_CAED_RS, ID_CAED_WS, FACE_DETECT", regexp = "BANK_CARD|ID_CAED_RS|ID_CAED_WS|FACE_DETECT|PERSON_VERIFY")
    private String imageType;

    /** base64 */
    @NotBlank(message = "图片不能为空")
    private String image;
    private String userNo;
    private String brand;

}
