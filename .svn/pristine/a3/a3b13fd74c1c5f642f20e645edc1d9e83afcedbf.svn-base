//package com.pay.tp.core.job;
//
//import com.pay.frame.common.base.enums.Brand;
//import com.pay.tp.core.biz.impl.WeChatBiz;
//import com.pay.tp.core.configuration.JobRegisterCondition;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Conditional;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//
///**
// *
// */
//@Slf4j
//@Component
//@Conditional({JobRegisterCondition.class})
//public class SmsMsgWxCheckHealthTask {
//
//    /**
//     * 钉钉通道业务处理
//     */
//    @Autowired
//    private WeChatBiz weChatBiz;
//
//
//
//    @Scheduled(cron = "0 0/30 0/1 * * ?")
//    public void wxHealthCheck() {
//        try {
////            List<MsgChannel> channels = weChatBiz.findWxAllChannel();
//            Arrays.asList(Brand.values()).forEach(brand ->{
//                weChatBiz.loadAccessToken(brand.name());
//            });
//        } catch (Exception e) {
//            log.error("wx_push channel health check error ", e);
//        }
//    }
//
//
//}
