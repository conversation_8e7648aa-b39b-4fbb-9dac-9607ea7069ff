package com.pay.tp.core.service.msgmanage;

import com.pay.tp.core.entity.msgmanage.MsgChannelConfig;
import com.pay.tp.core.exception.SmsDingDingException;
import com.pay.tp.core.mapper.msgmanage.MsgChannelConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息通道配置服务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MsgChannelConfigService {

    @Autowired
    private MsgChannelConfigMapper msgChannelConfigMapper;

    @Autowired
    private MsgChannelConfigLogService msgChannelConfigLogService;


    public List<MsgChannelConfig> findByChannelCode(String channelNo) {
        return msgChannelConfigMapper.findByChannelCode(channelNo);
    }

    public Map<String, String> findFlatMapConfigByChannelCode(String channelNo) {
        List<MsgChannelConfig> channelConfigs = msgChannelConfigMapper.findByChannelCode(channelNo);
        // list -> map(k, list) -> map(k, v)
        Map<String, String> cfgMap = channelConfigs
                .stream()
                .collect(Collectors.groupingBy(c -> c.getCfgParam(),
                        Collectors.mapping(c -> c.getCfgValue(), Collectors.joining(","))
                ));
        return cfgMap;
    }


    public MsgChannelConfig findByChannelCodeAndCfgParam(String channelNo, String param) {
        return msgChannelConfigMapper.findByChannelCodeAndCfgParam(channelNo, param);
    }


    @Transactional(rollbackFor = Exception.class)
    public void save(MsgChannelConfig config) {
        MsgChannelConfig existsConfig = msgChannelConfigMapper
                .findByChannelCodeAndCfgParam(config.getChannelCode(), config.getCfgParam());
        if (existsConfig == null) {
            config.setCreateTime(new Date()).setOptimistic(0L);
            int i = msgChannelConfigMapper.insert(config);
            msgChannelConfigLogService.save(config, config);
        } else {
            config.setId(existsConfig.getId())
                    .setOptimistic(existsConfig.getOptimistic())
                    .setUpdateTime(new Date());
            int i = msgChannelConfigMapper.updateByPrimaryKey(config);
            if (i != 1) {
                log.error("sms msg channel config update error {}", config);
                throw new SmsDingDingException("99",
                        "sms msg channel config update error, optimistic version error, id: " + config.getId());
            }
            msgChannelConfigLogService.save(existsConfig, config);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<MsgChannelConfig> configs) {
        if (!CollectionUtils.isEmpty(configs)) {
            configs.forEach(this::save);
        }
    }

    public List<MsgChannelConfig> findByParams(Map<String, Object> params) {
        return msgChannelConfigMapper.findByParams(params);
    }

    public MsgChannelConfig findById(Long id) {
        return msgChannelConfigMapper.selectByPrimaryKey(id);
    }

    public void updateByPrimaryKey(MsgChannelConfig config) {
        MsgChannelConfig before = this.findById(config.getId());

        config.setUpdateTime(new Date());
        int i = msgChannelConfigMapper.updateByPrimaryKey(config);
        if (i != 1) {
            log.error("sms msg channel config update error {}", config);
            throw new SmsDingDingException("99",
                    "sms msg channel config update error, optimistic version error, id: " + config.getId());
        }
        msgChannelConfigLogService.save(before, config);

    }

    public MsgChannelConfig findUnique(MsgChannelConfig config) {
        return msgChannelConfigMapper.findUnique(config);
    }
}
