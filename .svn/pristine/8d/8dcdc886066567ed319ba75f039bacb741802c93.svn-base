<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.tp.core.mapper.position.CoordinateMapper">


    <select id="query" resultType="com.pay.tp.core.entity.position.Coordinate" parameterType="com.pay.tp.core.beans.position.CoordinateParam">
        select 
        	ID, OPTIMISTIC, CREATE_TIME, SYS, REQUEST_NO, FORMATTED_ADDRESS, COUNTRY_CODE, 
        	PROVINCE, PROVINCE_CODE, CITY, CITY_CODE, AD_CODE, LONGITUDE, LATITUDE 
        from UBADMA.POSI_COORDINATE where REQUEST_NO = #{requestNo}
    </select>

    <insert id="insert" parameterType="com.pay.tp.core.entity.position.Coordinate">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT UBADMA.SEQ_POSI_COORDINATE.nextval FROM dual
        </selectKey>
        insert into UBADMA.POSI_COORDINATE (ID,OPTIMISTIC,CREATE_TIME,LONGITUDE, LATITUDE, REQUEST_NO)
        values (#{id,jdbcType=DECIMAL},0,sysdate,#{longitude},#{latitude},#{requestNo})
    </insert>

    <update id="update" parameterType="java.util.Map">
		    update UBADMA.POSI_COORDINATE
            set
              FORMATTED_ADDRESS = #{formattedAddress},
              COUNTRY_CODE = #{countryCode},
              PROVINCE = #{province},
              PROVINCE_CODE = #{provinceCode},
              CITY = #{city},
              CITY_CODE = #{cityCode},
              AD_CODE = #{adCode}
		    where ID = #{id}
  		</update>

</mapper>