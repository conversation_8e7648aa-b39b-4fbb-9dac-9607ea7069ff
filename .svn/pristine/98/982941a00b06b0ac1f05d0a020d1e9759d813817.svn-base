//package com.pay.tp.core.beans.fyt;
//
//import lombok.Data;
//import org.hibernate.validator.constraints.NotBlank;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @description:
// * @date 2023/10/12 14:47
// */
//@Data
//public class FytRegisterReqBean {
//
//    @NotBlank(message = "手机号必填")
//    private String phone;
//
//    /**
//     * 用户名
//     */
//    private String name;
//
//    /**
//     * 代理商标识
//     */
//    private String agency = "PLUS";
//
//    /**
//     * 用户头像链接
//     */
//    private String avatar;
//}
