package com.pay.tp.core.service.auth;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.auth.BankCardRes;
import com.pay.tp.core.remote.auth.zhangxun.AmiCloudResp;

/**
 * @Description: 掌讯鉴权解析
 * @see: AmiCloudService 此处填写需要参考的类
 * @version Nov 20, 2019 3:08:06 PM
 * <AUTHOR>
 */
@Service
public class AmiCloudService {
	private final Logger logger = LoggerFactory.getLogger(AmiCloudService.class);

	private static Map<String, String> codeMap = new HashMap<>();
	static {
		codeMap.put("90034", "卡片已被冻结");
		codeMap.put("90010", "该卡号当日连续3次认证不通过被限制校验，次日恢复");
		codeMap.put("90020", "该卡号当日请求次数超限，次日恢复");
		codeMap.put("90030", "该证件号当日请求次数超限，次日恢复");
		codeMap.put("90040", "该手机号当日请求次数超限，次日恢复");
		codeMap.put("90050", "该姓名+证件号当日请求次数超限，次日恢复");
		codeMap.put("90011", "此卡存在疑似伪造身份证的嫌疑");
		codeMap.put("90012", "此人存在疑似伪造身份证的嫌疑");
		codeMap.put("90013", "年龄不符合要求，存在风险");
		codeMap.put("90022", "身份证号或手机号格式错误");
		codeMap.put("90021", "不支持此卡的信息校验，请持卡人咨询银行");
		codeMap.put("90035", "卡片未激活、或卡已被户主挂失");
		codeMap.put("90006", "卡号不存在、卡已注销、卡已过有效期");
		codeMap.put("90099", "发卡方响应超时");
		codeMap.put("90032", "该银行卡无法校验");
		codeMap.put("90067", "银行卡在其他终端输入密码错误次数太多");
		codeMap.put("90049", "该银行卡需开通银联无卡支付后才能校验");
		codeMap.put("90027", "请求的卡非银联卡、卡号前6位输入错误");
		codeMap.put("90098", "系统其他错误");
		codeMap.put("90009", "短时间内请求次数过多");
	}

	public BankCardRes convertZX(AmiCloudResp resp) {
		BankCardRes zx = new BankCardRes();
		zx.setRspCod("000000");
		zx.setChannelRspCod(resp.getCode());

		String msg = resp.getMessage();
		if ("1008".equals(resp.getCode())) {
			String message = resp.getMessage();
			try {
				String str = message.substring(message.indexOf("[") + 1, message.indexOf("]"));
				msg = codeMap.get(str);
			} catch (Exception e) {
				logger.info("");
			}
		}
		zx.setRemark(msg);

		switch (resp.getCode()) {
		case "0000":
			zx.setValidateStatus("01");	// 认证一致 （收费）
			break;
		case "0001":
			zx.setValidateStatus("02");	// 认证不一致（收费） 
			break;
		case "0002":
			zx.setValidateStatus("05");	// 05-认证受限（收费）
			break;
		default:
			zx.setValidateStatus("04");	// 认证失败 （不收费） 
			break;
		}
		return zx;
	}
	
	
	public BankCardRes convertLdys(ResultsBean<String> res) {
		BankCardRes bcRes = new BankCardRes();
		bcRes.setRspCod("000000");
		bcRes.setChannelRspCod(res.getCode());
		bcRes.setRemark(res.getMessage());
		if(res.success()) {
			bcRes.setValidateStatus("01");	// 认证一致
		}else {
			bcRes.setValidateStatus("04");	// 认证失败 
		}
		return bcRes;
	}
}
