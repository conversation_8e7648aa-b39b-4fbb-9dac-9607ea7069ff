package com.pay.tp.core.controller.position;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.tp.core.beans.position.CellBean;
import com.pay.tp.core.beans.position.CellParam;
import com.pay.tp.core.entity.position.Cell;
import com.pay.tp.core.remote.position.JuheClient;
import com.pay.tp.core.service.position.CellService;


/**基站定位Api
 * <AUTHOR> zhangjian
 * @Package com.pay.position.core.controller
 * @Description: TODO
 * @date Date : 2018年12月24日 14:28
 */
@RestController
@RequestMapping("cell")
public class CellController {
    private final Logger logger = LoggerFactory.getLogger(CellController.class);

    @Autowired
    private CellService cellService;
    
    @Autowired
    private JuheClient juheClient;

    //基站定位
    @RequestMapping(value = "", method = RequestMethod.POST)
    public CellBean cell(@Validated @RequestBody CellParam param) throws Exception {

        logger.info("method = cell, param = {}", param);
        //要求幂等
        Cell cell = cellService.query(param);
        if (cell.getReason() != null) {
            return cell.getResult();
        } else {
            Map<String,Object> res = juheClient.request(cell.getRequestNo(),cell.getMnc(), cell.getLac(), cell.getCell());

            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(res);
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            Cell temp = objectMapper.readValue(json, Cell.class);

            BeanUtils.copyProperties(temp, cell, "id","optimistic","createTime","sys","requestNo","mnc","lac","cell");
            cellService.update(cell);

            if(StringUtils.isEmpty(cell.getdCell())){
                //throw new NoValidDataException(cell.getReason());
            }

            return cell.getResult();
        }
    }
    
    
    @RequestMapping(value = "updateByCell", method = RequestMethod.POST)
    public ResultsBean<String> updateByCell(@Validated @RequestBody Cell cell) throws Exception {

        logger.info("method = updateByCell, param = {}", cell);
        try {
        	cellService.updateByCell(cell);
    		return ResultsBean.SUCCESS("");
    	} catch (Exception e) {
    		logger.error("修改失败={}", e);
    		return ResultsBean.FAIL("");
    	}
    }
    
    
    @RequestMapping(value = "queryByLatLng", method = RequestMethod.POST)
    public ResultsBean<List<Map<String, String>>> queryByLatLng(@RequestParam("latLng") String latLng) throws Exception {
        logger.info("method = queryByLatLng, param = {}", latLng);
        return cellService.queryByLatLng(latLng);
    }
    
    
}
