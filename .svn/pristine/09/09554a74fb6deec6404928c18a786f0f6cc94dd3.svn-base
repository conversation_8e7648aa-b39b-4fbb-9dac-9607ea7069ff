package com.pay.tp.core.remote.auth.zhangxun;

/**
 * @Description: 掌讯鉴权返回
 * @see: AmiCloudResp 此处填写需要参考的类
 * @version Nov 20, 2019 1:39:19 PM 
 * <AUTHOR>
 */
public class AmiCloudResp {

    private String code;			// 应答码
    private String message;			// 应答描述
    private String out_trade_no;	// 交易流水号
    private String tran_amt;		// 交易金额
    private String tran_time;		// 交易时间

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOut_trade_no() {
        return out_trade_no;
    }

    public void setOut_trade_no(String out_trade_no) {
        this.out_trade_no = out_trade_no;
    }

    public String getTran_amt() {
        return tran_amt;
    }

    public void setTran_amt(String tran_amt) {
        this.tran_amt = tran_amt;
    }

    public String getTran_time() {
        return tran_time;
    }

    public void setTran_time(String tran_time) {
        this.tran_time = tran_time;
    }
}
