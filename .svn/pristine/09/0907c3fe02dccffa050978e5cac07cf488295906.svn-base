package com.pay.tp.core.service.msgmanage;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.tp.core.entity.msgmanage.MsgUser;
import com.pay.tp.core.exception.SmsOptimisticException;
import com.pay.tp.core.mapper.msgmanage.MsgUserMapper;

/**
 * 消息用户服务层
 *
 * <AUTHOR>
 */
@Service
public class MsgUserService {

    @Autowired
    private MsgUserMapper msgUserMapper;

    public List<MsgUser> findByGroupNo(String channelCode, String groupNo) {
        return msgUserMapper.findByChannelCodeAndGroupNo(channelCode, groupNo);
    }

    public MsgUser findByUserNo(String userNo, String channelCode) {
        return msgUserMapper.findByUserNo(userNo, channelCode);
    }

    public void updateByPrimaryKey(MsgUser user) {
        user.setUpdateTime(new Date());
        int i = msgUserMapper.updateByPrimaryKey(user);
        if (i != 1) {
            throw new SmsOptimisticException("99", "更新用户数据失败，已有变更先于此次变更");
        }
    }

    public void addUser(MsgUser user) {
        user.setCreateTime(new Date()).setOptimistic(0L);
        user.setUserNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmSS")));
        this.insert(user);
    }

    public void insert(MsgUser user) {
        user.setCreateTime(new Date()).setOptimistic(0L);
        msgUserMapper.insert(user);
    }

    public List<MsgUser> findByParams(Map<String, Object> params) {
        return msgUserMapper.findByParams(params);
    }

    public MsgUser findById(Long id) {
        return msgUserMapper.selectByPrimaryKey(id);
    }
}