package com.pay.tp.core.beans.tencent;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * @date 2022年07月20日 10:51
 */
@Data
public class WillBodyBean {

    /**
     * 用户编号
     */
    private String ownerNo;

    /**
     * 用户角色
     */
    private String ownerRole;

    /**
     * 状态
     */
    private String status;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 路径
     */
    private String url;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String brand;

    private String content1;

    /**
     * 临时url
     */
    private String temporaryVideoUrl;

    /**
     * 临时url
     */
    private String temporarySnapshotBase64;


    @Override
    public String toString() {
        return "WillBodyBean{" +
                "ownerNo='" + ownerNo + '\'' +
                ", ownerRole='" + ownerRole + '\'' +
                ", status='" + status + '\'' +
                ", amount=" + amount +
                ", url='" + url + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", brand='" + brand + '\'' +
                ", content1='" + content1 + '\'' +
                ", temporaryVideoUrl='" + temporaryVideoUrl + '\'' +
                '}';
    }
}

