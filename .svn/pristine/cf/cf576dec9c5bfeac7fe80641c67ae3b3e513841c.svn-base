package com.pay.tp.core.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * WX_MSG_TEMP_CFG
 * <AUTHOR>
@Data
public class WxMsgTempCfg implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 乐观锁
     */
    private Long optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 模板id
     */
    private String tempId;

    /**
     * key
     */
    private String key;

    /**
     * 模板key
     */
    private String tempKey;

    /**
     * 模板颜色
     */
    private String tempColor;

    /**
     * 状态
     */
    private String status;

    /**
     * 品牌
     */
    private String brand;

    private static final long serialVersionUID = 1L;
}