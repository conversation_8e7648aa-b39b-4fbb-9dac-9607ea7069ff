package com.pay.tp.core.controller.position;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pay.tp.core.beans.position.IpBean;
import com.pay.tp.core.beans.position.IpParam;
import com.pay.tp.core.entity.position.Ip;
import com.pay.tp.core.remote.position.AliyunClient;
import com.pay.tp.core.service.position.IpService;


/**IP定位Api
 * <AUTHOR> zhang<PERSON>an
 * @Package com.pay.position.core.controller
 * @Description: TODO
 * @date Date : 2018年12月24日 16:04
 */
//@RestController
//@RequestMapping("ip")
public class IpController {
    private final Logger logger = LoggerFactory.getLogger(IpController.class);

    @Autowired
    private IpService ipService;
   
    @Autowired
    private AliyunClient aliyunClient;

    //ip定位
    @RequestMapping(value = "", method = RequestMethod.POST)
    public IpBean exec(@Validated @RequestBody IpParam param) throws Exception {

        logger.info("method = exec, param = {}", param);
        Map<String,Object> map = new HashMap<>();
        //要求幂等
        Ip ip = ipService.query(param);
        if (!StringUtils.isEmpty(ip.getCountry())) {
            IpBean ipBean =  ip.getResult();
            ipBean.setProvCode(aliyunClient.getShengfenCode(ipBean.getProv()));
            return ipBean;
        } else {
            Map<String,Object> res = aliyunClient.request(ip.getRequestNo(),ip.getIp());

            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(res);
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            Ip temp = objectMapper.readValue(json, Ip.class);

            BeanUtils.copyProperties(temp, ip, "id","optimistic","createTime","sys","ip");
            ip.setBeginIp(res.get("beginip") == null ? null : res.get("beginip")+"");
            ip.setEndIp(res.get("endip") == null ? null : res.get("endip")+"");

            if(StringUtils.isEmpty(ip.getCountry())){
                //throw new NoValidDataException("定位信息不存在");
            }

            ipService.update(ip);
            IpBean ipBean =  ip.getResult();
            ipBean.setProvCode(aliyunClient.getShengfenCode(ipBean.getProv()));
            return ipBean;
        }
    }
    
    
}
