package com.pay.tp.core.entity.position;

import org.springframework.beans.BeanUtils;

import com.pay.tp.core.beans.position.IpBean;
import com.pay.tp.core.entity.BaseEntity;

/**
 * <AUTHOR> z<PERSON><PERSON>an
 * @Package com.pay.position.core.entity
 * @Description: TODO
 * @date Date : 2018年12月25日 17:54
 */
public class Ip extends BaseEntity{

    private String ip;
    private String land;
    private String country;
    private String city;
    private String prov;
    private String dist;
    private String isp;
    private String zipCode;
    private String english;
    private String cc;
    private String longitude;
    private String latitude;
    private String beginIp;
    private String endIp;
    private String area;

    public Ip(String requestNo, String sys, String ip) {
        super(requestNo, sys);
        this.ip = ip;
    }

    public Ip() {
        super();
    }


    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getLand() {
        return land;
    }

    public void setLand(String land) {
        this.land = land;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProv() {
        return prov;
    }

    public void setProv(String prov) {
        this.prov = prov;
    }

    public String getDist() {
        return dist;
    }

    public void setDist(String dist) {
        this.dist = dist;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getEnglish() {
        return english;
    }

    public void setEnglish(String english) {
        this.english = english;
    }

    public String getCc() {
        return cc;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getBeginIp() {
        return beginIp;
    }

    public void setBeginIp(String beginIp) {
        this.beginIp = beginIp;
    }

    public String getEndIp() {
        return endIp;
    }

    public void setEndIp(String endIp) {
        this.endIp = endIp;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    @Override
    public String toString() {
        return "Ip{" +
                "ip='" + ip + '\'' +
                ", land='" + land + '\'' +
                ", country='" + country + '\'' +
                ", city='" + city + '\'' +
                ", prov='" + prov + '\'' +
                ", dist='" + dist + '\'' +
                ", isp='" + isp + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", english='" + english + '\'' +
                ", cc='" + cc + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", beginIp='" + beginIp + '\'' +
                ", endIp='" + endIp + '\'' +
                ", area='" + area + '\'' +
                "} " + super.toString();
    }

    public IpBean getResult() {
        IpBean ipBean = new IpBean();
        BeanUtils.copyProperties(this, ipBean);
        return ipBean;
    }
}
