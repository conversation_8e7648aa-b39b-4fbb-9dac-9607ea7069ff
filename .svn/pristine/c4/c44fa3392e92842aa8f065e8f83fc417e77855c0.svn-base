package com.pay.tp.core.remote.bean;

import com.pay.frame.common.base.enums.Brand;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.enums.cust.CustProdType;
import com.pay.frame.common.base.enums.cust.CustSource;
import com.pay.frame.common.base.enums.cust.CustStatus;
import com.pay.frame.common.base.enums.cust.CustType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 商户信息
 * 
 * @version 2021年08月01日 下午
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
public class CustomerRsp implements Serializable{
	private static final long serialVersionUID = 8428438402546258687L;

	private Long id;
    private Long optimistic=0L;
    
    /** 状态 */
    private CustStatus status;
    
    /** 商户编号 */
    private String customerNo;

    /** 所属服务商编号 */
    private String agentNo;

    /** 全称 */
    private String fullName;

    /** 简称 */
    private String shortName;
    private String jhPrintName;

    /** 地区组织机构 */
    private String organCode;

    /** 手机号码 */
    private String phoneNo;

    /** 地址 */
    private String address;

    /** 来源 */
    private CustSource source;

    /** 商户类型 */
    private CustType custType;
    
    /** 产品类型 */
    private CustProdType prodType;
    
    /** 行业类别 mcc */
    private String mcc;
    
    /** 可绑定终端数量 */
    private int posCount;

    /** 创建时间 */
    private Date createTime;

    /** 开通时间 */
    private Date openTime;
    private String thirdCustNo;
    private String custGrade;
    /** 激活时间 */
    private Date actTime;
    /** 激活活动编号 */
    private String actMid;
    /** 聚合商户编号 */
	private String jhCustNo;

    /** 品牌 */
    private Brand brand;
    private Origin origin;
    private String markRateLabel;

    /** 升级后对应的服务商编号 */
    private String upgradeAgentNo;

    /** 邮箱 */
    private String email;
    /**
     * 交易截止时间
     */
    private Date transExpireTime;
    /** 渠道手机号 */
    private String channelPhoneNo;

    /**
     * 真实电话后4位
     */
    private String realPhone;

    /**
     * 惠商-商户编号
     */
    private String hsCustNo;
    
    /**
     * 	额度状态
     */
    private String quotaStatus;

    /**
     * 	M0关联服务商编号
     */
    private String m0AgentNo;

    private String userNo;

    /**
     * 行业名称
     */
    private String mccName;

    /**
     * 所属地区全编码-分隔
     */
    private String organAddrCode;

    /**
     * 所属地区全名称-分隔
     */
    private String organAddrName;

    private String incrFailReason;

    private String thirdActiveNo;
}